/usr/bin/c++ -fPIC  -std=c++11 -MMD -MP -g  -shared -Wl,-soname,libWT.Tester.API.WT4XXWrapper.so -o ../lib/libWT.Tester.API.WT4XXWrapper.so CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/InstrumentHandle.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/InstrumentHandle_V2.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/MISC.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/VSA.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/VSG.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/CryptologyWT4xx.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/Pac.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/WaveGenerator.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/WaveGenerator_V2.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/WaveGenerator_11BE.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/WT4XXWrapper.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/PrintParam.cpp.o CMakeFiles/WT.Tester.API.WT4XXWrapper.dir/parson.cpp.o   -L/home/<USER>/code/WT328_1/api/build/lib  -Wl,-rpath,/home/<USER>/code/WT328_1/api/build/lib -lpthread -lm ../lib/libWT.Tester.API.IOControl.so ../lib/libWT.Tester.API.PNFileProcess.so ../lib/libWT.Tester.API.Common.so -lmat -lmx ../lib/libWT.Tester.API.MAC.Encryption.so -lpthread -lm 
