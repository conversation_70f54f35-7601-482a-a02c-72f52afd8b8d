#include "listmod_sequence.h"
#include "alg/alg_3gpp_apidef.h"
//#include "types.h"
#include "wtlog.h"
#include "scpi_3gpp_base.h"

void Sequence::ClearSeg()
{
    m_SeqType = SEQUENCETX;
    m_MaxSegSize = 0;
    SeqStat = SEQUENCEOFF;
    m_TrigerOffset = 0;
    //m_LowNam.clear();
    m_Repet = 1;

    m_Seg.clear();
}

int Sequence::GetSeqRealSegNum()
{
    int count = 0;
    for (auto &seg : m_Seg)
    {
        if (seg.get() == nullptr)
        {
            return 0;
        }

        if (seg.get()->TxFlag)
        {
            count += seg.get()->tx_seg.SegTimeParam.Repeat;
        }
        else
        {
            count += seg.get()->rx_seg.SegTimeParam.Repeat;
        }
    }

    return count;
}

void Sequence::SetSeqMaxSize(int Size)
{
    m_MaxSegSize = Size;
    m_Seg.resize(m_MaxSegSize);

    for(auto &Iter:m_Seg)
    {
        Iter.reset();
    }
}

std::vector<std::unique_ptr<Segment>>::iterator Sequence::GetSegBeginIter()
{
    return m_Seg.begin();
}

std::vector<std::unique_ptr<Segment>>::iterator Sequence::GetSegEndIter()
{
    return m_Seg.end();
}

void Sequence::ResetSeg(std::unique_ptr<Segment> &Seg, int SegNo)
{
    if (Seg->TxFlag == true)
    {
        //初始化采样参数
        Seg->tx_seg.vsaParam.Freq = 2412 * MHz_API;
        Seg->tx_seg.vsaParam.Freq2 = 0.0;
        Seg->tx_seg.vsaParam.FreqOffset = 0.0;
        Seg->tx_seg.vsaParam.VsaUnitMask[0] = 0;
        Seg->tx_seg.vsaParam.MaxPower[0] = 30.0;
        Seg->tx_seg.vsaParam.RfPort[0] = WT_PORT_RF1;
        Seg->tx_seg.vsaParam.ExtPathLoss[0] = 0.0;
        Seg->tx_seg.vsaParam.ExtPathLoss2[0] = 0.0;
        Seg->tx_seg.vsaParam.ValidNum = 1;
        Seg->tx_seg.vsaParam.Demode = WT_DEMOD_CW;
        if (SegNo == 0)
        {
            Seg->tx_seg.vsaParam.TrigType = WT_TRIG_TYPE_IF_API;
        }
        else
        {
            Seg->tx_seg.vsaParam.TrigType = WT_TRIG_TYPE_FREE_RUN_API;
        }
        Seg->tx_seg.vsaParam.TrigLevel = -31.0;
        Seg->tx_seg.vsaParam.TrigTimeout = 0.2;
        Seg->tx_seg.vsaParam.SamplingFreq = MAX_SMAPLE_RATE_API;
        Seg->tx_seg.vsaParam.SmpTime = 2 * Ms;
        Seg->tx_seg.vsaParam.TrigPretime = 0 * Us;
        Seg->tx_seg.vsaParam.MaxIFGGap = 0.1;
        Seg->tx_seg.vsaParam.TimeoutWaiting = 4.0;

        //初始化trig参数
        Seg->tx_seg.vsaTrigParam.Edge = WT_TRIG_DEGE_POSITIVE_API;
        Seg->tx_seg.vsaTrigParam.GapTime = 100e-6;
        Seg->tx_seg.vsaTrigParam.GenTriggerType = VSA_GEN_MEASTART_DUREND_TRIGGER;
        Seg->tx_seg.vsaTrigParam.FrameTime = 0;
        Seg->tx_seg.SegTigComParam.TriggerTimeout = 10;
        Seg->tx_seg.SegTigComParam.OutTriggerValidTime = 0;
        Seg->tx_seg.SegTigComParam.VsgSeqTrigRepeatRum = 0;

        //初始化分析参数
        Seg->tx_seg.vsaAlzParam.Reset();

        //初始化时间参数
        Seg->tx_seg.SegTimeParam.Duration = 0.005;
        Seg->tx_seg.SegTimeParam.Meaoffset = 0;
        Seg->tx_seg.SegTimeParam.Meadura = 0.001;
        Seg->tx_seg.SegTimeParam.Repeat = 1;
        Seg->tx_seg.SegTimeParam.Repeat = 1;
    }
    else
    {
        //初始化vsg参数
        memset(&Seg->rx_seg.vsgParam, 0, sizeof(VsgParameter));
        Seg->rx_seg.vsgParam.Freq = 2412 * MHz_API;
        Seg->rx_seg.vsgParam.Freq2 = 0.0;
        Seg->rx_seg.vsgParam.FreqOffset = 0.0;
        Seg->rx_seg.vsgParam.VsgUnitMask[0] = 0;
        Seg->rx_seg.vsgParam.Power[0] = -10.0;
        Seg->rx_seg.vsgParam.RfPort[0] = WT_PORT_RF1;
        Seg->rx_seg.vsgParam.ExtPathLoss[0] = 0.0;
        Seg->rx_seg.vsgParam.ExtPathLoss2[0] = 0.0;
        Seg->rx_seg.vsgParam.ValidNum = 1;
        Seg->rx_seg.vsgParam.SamplingFreq = UNKNOWN_SAMPLE_RATE;
        Seg->rx_seg.vsgParam.TimeoutWaiting = 4.0;

        //初始化wave参数
        memset(&Seg->rx_seg.waveParam, 0, sizeof(VsgPattern));
        if (m_LowNam.length() == 0)
        {
            Seg->rx_seg.waveParam.WaveType = SIG_CW_SIN0;
        }
        else
        {
            Seg->rx_seg.waveParam.WaveType = SIG_USERFILE;
            strcpy(Seg->rx_seg.waveParam.WaveName, m_LowNam.c_str());
        }

        //初始化时间参数
        Seg->rx_seg.SegTimeParam.Duration = 0.005;
        Seg->rx_seg.SegTimeParam.Meaoffset = 0.0002;
        Seg->rx_seg.SegTimeParam.Meadura = 0;
        Seg->rx_seg.SegTimeParam.Repeat = 1;
        Seg->rx_seg.waveParam.Repeat = 1;
        Seg->rx_seg.waveParam.Extend = 0;
        Seg->rx_seg.vsgSyncParam = 0;

        //初始化trig参数
        Seg->rx_seg.SegTigComParam.TriggerTimeout = 10;
        Seg->rx_seg.SegTigComParam.OutTriggerValidTime = 0;
        Seg->rx_seg.SegTigComParam.VsgSeqTrigRepeatRum = 0;
    }
}

void Sequence::CheckListSeqSegNo(int SegNo)
{
    int Size = m_Seg.size();

    if (Size <= SegNo)
    {
        m_MaxSegSize = SegNo + 1;
        for (; Size < m_MaxSegSize; Size++)
        {
            m_Seg.push_back(nullptr);
        }
    }
}

void Sequence::SetListSeqFreq(SEQUENCETYPE Type, int SegNo, double Freq)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.Freq = Freq;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgParam.Freq = Freq;
    }
}

void Sequence::SetListSeqFreqAll(SEQUENCETYPE Type, double Freq)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.Freq = Freq;
        }
        else
        {
            Iter->rx_seg.vsgParam.Freq = Freq;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqPower(SEQUENCETYPE Type, int SegNo, double Power)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        m_Seg[SegNo]->rx_seg.waveParam.WaveType = SIG_CW_SIN0;
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.MaxPower[0] = Power;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgParam.Power[0] = Power;
    }
}

void Sequence::SetListSeqPowerAll(SEQUENCETYPE Type, double Power)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.MaxPower[0] = Power;
        }
        else
        {
            Iter->rx_seg.vsgParam.Power[0] = Power;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqPort(SEQUENCETYPE Type, int SegNo, double Port)
{
    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.RfPort[0] = Port;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgParam.RfPort[0] = Port;
    }
}

void Sequence::SetListSeqPortAll(SEQUENCETYPE Type, double Port)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.RfPort[0] = Port;
        }
        else
        {
            Iter->rx_seg.vsgParam.RfPort[0] = Port;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqSync(SEQUENCETYPE Type, int SegNo, int Sync)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
       /*
        if(Sync)
        {
            m_Seg[SegNo]->tx_seg.vsaParam.TrigType = 2;
        }
        else
        {
            m_Seg[SegNo]->tx_seg.vsaParam.TrigType = 0;
        }*/

    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgSyncParam = (Sync == 1) ? 3 : 0; //如果第0个seg需要vsa同步，则传给fpga的是enable triger
    }
}

void Sequence::SetListSeqSyncAll(SEQUENCETYPE Type, int Sync)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            /*if(Sync)
            {
                Iter->tx_seg.vsaParam.TrigType = 2;
            }
            else
            {
                Iter->tx_seg.vsaParam.TrigType = 0;
            }*/
        }
        else
        {
            Iter->rx_seg.vsgSyncParam = Sync;
        }
        SegNo++;
    }
}

void Sequence::SetListRxSeqIncre(SEQUENCETYPE Type, int Incre)
{
    int SegNo = 0;
    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            /*if(Sync)
            {
                Iter->tx_seg.vsaParam.TrigType = 2;
            }
            else
            {
                Iter->tx_seg.vsaParam.TrigType = 0;
            }*/
        }
        else
        {
            if (SegNo != 0)
            {

                Iter->rx_seg.vsgSyncParam = (Incre == 1) ? 4 : 0; //如果非第0个seg需要vsa同步，则传给fpga的是incre triger
            }
        }
        SegNo++;
    }

}

void Sequence::SetListSeqSampleRate(SEQUENCETYPE Type, int SegNo, double SampleRate)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.SamplingFreq = SampleRate;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgParam.SamplingFreq = SampleRate;
    }
}

void Sequence::SetListSeqSampleRateAll(SEQUENCETYPE Type, double SampleRate)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.SamplingFreq = SampleRate;
        }
        else
        {
            Iter->rx_seg.vsgParam.SamplingFreq = SampleRate;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqExtGain(SEQUENCETYPE Type, int SegNo, double ExtGain)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.ExtPathLoss[0] = ExtGain;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.vsgParam.ExtPathLoss[0] = ExtGain;
    }
}

void Sequence::SetListSeqExtGainAll(SEQUENCETYPE Type, double ExtGain)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.ExtPathLoss[0] = ExtGain;
        }
        else
        {
            Iter->rx_seg.vsgParam.ExtPathLoss[0] = ExtGain;
        }
        SegNo++;
    }
}

void Sequence::SetListTxSeqTriggerType(SEQUENCETYPE Type, int SegNo, int TrigType)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.TrigType = TrigType;
    }
    else
    {
        ;
    }
}

void Sequence::SetListTxSeqTriggerTypeAll(SEQUENCETYPE Type, int TrigType)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.TrigType = TrigType;
        }
        else
        {
            ;
        }
        SegNo++;
    }
}

void Sequence::SetListTxSeqTriggerLevel(SEQUENCETYPE Type, int SegNo, double TrigLevel)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.TrigLevel = TrigLevel;
    }
    else
    {
        ;
    }
}

void Sequence::SetListTxSeqTriggerLevelAll(SEQUENCETYPE Type, double TrigLevel)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.TrigLevel = TrigLevel;
        }
        else
        {
            ;
        }
        SegNo++;
    }
}

void Sequence::SetListTxSeqTriggerGaptime(SEQUENCETYPE Type, int SegNo, double TrigGap)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaTrigParam.GapTime = TrigGap;
    }
    else
    {
        ;
    }
}

void Sequence::SetListTxSeqTriggerGaptimeAll(SEQUENCETYPE Type, double TrigGap)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaTrigParam.GapTime = TrigGap;
        }
        else
        {
            ;
        }
        SegNo++;
    }
}

void Sequence::SetListTxSeqTriggerFrametime(SEQUENCETYPE Type, int SegNo, double TrigFrame)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaTrigParam.FrameTime = TrigFrame;
    }
    else
    {
        ;
    }
}

void Sequence::SetListTxSeqTriggerFrametimeAll(SEQUENCETYPE Type, double TrigFrame)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaTrigParam.FrameTime = TrigFrame;
        }
        else
        {
            ;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqTriggerTimeout(SEQUENCETYPE Type, int SegNo, double TriggerTimeout)
{
     CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.TrigTimeout = TriggerTimeout;
        m_Seg[SegNo]->tx_seg.SegTigComParam.TriggerTimeout = TriggerTimeout;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.SegTigComParam.TriggerTimeout = TriggerTimeout;;
    }
}

void Sequence::SetListSeqTriggerTimeoutAll(SEQUENCETYPE Type, double TriggerTimeout)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.SegTigComParam.TriggerTimeout = TriggerTimeout;
        }
        else
        {
            Iter->rx_seg.SegTigComParam.TriggerTimeout = TriggerTimeout;;
        }
        SegNo++;
    }
}

void Sequence::SetListTxSeqGenTriggerType(SEQUENCETYPE Type, int SegNo, int GenTriggerType)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaTrigParam.GenTriggerType = GenTriggerType;
    }
    else
    {
        ;
    }
}

void Sequence::SetListTxSeqGenTriggerTypeAll(SEQUENCETYPE Type, int GenTriggerType)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaTrigParam.GenTriggerType = GenTriggerType;
        }
        else
        {
            ;
        }
        SegNo++;
    }
}

void Sequence::SetListRxSeqWave(int SegNo, std::string &LowName)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        m_Seg[SegNo]->TxFlag = false;
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == false)
    {
        if (LowName.length() == 0)
        {
            m_Seg[SegNo]->rx_seg.waveParam.WaveType = SIG_CW_SIN0;
        }
        else
        {
            m_Seg[SegNo]->rx_seg.waveParam.WaveType = SIG_USERFILE;
            strcpy(m_Seg[SegNo]->rx_seg.waveParam.WaveName, LowName.c_str());
        }
    }
}

void Sequence::SetListRxSeqWaveAll(std::string &LowName)
{
    int SegNo = 0;

    m_LowNam = LowName;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            Iter->TxFlag = false;
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == false)
        {
            if (LowName.length() == 0)
            {
                Iter->rx_seg.waveParam.WaveType = SIG_CW_SIN0;
                strcpy(Iter->rx_seg.waveParam.WaveName, LowName.c_str());
            }
            else
            {
                Iter->rx_seg.waveParam.WaveType = SIG_USERFILE;
                strcpy(Iter->rx_seg.waveParam.WaveName, LowName.c_str());
            }
         }
        SegNo++;
    }

}

void Sequence::SetListSeqDuration(SEQUENCETYPE Type, int SegNo, double Duration)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.SegTimeParam.Duration = Duration;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.SegTimeParam.Duration = Duration;
    }
}

void Sequence::SetListSeqDurationAll(SEQUENCETYPE Type, double Duration)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }
        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.SegTimeParam.Duration = Duration;
        }
        else
        {
            Iter->rx_seg.SegTimeParam.Duration = Duration;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqMeaoffset(SEQUENCETYPE Type, int SegNo, double Meaoffset)
{
    CheckListSeqSegNo(SegNo);

    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListSeqMeaoffset Type" << Type << "SegNo " << SegNo << "Meaoffset " << Meaoffset << std::endl;

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.SegTimeParam.Meaoffset = Meaoffset;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.SegTimeParam.Meaoffset = Meaoffset;
    }
}

void Sequence::SetListSeqMeaoffsetAll(SEQUENCETYPE Type, double Meaoffset)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.SegTimeParam.Meaoffset = Meaoffset;
        }
        else
        {
            Iter->rx_seg.SegTimeParam.Meaoffset = Meaoffset;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqMeaDur(SEQUENCETYPE Type, int SegNo, double Meadura)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.SegTimeParam.Meadura = Meadura;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.SegTimeParam.Meadura = Meadura;
    }
}

void Sequence::SetListSeqMeaDurAll(SEQUENCETYPE Type, double Meadura)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.SegTimeParam.Meadura = Meadura;
        }
        else
        {
            Iter->rx_seg.SegTimeParam.Meadura = Meadura;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqRepeat(SEQUENCETYPE Type, int SegNo, double Repeat)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.SegTimeParam.Repeat = Repeat;
    }
    else
    {
        m_Seg[SegNo]->rx_seg.SegTimeParam.Repeat = Repeat;
    }
}

void Sequence::SetListSeqRepeatAll(SEQUENCETYPE Type, double Repeat)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.SegTimeParam.Repeat = Repeat;
        }
        else
        {
            Iter->rx_seg.SegTimeParam.Repeat = Repeat;
        }
        SegNo++;
    }
}

void Sequence::SetListSeqAnalDemod(SEQUENCETYPE Type, int SegNo, int AnalDemod)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.Demode = AnalDemod;
        if (IsAlg3GPPStandardType(AnalDemod))
        {
            m_Seg[SegNo]->tx_seg.vsaAlzParam.Reset_AlzParam(m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP, AnalDemod);
        }
        else if (AnalDemod == WT_DEMOD_LRWPAN_FSK || AnalDemod == WT_DEMOD_LRWPAN_OQPSK || AnalDemod == WT_DEMOD_LRWPAN_OFDM)
        {
            m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParamWiSun.Demode = AnalDemod;
        }
        else
        {
            m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParamWifi.Demode = AnalDemod;
        }
    }
    else
    {
        return;
    }
}

void Sequence::SetListSeqAnalDemodAll(SEQUENCETYPE Type, int AnalDemod)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            if (Type == SEQUENCETX)
            {
                Iter->TxFlag = true;
            }
            else
            {
                Iter->TxFlag = false;
            }
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == true)
        {
            Iter->tx_seg.vsaParam.Demode = AnalDemod;
            if (IsAlg3GPPStandardType(AnalDemod))
            {
                Iter->tx_seg.vsaAlzParam.Reset_AlzParam(Iter->tx_seg.vsaAlzParam.analyzeParam3GPP, AnalDemod);
            }
            else if (AnalDemod == WT_DEMOD_LRWPAN_FSK || AnalDemod == WT_DEMOD_LRWPAN_OQPSK || AnalDemod == WT_DEMOD_LRWPAN_OFDM)
            {
                Iter->tx_seg.vsaAlzParam.analyzeParamWiSun.Demode = AnalDemod;
            }
            else
            {
                Iter->tx_seg.vsaAlzParam.analyzeParamWifi.Demode = AnalDemod;
            }
        }
        else
        {
            ;
        }
        SegNo++;
    }
}

void Sequence::DeleteListSeqSeg(int SegNo)
{
    std::vector<std::unique_ptr<Segment>>::iterator Iter = m_Seg.begin() + SegNo;
    m_Seg.erase(Iter);
}

void Sequence::DeleteListSeqSegAll()
{
    m_SeqType = SEQUENCETX;
    m_MaxSegSize = 0;
    SeqStat = SEQUENCEOFF;
    m_Seg.clear();
}

void Sequence::SetListLteTxSeqParam(SEQUENCETYPE Type, int SegNo, double *LteTxPara)
{
    const double LeftOff = 300 * Us;
    const double Subframe = 1.0 * Ms;

    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        m_Seg[SegNo]->tx_seg.SegTimeParam.Duration = LteTxPara[0] * Subframe;
        m_Seg[SegNo]->tx_seg.vsaParam.MaxPower[0] = LteTxPara[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Duplexing = LteTxPara[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.rf_band[0] = LteTxPara[3];
        m_Seg[SegNo]->tx_seg.vsaParam.Freq = LteTxPara[4];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.ChannelBW = LteTxPara[5];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.CyclicPrefix = LteTxPara[6];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.ChanType = LteTxPara[7];
        m_Seg[SegNo]->tx_seg.vsaParam.TrigType = LteTxPara[8];
        m_Seg[SegNo]->tx_seg.SegTimeParam.Meaoffset = (LteTxPara[9] * Subframe >= LeftOff) ? LteTxPara[9] * Subframe - LeftOff : LteTxPara[9] * Subframe;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqParam: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    LteTxPara[0]:" << LteTxPara[0] << "Duration" << m_Seg[SegNo]->tx_seg.SegTimeParam.Duration << std::endl
                                                             << "    LteTxPara[1]:" << LteTxPara[1] << "MaxPower" << m_Seg[SegNo]->tx_seg.vsaParam.MaxPower[0] << std::endl
                                                             << "    LteTxPara[2] Duplexing:" << LteTxPara[2] << std::endl
                                                             << "    LteTxPara[3] rf_band:" << LteTxPara[3] << std::endl
                                                             << "    LteTxPara[4] Freq:" << LteTxPara[4] << std::endl
                                                             << "    LteTxPara[5] ChannelBW:" << LteTxPara[5] << std::endl
                                                             << "    LteTxPara[6] CyclicPrefix:" << LteTxPara[6] << std::endl
                                                             << "    LteTxPara[7] ChanType:" << LteTxPara[7] << std::endl
                                                             << "    LteTxPara[8] TrigType:" << LteTxPara[8] << std::endl
                                                             << "    LteTxPara[9] Meaoffset:" << LteTxPara[9] << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqTdd(SEQUENCETYPE Type, int SegNo, int UpDownLink, int SpecSubframe)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.ULDLConfig = UpDownLink;
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.SpecialSubfrmConfig = SpecSubframe;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqTdd: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    ULDLConfig:" << UpDownLink << std::endl
                                                             << "    SpecialSubfrmConfig:" << SpecSubframe << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqRbAllocation(SEQUENCETYPE Type, int SegNo, int Auto, int NoRb, int Offset)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.RBAutoMode = Auto;
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.RBNum = NoRb;
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.RBOffset = Offset;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqRbAllocation: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    RBAutoMode:" << Auto << std::endl
                                                             << "    RBNum:" << NoRb << std::endl
                                                             << "    RBOffset:" << Offset << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqModulation(SEQUENCETYPE Type, int SegNo, int *LteTxModulation)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ModStatNum = LteTxModulation[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ModEnable = LteTxModulation[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.EvmEnable = LteTxModulation[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.MErrEnable = LteTxModulation[3];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PErrEnable = LteTxModulation[4];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.IBEEnable = LteTxModulation[5];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ESFlatEnable = LteTxModulation[6];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Modulate = LteTxModulation[7];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqModulation: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    ModStatNum:" << LteTxModulation[0] << std::endl
                                                             << "    ModEnable:" << LteTxModulation[1] << std::endl
                                                             << "    EvmEnable:" << LteTxModulation[2] << std::endl
                                                             << "    MErrEnable:" << LteTxModulation[3] << std::endl
                                                             << "    PErrEnable:" << LteTxModulation[4] << std::endl
                                                             << "    IBEEnable:" << LteTxModulation[5] << std::endl
                                                             << "    ESFlatEnable:" << LteTxModulation[6] << std::endl
                                                             << "    Modulate:" << LteTxModulation[7] << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqSemask(SEQUENCETYPE Type, int SegNo, int *LteTxSemask)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.SEMStatNum = LteTxSemask[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.SpectEnable = LteTxSemask[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.OBWEnable = LteTxSemask[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.SEMEnable = LteTxSemask[3];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqSemask: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    SEMStatNum:" << LteTxSemask[0] << std::endl
                                                             << "    SpectEnable:" << LteTxSemask[1] << std::endl
                                                             << "    OBWEnable:" << LteTxSemask[2] << std::endl
                                                             << "    SEMEnable:" << LteTxSemask[3] << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqAclr(SEQUENCETYPE Type, int SegNo, int *LteTxAclr)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ACLRStatNum = LteTxAclr[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.ACLREnable = LteTxAclr[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.UTRA1Enable = LteTxAclr[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.UTRA2Enable = LteTxAclr[3];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.EUTRAEnable = LteTxAclr[4];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqAclr: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    ACLRStatNum:" << LteTxAclr[0] << std::endl
                                                             << "    ACLREnable:" << LteTxAclr[1] << std::endl
                                                             << "    UTRA1Enable:" << LteTxAclr[2] << std::endl
                                                             << "    UTRA2Enable:" << LteTxAclr[3] << std::endl
                                                             << "    EUTRAEnable:" << LteTxAclr[4] << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqPmonitor(SEQUENCETYPE Type, int SegNo, int PowMonEnab)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PMonitorEnable = PowMonEnab;
    }
    else
    {
        return;
    }
}

void Sequence::SetListLteTxSeqPower(SEQUENCETYPE Type, int SegNo, int *LteTxPower)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_4G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PowerStatNum = LteTxPower[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.LTELIST.Measure.PowerEnable = LteTxPower[1];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListLteTxSeqAclr: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    PowerStatNum:" << LteTxPower[0] << std::endl
                                                             << "    PowerEnable:" << LteTxPower[1] << std::endl
                                                             << "}" << std::endl;
    }
    else
    {
        return;
    }
}

void Sequence::SetListNr5gTxSeqRedCap(SEQUENCETYPE Type, int *Nr5gTxRedCap)
{
    for (auto &Seg : m_Seg)
    {
        if (Seg.get() == nullptr)
        {
            continue;
        }
        if (Seg->TxFlag == true && Seg->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
        {
            Seg->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.RedcapEnable = Nr5gTxRedCap[0];
            if (Nr5gTxRedCap[0] == 1)
            {
                Seg->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Version = Nr5gTxRedCap[1];
            }
            else
            {
                Seg->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Version = 17;
            }
            WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqRedCap: {" << std::endl
                                                             << "    RedCap:" << Nr5gTxRedCap[0] << std::endl
                                                             << "    Version:" << Nr5gTxRedCap[1] << std::endl
                                                             << "}" << std::endl;
        }
    }
}

void Sequence::SetListNr5gTxSeqParam(SEQUENCETYPE Type, int SegNo, double *Nr5gTxPara)
{
    const double LeftOff = 200 * Us;
    const double Subframe = 1.0 * Ms;

    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.SegTimeParam.Duration = Nr5gTxPara[0] * Subframe;
        m_Seg[SegNo]->tx_seg.vsaParam.MaxPower[0] = Nr5gTxPara[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Duplexing = Nr5gTxPara[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.rf_band[0] = Nr5gTxPara[3];
        m_Seg[SegNo]->tx_seg.vsaParam.TrigType = Nr5gTxPara[4];
        m_Seg[SegNo]->tx_seg.SegTimeParam.Meaoffset = (Nr5gTxPara[5] * Subframe >= LeftOff) ? (Nr5gTxPara[5] * Subframe - LeftOff) : (Nr5gTxPara[5] * Subframe);
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqParam: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    Nr5gTxPara[0]:" << Nr5gTxPara[0] << "Duration" << m_Seg[SegNo]->tx_seg.SegTimeParam.Duration << std::endl
                                                             << "    Nr5gTxPara[1]:" << Nr5gTxPara[1] << "MaxPower" << m_Seg[SegNo]->tx_seg.vsaParam.MaxPower[0] << std::endl
                                                             << "    Nr5gTxPara[2] Duplexing:" << Nr5gTxPara[2] << std::endl
                                                             << "    Nr5gTxPara[3] rf_band:" << Nr5gTxPara[3] << std::endl
                                                             << "    Nr5gTxPara[4] TrigType:" << Nr5gTxPara[4] << std::endl
                                                             << "    Nr5gTxPara[5] Meaoffset:" << Nr5gTxPara[5] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqFrequency(SEQUENCETYPE Type, int SegNo, int64_t Nr5gTxFrequency)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaParam.Freq = Nr5gTxFrequency;
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqFrequency: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    Frequency:" << Nr5gTxFrequency << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqBandwidth(SEQUENCETYPE Type, int SegNo, int *Nr5gTxBandwidth)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.ChannelBW = Nr5gTxBandwidth[0];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqBandwidth: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    Bandwidth:" << Nr5gTxBandwidth[0] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqPhyCellID(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPhyCellID)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.PhyCellID = Nr5gTxPhyCellID[0];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqPhyCellID: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    PhyCellID:" << Nr5gTxPhyCellID[0] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqDmrsTypeAPos(SEQUENCETYPE Type, int SegNo, int *Nr5gTxDmrsTypeAPos)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.DmrsTypeAPos = Nr5gTxDmrsTypeAPos[0];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqDmrsTypeAPos: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    DmrsTypeAPos:" << Nr5gTxDmrsTypeAPos[0] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqUseScsSpacing(SEQUENCETYPE Type, int SegNo, int *Nr5gTxUseScsSpacing)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.UseSCSpacing = Nr5gTxUseScsSpacing[0];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqUseScsSpacing: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    UseSCSpacing:" << Nr5gTxUseScsSpacing[0] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqTxBWidthOffset(SEQUENCETYPE Type, int SegNo, int *Nr5gTxTxBWidthOffset)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.OffsetToCarrier = Nr5gTxTxBWidthOffset[0];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqTxBWidthOffset: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    TxBWidthOffset:" << Nr5gTxTxBWidthOffset[0] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqBwpPart(SEQUENCETYPE Type, int SegNo, int *Nr5gTxBwpPart)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.SCSpacing = Nr5gTxBwpPart[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.CyclicPrefix = Nr5gTxBwpPart[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.BwpRBNum = Nr5gTxBwpPart[3];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.BwpRBOffset = Nr5gTxBwpPart[4];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqBwpPart: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    SCSpacing:" << Nr5gTxBwpPart[1] << std::endl
                                                             << "    CyclicPrefix:" << Nr5gTxBwpPart[2] << std::endl
                                                             << "    BwpRBNum:" << Nr5gTxBwpPart[3] << std::endl
                                                             << "    BwpRBOffset:" << Nr5gTxBwpPart[4] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqBwpPartPuschDmrsTypeA(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPuschDmrsTypeA)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.ConfigType[0] = Nr5gTxPuschDmrsTypeA[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.AdditionalPos[0] = Nr5gTxPuschDmrsTypeA[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.MaxLength[0] = Nr5gTxPuschDmrsTypeA[2];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqBwpPartPuschDmrsTypeA: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    ConfigType:" << Nr5gTxPuschDmrsTypeA[0] << std::endl
                                                             << "    AdditionalPos:" << Nr5gTxPuschDmrsTypeA[1] << std::endl
                                                             << "    MaxLength:" << Nr5gTxPuschDmrsTypeA[2] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqBwpPartPuschDmrsTypeB(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPuschDmrsTypeB)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.ConfigType[1] = Nr5gTxPuschDmrsTypeB[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.AdditionalPos[1] = Nr5gTxPuschDmrsTypeB[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.MaxLength[1] = Nr5gTxPuschDmrsTypeB[2];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqBwpPartPuschDmrsTypeB: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    ConfigType:" << Nr5gTxPuschDmrsTypeB[0] << std::endl
                                                             << "    AdditionalPos:" << Nr5gTxPuschDmrsTypeB[1] << std::endl
                                                             << "    MaxLength:" << Nr5gTxPuschDmrsTypeB[2] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqBwpPartPuschDmrsTypeADftPrecoding(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPuschDmrsTypeADftPrecoding)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.TransformPrecoder = Nr5gTxPuschDmrsTypeADftPrecoding[0];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqBwpPartPuschDmrsTypeADftPrecoding: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    TransformPrecoder:" << Nr5gTxPuschDmrsTypeADftPrecoding[0] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqBwpPartPusch(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPusch)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.MappingType = Nr5gTxPusch[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.SymNum = Nr5gTxPusch[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.SymbOffset = Nr5gTxPusch[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.RBAutoMode = Nr5gTxPusch[3];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.RBNum = Nr5gTxPusch[4];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.RBOffset = Nr5gTxPusch[5];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Modulate = Nr5gTxPusch[6];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqBwpPartPusch: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    MappingType:" << Nr5gTxPusch[0] << std::endl
                                                             << "    SymNum:" << Nr5gTxPusch[1] << std::endl
                                                             << "    SymbOffset:" << Nr5gTxPusch[2] << std::endl
                                                             << "    RBAutoMode:" << Nr5gTxPusch[3] << std::endl
                                                             << "    RBNum:" << Nr5gTxPusch[4] << std::endl
                                                             << "    RBOffset:" << Nr5gTxPusch[5] << std::endl
                                                             << "    Modulate:" << Nr5gTxPusch[6] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqBwpPartPuschAdditional(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPuschAdditional)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.DmrsSymbLen = Nr5gTxPuschAdditional[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.DmrsAntPort = Nr5gTxPuschAdditional[1];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqBwpPartPuschAdditional: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    DmrsSymbLen:" << Nr5gTxPuschAdditional[0] << std::endl
                                                             << "    DmrsAntPort:" << Nr5gTxPuschAdditional[1] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqBwpPartPuschSGeneneration(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPuschSGeneneration)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.DmrsInitType = Nr5gTxPuschSGeneneration[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.DmrsID = Nr5gTxPuschSGeneneration[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.NSCID = Nr5gTxPuschSGeneneration[2];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqBwpPartPuschSGeneneration: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    DmrsInitType:" << Nr5gTxPuschSGeneneration[0] << std::endl
                                                             << "    DmrsID:" << Nr5gTxPuschSGeneneration[1] << std::endl
                                                             << "    NSCID:" << Nr5gTxPuschSGeneneration[2] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqModulation(SEQUENCETYPE Type, int SegNo, int *Nr5gTxModulation)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.ModStatNum = Nr5gTxModulation[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.ModEnable = Nr5gTxModulation[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.EvmEnable = Nr5gTxModulation[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.MErrEnable = Nr5gTxModulation[3];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.PErrEnable = Nr5gTxModulation[4];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.IBEEnable = Nr5gTxModulation[5];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.ESFlatEnable = Nr5gTxModulation[6];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqModulation: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    ModStatNum:" << Nr5gTxModulation[0] << std::endl
                                                             << "    ModEnable:" << Nr5gTxModulation[1] << std::endl
                                                             << "    EvmEnable:" << Nr5gTxModulation[2] << std::endl
                                                             << "    MErrEnable:" << Nr5gTxModulation[3] << std::endl
                                                             << "    PErrEnable:" << Nr5gTxModulation[4] << std::endl
                                                             << "    IBEEnable:" << Nr5gTxModulation[5] << std::endl
                                                             << "    ESFlatEnable:" << Nr5gTxModulation[6] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqSemask(SEQUENCETYPE Type, int SegNo, int *Nr5gTxSemask)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.SEMStatNum = Nr5gTxSemask[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.SpectEnable = Nr5gTxSemask[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.OBWEnable = Nr5gTxSemask[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.SEMEnable = Nr5gTxSemask[3];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqSemask: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    SEMStatNum:" << Nr5gTxSemask[0] << std::endl
                                                             << "    SpectEnable:" << Nr5gTxSemask[1] << std::endl
                                                             << "    OBWEnable:" << Nr5gTxSemask[2] << std::endl
                                                             << "    SEMEnable:" << Nr5gTxSemask[3] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqAclr(SEQUENCETYPE Type, int SegNo, int *Nr5gTxAclr)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.ACLRStatNum = Nr5gTxAclr[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.ACLREnable = Nr5gTxAclr[1];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.UTRA1Enable = Nr5gTxAclr[2];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.UTRA2Enable = Nr5gTxAclr[3];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.NREnable = Nr5gTxAclr[4];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqAclr: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    ACLRStatNum:" << Nr5gTxAclr[0] << std::endl
                                                             << "    ACLREnable:" << Nr5gTxAclr[1] << std::endl
                                                             << "    UTRA1Enable:" << Nr5gTxAclr[2] << std::endl
                                                             << "    UTRA2Enable:" << Nr5gTxAclr[3] << std::endl
                                                             << "    NREnable:" << Nr5gTxAclr[4] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListNr5gTxSeqPmonitor(SEQUENCETYPE Type, int SegNo, int PowMonEnab)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.PMonitorEnable = PowMonEnab;
    }
}

void Sequence::SetListNr5gTxSeqPower(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPower)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        if (Type == SEQUENCETX)
        {
            m_Seg[SegNo]->TxFlag = true;
        }
        else
        {
            m_Seg[SegNo]->TxFlag = false;
        }
        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == true && m_Seg[SegNo]->tx_seg.vsaParam.Demode == ALG_3GPP_STD_5G)
    {
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.TxPwrStatNum = Nr5gTxPower[0];
        m_Seg[SegNo]->tx_seg.vsaAlzParam.analyzeParam3GPP.NR5GLIST.Measure.TxPwrEnable = Nr5gTxPower[1];
        WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "SetListNr5gTxSeqPower: {" << std::endl
                                                             << "    SegNo:" << SegNo << std::endl
                                                             << "    TxPwrStatNum:" << Nr5gTxPower[0] << std::endl
                                                             << "    TxPwrEnable:" << Nr5gTxPower[1] << std::endl
                                                             << "}" << std::endl;
    }
}

void Sequence::SetListSeqRepet(int Repet)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            Iter->TxFlag = false;
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == false)
        {
            Iter->rx_seg.SegTigComParam.VsgSeqTrigRepeatRum = Repet;
        }
        SegNo++;
    }

    m_Repet = Repet;

}

void Sequence::SetListSeqEnableFlag(int EnableFlag)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            Iter->TxFlag = false;
            ResetSeg(Iter, SegNo);
        }

        SegNo++;
    }

    m_EnableFlag = EnableFlag;
}

void Sequence::SetListRxSeqCellMod(int CellMod)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            Iter->TxFlag = false;
            ResetSeg(Iter, SegNo);
        }

        SegNo++;
    }

    m_CellMod = CellMod;
}


void Sequence::SetListSeqIncrementFlag(int IncrementFlag)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            Iter->TxFlag = false;
            ResetSeg(Iter, SegNo);
        }

        SegNo++;
    }

    m_IncrementFlag = IncrementFlag;
}

void Sequence::SetListTxSeqTrigerOffset(double TrigerOffset)
{
    m_TrigerOffset = TrigerOffset;
}

int Sequence::GetListSeqRepet()
{
    return m_Repet;
}

int Sequence::GetListSeqEnableFlag()
{
    return m_EnableFlag;
}

int Sequence::GetListSeqCellMod()
{
    return m_CellMod;
}


int Sequence::GetListSeqIncrementFlag()
{
    return m_IncrementFlag;
}

double Sequence::GetListTxSeqTrigerOffset()
{
    return m_TrigerOffset;
}

void Sequence::SetListRxSeqArbRepet(int SegNo, int Repet)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        m_Seg[SegNo]->TxFlag = false;

        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == false)
    {
        m_Seg[SegNo]->rx_seg.waveParam.Repeat = Repet;
    }
}

void Sequence::SetListRxSeqArbRepetAll(int Repet)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            Iter->TxFlag = false;
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == false)
        {
            Iter->rx_seg.waveParam.Repeat = Repet;
        }
        SegNo++;
    }
}

void Sequence::SetListRxSeqArbExtend(int SegNo, int Extend)
{
    CheckListSeqSegNo(SegNo);

    if (m_Seg[SegNo].get() == nullptr)
    {
        m_Seg[SegNo].reset(new Segment());
        m_Seg[SegNo]->TxFlag = false;

        ResetSeg(m_Seg[SegNo], SegNo);
    }

    if (m_Seg[SegNo]->TxFlag == false)
    {
        m_Seg[SegNo]->rx_seg.waveParam.Extend = Extend;
    }
}

void Sequence::SetListRxSeqArbExtendAll(int Extend)
{
    int SegNo = 0;

    for(auto &Iter:m_Seg)
    {
        if (Iter.get() == nullptr)
        {
            Iter.reset(new Segment());
            Iter->TxFlag = false;
            ResetSeg(Iter, SegNo);
        }

        if (Iter->TxFlag == false)
        {
            Iter->rx_seg.waveParam.Extend = Extend;
        }
        SegNo++;
    }
}

void ListSeq::SetTxListModEnable()
{
    m_Seq[0].ClearSeg();

    m_SeqScen = LISTSCEN_NONECOMB;
    m_ListEnable = true;
}

void ListSeq::SetTxListModDisable()
{
    m_Seq[0].ClearSeg();

    m_SeqScen = LISTSCEN_NONECOMB;
    m_ListEnable = false;
}

void ListSeq::SetRxListModEnable()
{
    m_Seq[1].ClearSeg();

    m_SeqScen = LISTSCEN_NONECOMB;
    m_ListEnable = true;

    m_Seq[1].SetListRxSeqCellMod(0);
}

void ListSeq::SetRxListModDisable()
{
    m_Seq[1].ClearSeg();

    m_SeqScen = LISTSCEN_NONECOMB;
    m_ListEnable = false;
}


int ListSeq::GetListModSeqSize(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
        return m_Seq[0].GetSeqSegNum();
    }
    else
    {
        return m_Seq[1].GetSeqSegNum();
    }
}

int ListSeq::GetListModRealSeqSize(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
        // printf("ListSeq::GetListModRealSeqSize(SEQUENCETYPE Type)\n");
        return m_Seq[0].GetSeqRealSegNum();
    }
    else
    {
        return m_Seq[1].GetSeqRealSegNum();
    }
}

void ListSeq::ClearListSeq()
{
    int i;

    for (i = 0; i < 2; i++)
    {
        m_Seq[i].ClearSeg();
    }
}

std::vector<std::unique_ptr<Segment>>::iterator ListSeq::GetSegBeginIter(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
       return m_Seq[0].GetSegBeginIter();
    }
    else
    {
       return m_Seq[1].GetSegBeginIter();
    }
}

std::vector<std::unique_ptr<Segment>>::iterator ListSeq::GetSegEndIter(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
       return m_Seq[0].GetSegEndIter();
    }
    else
    {
       return m_Seq[1].GetSegEndIter();
    }
}

void ListSeq::SetListSeqStat(SEQUENCETYPE Type, SEQUENCESTATE Stat)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
        m_Seq[0].SetSeqStat(Stat);
    }
    else
    {
        m_Seq[1].SetSeqStat(Stat);
    }
}
SEQUENCESTATE ListSeq::GetListSeqStat(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
        return m_Seq[0].GetSeqStat();
    }
    else
    {
        return m_Seq[1].GetSeqStat();
    }
}

void ListSeq::SetListSeqMaxSize(SEQUENCETYPE Type, int Sizes)
{
    if (Type == SEQUENCETX || Type == SEQUENCETXRX)
    {
        m_Seq[0].SetSeqMaxSize(Sizes);
    }
    else
    {
        m_Seq[1].SetSeqMaxSize(Sizes);
    }
}

void ListSeq::SetListSeqFreq(SEQUENCETYPE Type, int SegNo, double Freq)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqFreq(Type, SegNo, Freq);
    }
    else
    {
        m_Seq[1].SetListSeqFreq(Type, SegNo, Freq);
    }
}

void ListSeq::SetListSeqFreqAll(SEQUENCETYPE Type, double Freq)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqFreqAll(Type,  Freq);
    }
    else
    {
        m_Seq[1].SetListSeqFreqAll(Type, Freq);
    }
}

void ListSeq::SetListSeqPower(SEQUENCETYPE Type, int SegNo, double Power)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqPower(Type, SegNo, Power);
    }
    else
    {
        m_Seq[1].SetListSeqPower(Type, SegNo, Power);
    }
}

void ListSeq::SetListSeqPowerAll(SEQUENCETYPE Type, double Power)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqPowerAll(Type,  Power);
    }
    else
    {
        m_Seq[1].SetListSeqPowerAll(Type, Power);
    }
}

void ListSeq::SetListSeqPort(SEQUENCETYPE Type, int SegNo, double Port)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqPort(Type, SegNo, Port);
    }
    else
    {
        m_Seq[1].SetListSeqPort(Type, SegNo, Port);
    }
}

void ListSeq::SetListSeqPortAll(SEQUENCETYPE Type, double Port)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqPortAll(Type,  Port);
    }
    else
    {
        m_Seq[1].SetListSeqPortAll(Type, Port);
    }
}

void ListSeq::SetListSeqSync(SEQUENCETYPE Type, int SegNo, int Sync)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqSync(Type, SegNo, Sync);
    }
    else
    {
        m_Seq[1].SetListSeqSync(Type, SegNo, Sync);
    }
}

void ListSeq::SetListSeqSyncAll(SEQUENCETYPE Type, int Sync)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqSyncAll(Type, Sync);
    }
    else
    {
        m_Seq[1].SetListSeqSyncAll(Type, Sync);
    }
}

void ListSeq::SetListRxSeqIncre(SEQUENCETYPE Type, int Incre)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListRxSeqIncre(Type, Incre);
    }
    else
    {
        m_Seq[1].SetListRxSeqIncre(Type, Incre);
    }
}


void ListSeq::SetListSeqSampleRate(SEQUENCETYPE Type, int SegNo, double SampleRate)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqSampleRate(Type, SegNo, SampleRate);
    }
    else
    {
        m_Seq[1].SetListSeqSampleRate(Type, SegNo, SampleRate);
    }
}

void ListSeq::SetListSeqSampleRateAll(SEQUENCETYPE Type, double SampleRate)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqSampleRateAll(Type,  SampleRate);
    }
    else
    {
        m_Seq[1].SetListSeqSampleRateAll(Type, SampleRate);
    }
}

void ListSeq::SetListSeqExtGain(SEQUENCETYPE Type, int SegNo, double ExtGain)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqExtGain(Type, SegNo, ExtGain);
    }
    else
    {
        m_Seq[1].SetListSeqExtGain(Type, SegNo, ExtGain);
    }
}

void ListSeq::SetListSeqExtGainAll(SEQUENCETYPE Type, double ExtGain)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqExtGainAll(Type, ExtGain);
    }
    else
    {
        m_Seq[1].SetListSeqExtGainAll(Type, ExtGain);
    }
}

void ListSeq::SetListTxSeqTriggerType(SEQUENCETYPE Type, int SegNo, int TrigType)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerType(Type, SegNo, TrigType);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerType(Type, SegNo, TrigType);
    }
}

void ListSeq::SetListTxSeqTriggerTypeAll(SEQUENCETYPE Type, int TrigType)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerTypeAll(Type, TrigType);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerTypeAll(Type, TrigType);
    }
}

void ListSeq::SetListTxSeqTriggerLevel(SEQUENCETYPE Type, int SegNo, double TrigLevel)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerLevel(Type, SegNo, TrigLevel);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerLevel(Type, SegNo, TrigLevel);
    }
}

void ListSeq::SetListTxSeqTriggerLevelAll(SEQUENCETYPE Type, double TrigLevel)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerLevelAll(Type, TrigLevel);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerLevelAll(Type, TrigLevel);
    }
}

void ListSeq::SetListTxSeqTriggerGaptime(SEQUENCETYPE Type, int SegNo, double TrigGap)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerGaptime(Type, SegNo, TrigGap);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerGaptime(Type, SegNo, TrigGap);
    }
}


void ListSeq::SetListTxSeqTriggerGaptimeAll(SEQUENCETYPE Type, double TrigGap)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerGaptimeAll(Type, TrigGap);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerGaptimeAll(Type, TrigGap);
    }
}

void ListSeq::SetListTxSeqTriggerFrametime(SEQUENCETYPE Type, int SegNo, double TrigFrame)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerFrametime(Type, SegNo, TrigFrame);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerFrametime(Type, SegNo, TrigFrame);
    }
}


void ListSeq::SetListTxSeqTriggerFrametimeAll(SEQUENCETYPE Type, double TrigFrame)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTriggerFrametimeAll(Type, TrigFrame);
    }
    else
    {
        m_Seq[1].SetListTxSeqTriggerFrametimeAll(Type, TrigFrame);
    }
}

void ListSeq::SetListSeqTriggerTimeout(SEQUENCETYPE Type, int SegNo, double TriggerTimeout)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqTriggerTimeout(Type, SegNo, TriggerTimeout);
    }
    else
    {
        m_Seq[1].SetListSeqTriggerTimeout(Type, SegNo, TriggerTimeout);
    }
}


void ListSeq::SetListSeqTriggerTimeoutAll(SEQUENCETYPE Type, double TriggerTimeout)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqTriggerTimeoutAll(Type, TriggerTimeout);
    }
    else
    {
        m_Seq[1].SetListSeqTriggerTimeoutAll(Type, TriggerTimeout);
    }
}

void ListSeq::SetListTxSeqGenTriggerType(SEQUENCETYPE Type, int SegNo, int GenTriggerType)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqGenTriggerType(Type, SegNo, GenTriggerType);
    }
    else
    {
        m_Seq[1].SetListTxSeqGenTriggerType(Type, SegNo, GenTriggerType);
    }
}

void ListSeq::SetListTxSeqGenTriggerTypeAll(SEQUENCETYPE Type, int GenTriggerType)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqGenTriggerTypeAll(Type, GenTriggerType);
    }
    else
    {
        m_Seq[1].SetListTxSeqGenTriggerTypeAll(Type, GenTriggerType);
    }
}

void ListSeq::SetListRxSeqWave(int SegNo, std::string &LowName)
{
    if (m_SeqScen == LISTSCEN_COMB)
    {
        m_Seq[0].SetListRxSeqWave(SegNo, LowName);
    }
    else
    {
        m_Seq[1].SetListRxSeqWave(SegNo, LowName);
    }
}

void ListSeq::SetListRxSeqWaveAll(std::string &LowName)
{
    if (m_SeqScen == LISTSCEN_COMB)
    {
        m_Seq[0].SetListRxSeqWaveAll(LowName);
    }
    else
    {
        m_Seq[1].SetListRxSeqWaveAll(LowName);
    }
}

void ListSeq::SetListSeqDuration(SEQUENCETYPE Type, int SegNo, double Duration)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqDuration(Type, SegNo, Duration);
    }
    else
    {
        m_Seq[1].SetListSeqDuration(Type, SegNo, Duration);
    }
}

void ListSeq::SetListSeqDurationAll(SEQUENCETYPE Type, double Duration)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqDurationAll(Type, Duration);
    }
    else
    {
        m_Seq[1].SetListSeqDurationAll(Type, Duration);
    }
}

void ListSeq::SetListSeqMeaoffset(SEQUENCETYPE Type, int SegNo, double Meaoffset)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqMeaoffset(Type, SegNo, Meaoffset);
    }
    else
    {
        m_Seq[1].SetListSeqMeaoffset(Type, SegNo, Meaoffset);
    }
}

void ListSeq::SetListSeqMeaoffsetAll(SEQUENCETYPE Type, double Meaoffset)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqMeaoffsetAll(Type, Meaoffset);
    }
    else
    {
        m_Seq[1].SetListSeqMeaoffsetAll(Type, Meaoffset);
    }
}
void ListSeq::SetListSeqMeaDur(SEQUENCETYPE Type, int SegNo, double Meadura)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqMeaDur(Type, SegNo, Meadura);
    }
    else
    {
        m_Seq[1].SetListSeqMeaDur(Type, SegNo, Meadura);
    }
}

void ListSeq::SetListSeqMeaDurAll(SEQUENCETYPE Type, double Meadura)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqMeaDurAll(Type, Meadura);
    }
    else
    {
        m_Seq[1].SetListSeqMeaDurAll(Type, Meadura);
    }
}

void ListSeq::SetListSeqRepeat(SEQUENCETYPE Type, int SegNo, double Repeat)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqRepeat(Type, SegNo, Repeat);
    }
    else
    {
        m_Seq[1].SetListSeqRepeat(Type, SegNo, Repeat);
    }
}

void ListSeq::SetListSeqRepeatAll(SEQUENCETYPE Type, double Repeat)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqRepeatAll(Type, Repeat);
    }
    else
    {
        m_Seq[1].SetListSeqRepeatAll(Type, Repeat);
    }
}

void ListSeq::SetListSeqAnalDemod(SEQUENCETYPE Type, int SegNo, int AnalDemod)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqAnalDemod(Type, SegNo, AnalDemod);
    }
    else
    {
        m_Seq[1].SetListSeqAnalDemod(Type, SegNo, AnalDemod);
    }
}

void ListSeq::SetListSeqAnalDemodAll(SEQUENCETYPE Type, int AnalDemod)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqAnalDemodAll(Type, AnalDemod);
    }
    else
    {
        m_Seq[1].SetListSeqAnalDemodAll(Type, AnalDemod);
    }
}

void ListSeq::DeleteListSeqSeg(SEQUENCETYPE Type, int SegNo)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].DeleteListSeqSeg(SegNo);
    }
    else
    {
        m_Seq[1].DeleteListSeqSeg(SegNo);
    }
}

void ListSeq::DeleteListSeqSegAll(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].DeleteListSeqSegAll();
    }
    else
    {
        m_Seq[1].DeleteListSeqSegAll();
    }
}

void ListSeq::SetListLteTxSeqParam(SEQUENCETYPE Type, int SegNo, double *LteTxPara)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqParam(Type, SegNo, LteTxPara);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqParam(Type, SegNo, LteTxPara);
    }
}

void ListSeq::SetListLteTxSeqTdd(SEQUENCETYPE Type, int SegNo, int UpDownLink, int SpecSubframe)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqTdd(Type, SegNo, UpDownLink, SpecSubframe);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqTdd(Type, SegNo, UpDownLink, SpecSubframe);
    }
}

void ListSeq::SetListLteTxSeqRbAllocation(SEQUENCETYPE Type, int SegNo, int Auto, int NoRb, int Offset)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqRbAllocation(Type, SegNo, Auto, NoRb, Offset);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqRbAllocation(Type, SegNo, Auto, NoRb, Offset);
    }
}

void ListSeq::SetListLteTxSeqModulation(SEQUENCETYPE Type, int SegNo, int *LteTxModulation)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqModulation(Type, SegNo, LteTxModulation);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqModulation(Type, SegNo, LteTxModulation);
    }
}

void ListSeq::SetListLteTxSeqSemask(SEQUENCETYPE Type, int SegNo, int *LteTxSemask)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqSemask(Type, SegNo, LteTxSemask);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqSemask(Type, SegNo, LteTxSemask);
    }
}

void ListSeq::SetListLteTxSeqAclr(SEQUENCETYPE Type, int SegNo, int *LteTxAclr)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqAclr(Type, SegNo, LteTxAclr);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqAclr(Type, SegNo, LteTxAclr);
    }
}

void ListSeq::SetListLteTxSeqPmonitor(SEQUENCETYPE Type, int SegNo, int PowMonEnab)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqPmonitor(Type, SegNo, PowMonEnab);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqPmonitor(Type, SegNo, PowMonEnab);
    }
}

void ListSeq::SetListLteTxSeqPower(SEQUENCETYPE Type, int SegNo, int *LteTxPower)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListLteTxSeqPower(Type, SegNo, LteTxPower);
    }
    else
    {
        m_Seq[1].SetListLteTxSeqPower(Type, SegNo, LteTxPower);
    }
}

void ListSeq::SetListNr5gTxSeqRedCap(SEQUENCETYPE Type, int *Nr5gTxRedCap)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqRedCap(Type, Nr5gTxRedCap);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqRedCap(Type, Nr5gTxRedCap);
    }
}

void ListSeq::SetListNr5gTxSeqParam(SEQUENCETYPE Type, int SegNo, double *Nr5gTxPara)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqParam(Type, SegNo, Nr5gTxPara);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqParam(Type, SegNo, Nr5gTxPara);
    }
}

void ListSeq::SetListNr5gTxSeqFrequency(SEQUENCETYPE Type, int SegNo, int64_t Nr5gTxFrequency)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqFrequency(Type, SegNo, Nr5gTxFrequency);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqFrequency(Type, SegNo, Nr5gTxFrequency);
    }
}

void ListSeq::SetListNr5gTxSeqBandwidth(SEQUENCETYPE Type, int SegNo, int *Nr5gTxBandwidth)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqBandwidth(Type, SegNo, Nr5gTxBandwidth);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqBandwidth(Type, SegNo, Nr5gTxBandwidth);
    }
}

void ListSeq::SetListNr5gTxSeqPhyCellID(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPhyCellID)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqPhyCellID(Type, SegNo, Nr5gTxPhyCellID);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqPhyCellID(Type, SegNo, Nr5gTxPhyCellID);
    }
}

void ListSeq::SetListNr5gTxSeqDmrsTypeAPos(SEQUENCETYPE Type, int SegNo, int *Nr5gTxDmrsTypeAPos)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqDmrsTypeAPos(Type, SegNo, Nr5gTxDmrsTypeAPos);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqDmrsTypeAPos(Type, SegNo, Nr5gTxDmrsTypeAPos);
    }
}

void ListSeq::SetListNr5gTxSeqUseScsSpacing(SEQUENCETYPE Type, int SegNo, int *Nr5gTxScsSpacing)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqUseScsSpacing(Type, SegNo, Nr5gTxScsSpacing);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqUseScsSpacing(Type, SegNo, Nr5gTxScsSpacing);
    }
}

void ListSeq::SetListNr5gTxSeqTxBWidthOffset(SEQUENCETYPE Type, int SegNo, int *Nr5gTxTxBWidthOffset)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqTxBWidthOffset(Type, SegNo, Nr5gTxTxBWidthOffset);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqTxBWidthOffset(Type, SegNo, Nr5gTxTxBWidthOffset);
    }
}

void ListSeq::SetListNr5gTxSeqBwpPart(SEQUENCETYPE Type, int SegNo, int *Nr5gTxBwpPart)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqBwpPart(Type, SegNo, Nr5gTxBwpPart);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqBwpPart(Type, SegNo, Nr5gTxBwpPart);
    }
}

void ListSeq::SetListNr5gTxSeqBwpPartPuschDmrsTypeA(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPuschDmrsTypeA)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqBwpPartPuschDmrsTypeA(Type, SegNo, Nr5gTxPuschDmrsTypeA);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqBwpPartPuschDmrsTypeA(Type, SegNo, Nr5gTxPuschDmrsTypeA);
    }
}

void ListSeq::SetListNr5gTxSeqBwpPartPuschDmrsTypeB(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPuschDmrsTypeB)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqBwpPartPuschDmrsTypeB(Type, SegNo, Nr5gTxPuschDmrsTypeB);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqBwpPartPuschDmrsTypeB(Type, SegNo, Nr5gTxPuschDmrsTypeB);
    }
}

void ListSeq::SetListNr5gTxSeqBwpPartPuschDmrsTypeADftPrecoding(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPuschDmrsTypeADftPrecoding)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqBwpPartPuschDmrsTypeADftPrecoding(Type, SegNo, Nr5gTxPuschDmrsTypeADftPrecoding);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqBwpPartPuschDmrsTypeADftPrecoding(Type, SegNo, Nr5gTxPuschDmrsTypeADftPrecoding);
    }
}

void ListSeq::SetListNr5gTxSeqBwpPartPusch(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPusch)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqBwpPartPusch(Type, SegNo, Nr5gTxPusch);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqBwpPartPusch(Type, SegNo, Nr5gTxPusch);
    }
}

void ListSeq::SetListNr5gTxSeqBwpPartPuschAdditional(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPuschAdditional)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqBwpPartPuschAdditional(Type, SegNo, Nr5gTxPuschAdditional);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqBwpPartPuschAdditional(Type, SegNo, Nr5gTxPuschAdditional);
    }
}

void ListSeq::SetListNr5gTxSeqBwpPartPuschSGeneneration(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPuschSGeneneration)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqBwpPartPuschSGeneneration(Type, SegNo, Nr5gTxPuschSGeneneration);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqBwpPartPuschSGeneneration(Type, SegNo, Nr5gTxPuschSGeneneration);
    }
}

void ListSeq::SetListNr5gTxSeqModulation(SEQUENCETYPE Type, int SegNo, int *Nr5gTxModulation)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqModulation(Type, SegNo, Nr5gTxModulation);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqModulation(Type, SegNo, Nr5gTxModulation);
    }
}

void ListSeq::SetListNr5gTxSeqSemask(SEQUENCETYPE Type, int SegNo, int *Nr5gTxSemask)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqSemask(Type, SegNo, Nr5gTxSemask);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqSemask(Type, SegNo, Nr5gTxSemask);
    }
}

void ListSeq::SetListNr5gTxSeqAclr(SEQUENCETYPE Type, int SegNo, int *Nr5gTxAclr)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqAclr(Type, SegNo, Nr5gTxAclr);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqAclr(Type, SegNo, Nr5gTxAclr);
    }
}

void ListSeq::SetListNr5gTxSeqPmonitor(SEQUENCETYPE Type, int SegNo, int PowMonEnab)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqPmonitor(Type, SegNo, PowMonEnab);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqPmonitor(Type, SegNo, PowMonEnab);
    }
}

void ListSeq::SetListNr5gTxSeqPower(SEQUENCETYPE Type, int SegNo, int *Nr5gTxPower)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListNr5gTxSeqPower(Type, SegNo, Nr5gTxPower);
    }
    else
    {
        m_Seq[1].SetListNr5gTxSeqPower(Type, SegNo, Nr5gTxPower);
    }
}

void ListSeq::SetListSeqRepet(SEQUENCETYPE Type, int Repet)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqRepet(Repet);
    }
    else
    {
        m_Seq[1].SetListSeqRepet(Repet);
    }
}

void ListSeq::SetListSeqEnableFlag(SEQUENCETYPE Type, int EnableFlag)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqEnableFlag(EnableFlag);
    }
    else
    {
        m_Seq[1].SetListSeqEnableFlag(EnableFlag);
    }
}

void ListSeq::SetListRxSeqCellMod(SEQUENCETYPE Type, int CellMod)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListRxSeqCellMod(CellMod);
    }
    else
    {
        m_Seq[1].SetListRxSeqCellMod(CellMod);
    }
}


void ListSeq::SetListSeqIncrementFlag(SEQUENCETYPE Type, int IncrementFlag)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListSeqIncrementFlag(IncrementFlag);
    }
    else
    {
        m_Seq[1].SetListSeqIncrementFlag(IncrementFlag);
    }
}

void ListSeq::SetListTxSeqTrigerOffset(SEQUENCETYPE Type, double TrigerOffset)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        m_Seq[0].SetListTxSeqTrigerOffset(TrigerOffset);
    }
    else
    {
        m_Seq[1].SetListTxSeqTrigerOffset(TrigerOffset);
    }
}

int ListSeq::GetListSeqRepet(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        return m_Seq[0].GetListSeqRepet();
    }
    else
    {
        return m_Seq[1].GetListSeqRepet();
    }
}

int ListSeq::GetListSeqEnableFlag(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        return m_Seq[0].GetListSeqEnableFlag();
    }
    else
    {
        return m_Seq[1].GetListSeqEnableFlag();
    }
}

int ListSeq::GetListSeqCellMod(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        return m_Seq[0].GetListSeqCellMod();
    }
    else
    {
        return m_Seq[1].GetListSeqCellMod();
    }
}


int ListSeq::GetListSeqIncrementFlag(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        return m_Seq[0].GetListSeqIncrementFlag();
    }
    else
    {
        return m_Seq[1].GetListSeqIncrementFlag();
    }
}


double ListSeq::GetListTxSeqTrigerOffset(SEQUENCETYPE Type)
{
    if (Type == SEQUENCETX || (Type == SEQUENCERX && m_SeqScen == LISTSCEN_COMB))
    {
        return m_Seq[0].GetListTxSeqTrigerOffset();
    }
    else
    {
        return m_Seq[1].GetListTxSeqTrigerOffset();
    }
}


void ListSeq::SetListRxSeqArbRepet(int SegNo, int Repet)
{
    m_Seq[1].SetListRxSeqArbRepet(SegNo, Repet);
}

void ListSeq::SetListRxSeqArbRepetAll(int Repet)
{
    m_Seq[1].SetListRxSeqArbRepetAll(Repet);
}

void ListSeq::SetListRxSeqArbExtend(int SegNo, int Extend)
{
    m_Seq[1].SetListRxSeqArbExtend(SegNo, Extend);
}

void ListSeq::SetListRxSeqArbExtendAll(int Extend)
{
    m_Seq[1].SetListRxSeqArbExtendAll(Extend);
}
