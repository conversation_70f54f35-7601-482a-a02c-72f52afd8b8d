#!/bin/bash

dir=$(dirname $(readlink -f $0))/bin
targetdir=$1

echo "$(dirname $(readlink -f $0))/pack.sh $@"

if [[ $@ == *DEVTYPE=WT428* ]]; then
	DevType="WT428"
elif [[ $@ == *DEVTYPE=WT448* ]]; then
    DevType="WT448"
elif [[ $@ == *DEVTYPE=WT418* || $@ == *DEVTYPE=WT328CE* ]]; then
    DevType="WT328CE"
else 
    echo "error dev type! exit..."
    exit -1
fi

#Lib file~
cp ../extlib/lib/libfftw3.so $dir/libfftw3.so.3
cp ../extlib/lib/*.so $dir/
cp ../extlib/lib/*.so.* $dir/
cp ../extlib/lib/*.so.* $dir/
rm $dir/libfftw3.so -f

cp scpi/lib/libSCPI.so $dir/
cp scpi/lib/matlab/libgomp.so.1 $dir/
cp scpi/lib/wireshark/* $dir/
#eth file~
if [ ! -d "$dir/eth" ]; then
    mkdir -p $dir/eth
else
    rm -rf $dir/eth/*
fi

if [ "$DevType" != "WT448" ]; then
cp $(pwd)/config/eth/arp.sh $dir/eth/ -rf
cp $(pwd)/config/eth/arpsend $dir/eth/ -rf
cp $(pwd)/config/eth/nat.sh $dir/eth/ -rf
cp $(pwd)/config/eth/updatearp.sh $dir/eth/ -rf
cp $(pwd)/config/eth/network.sh $dir/eth/ -rf
cp $(pwd)/config/eth/eth_rename.sh $dir/eth/ -rf
fi

#wave file~
if [ ! -e "$dir/wave" ]; then
    mkdir $dir/wave
else
    rm -rf $dir/wave/*
fi
mkdir $dir/wave/BT
mkdir $dir/wave/User
mkdir $dir/wave/User/8080
mkdir $dir/wave/Wifi
mkdir $dir/wave/Wifi/8080

#fpga file~
bash $dir/../config/fpga/getfpgafile.sh $dir

#configuration file~
if [ -d "$dir/configuration" ]; then
    rm -rf $dir/configuration/*
fi
cp ../extlib/lib/configuration $dir -rf
rm $dir/configuration/.svn -r
rm $dir/configuration/wt_config.csv -r

#不打包calibration文件
if [ -d "$dir/calibration" ]; then
    rm -rf $dir/calibration/
fi

#不打包lic文件
if [ -d "$dir/lic" ]; then
    rm -rf $dir/lic/
fi

#config file~

cp $(pwd)/config/dev.conf $dir
cp $(pwd)/config/_Net.ini $dir
#cp $(pwd)/config/ethcheck.sh $dir
cp $(pwd)/scpi/config/scpi.conf $dir
cp $(pwd)/scpi/config/moni_cmd_ignor.conf $dir
cp $(pwd)/config/triggerbase.conf $dir
#cp $(pwd)/config/base.conf $dir
cp $(pwd)/config/hwinit.conf $dir
cp $(pwd)/config/gstack_2.sh $dir
cp $(pwd)/config/wave/sin_1M_240M_1cycle.low $dir/wave/
if [ "$DevType" == "WT328CE" ]; then
    cp $(pwd)/config/wtcmd_WT418.json $dir/wtcmd.json
else
    cp $(pwd)/config/wtcmd.json $dir
fi


cp $(pwd)/config/config.txt $dir
cp $(pwd)/config/SystemMonitor.sh $dir
cp $(pwd)/config/gstack_2.sh $dir
cp $(pwd)/config/algreset.conf $dir

if [[ $@ = *DISABLE_SECURE=1* ]]; then
    echo "...disable file secure"
else
    cp $(pwd)/config/crypto_file_list.conf $dir
fi

#不打包base.conf文件
if [ -d "$dir/base.conf " ]; then
    rm -f $dir/base.conf
fi

#deb
if [ -d "$dir/deb" ]; then
    rm -rf $dir/deb/*
fi
cp $(pwd)/config/deb $dir -rf


#需要修改系统配置文件时才打开使用
#cp $(pwd)/config/syscfg $dir -rf
#rm $dir/syscfg/.svn -r


#find $dir -name .svn | xargs rm -rf        #this one can't deal the dir name with ' 'space
find $dir -name .svn -print0 | xargs -0 rm -rf
chmod 777 -R *

cd bin
date +"%Y-%m-%d %H:%M:%S" > pack.md5
md5sum WT-Manager WT-Server WT-Link WT-Scpi-Server libgeneral.so libAlgVsa.so libAlgVsg.so libwt-calibration.so >> pack.md5
cd ..

date_s=`date +%Y%m%d`

if [[ $DevType == *WT3* ]]; then
	DevType=WT328CE
elif [[ $DevType != *WT4* ]]; then
	DevType=WT448
fi

if [ ! -d "$dir/meter" ]; then
    mkdir $dir/meter
else
    rm -rf $dir/meter/*
fi

if [ "$DevType" == "WT448" ]; then
    VersionDef="WT448_FW_VERSION"
    cp $(pwd)/config/pcicheck_update.sh $dir
elif [ "$DevType" == "WT428" ]; then
    VersionDef="WT428_FW_VERSION"
	cp $(pwd)/config/pcicheck_update_wt428.sh $dir/pcicheck_update.sh
elif [ "$DevType" == "WT328CE" ]; then
    VersionDef="WT418_FW_VERSION"
	cp $(pwd)/config/pcicheck_wt418.sh $dir/pcicheck_update.sh
fi

#pdf file
ExportFile="EXPORT_FILE=1"
if [[ $@ = *EXPORT_FILE=0* ]]; then
    ExportFile="EXPORT_FILE=0"
fi
bash $dir/../../meter/meter_pack.sh $DevType $ExportFile

SvnRev=$(svn info --username softwarepackage --password s5695816923 --show-item revision $targetdir)
echo "SvnRev=$SvnRev"
SvnAddress=$(svn info --username softwarepackage --password s5695816923 --show-item url $targetdir $targetdir)
echo "SvnAddress=$SvnAddress"
FwVersion=$(grep -m 1 "$VersionDef" $(pwd)/general/version.cpp | grep -oP '\d+\.\d+\.\d+\.\d+')
echo "FwVersion=$FwVersion"
VsaVersion=$(grep -m 1 -aoP '\d+\.\d+\.\d+\.\d+' $dir/libAlgVsaVersion.so)
VsgVersion=$(grep -m 1 -aoP '\d+\.\d+\.\d+\.\d+' $dir/libAlgVsgVersion.so)
echo VsaVersion = $VsaVersion > $dir/algversion.txt
echo VsgVersion = $VsgVersion >> $dir/algversion.txt

#生成fwversion.txt文件,保存当前固件的版本信息,以备升级判断
echo "FwVersion = $FwVersion" > $dir/fwversion.txt
echo "DevType = $DevType" >> $dir/fwversion.txt
echo "SvnAddress = $SvnAddress" >> $dir/fwversion.txt
echo "SvnRev = $SvnRev" >> $dir/fwversion.txt
echo "snc_cal_data_version = 2" >> $dir/fwversion.txt
echo "wt428_support_switch_vd = 1" >> $dir/fwversion.txt
echo "wt428C_support_V2_VD = 1" >> $dir/fwversion.txt
if [[ $@ == *SISO=1* ]]; then
	echo "SISO_VERSION = 1" >> $dir/fwversion.txt
else
	echo "SISO_VERSION = 0" >> $dir/fwversion.txt
fi
	
tgzName=$targetdir/${DevType}
if [ "$DevType" == "WT428" ]; then
	if [[ $@ = *DISABLE_SECURE=1* ]]; then
		tgzName+=_no_secure
	else
		tgzName+=_secure
	fi

    if [[ $@ == *SISO=1* ]]; then
        tgzName+="_siso"
    else
        tgzName+="_mimo"
    fi
fi

rm -f ${tgzName}_fw*.tgz
tgzName+=_fw${FwVersion}_vsa${VsaVersion}_vsg${VsgVersion}_${date_s}.tgz

echo "tgzName = $tgzName"
tar -cvzf $tgzName bin run.sh

if [[ $@ = *GENERATOR=1* ]]; then
    echo "fw_package $tgzName"
    $targetdir/fw_package $tgzName
fi

