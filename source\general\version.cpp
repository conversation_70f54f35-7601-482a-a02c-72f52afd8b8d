
#include "version.h"
#include "stdio.h"
#define WT448_FW_VERSION "1.0.0.247"
#define WT428_FW_VERSION "1.1.0.288" 
#define WT418_FW_VERSION "2.8.6.515"

#if WT448_FW
#define FW_VERSION WT448_FW_VERSION;
#elif WT428_FW
#define FW_VERSION WT428_FW_VERSION;
#elif WT418_FW
#define FW_VERSION WT418_FW_VERSION;
#else
#define FW_VERSION WT448_FW_VERSION;
#endif

__attribute__((optimize("O0"))) const char *WTGetBuildDate()
{
    return GetBuildDate();
}

__attribute__((optimize("O0"))) const char *WTGetFwVersion()
{
    return FW_VERSION;
}

bool WTVersion::IsDigitString(const std::string &StrValue)
{
    if (StrValue.empty())
        return false;
    for (size_t i = 0; i < StrValue.size(); ++i)
    {
        if (!iswdigit(StrValue[i]))
            return false;
    }
    return true;
}

bool WTVersion::GetVersion(const std::string &StrVersion)
{
    int Begin = 0, Pos = 0;
    std::vector<std::string> NumList;
    std::string StrSub;
    while ((Pos = StrVersion.find(".", Begin)) != std::string::npos)
    {
        StrSub = StrVersion.substr(Begin, Pos - Begin);
        if (StrSub.empty())
            return false;
        if (!IsDigitString(StrSub))
            return false;
        NumList.push_back(StrSub);
        Begin = Pos + 1;
    }
    if (Begin < (signed)StrVersion.size())
    {
        StrSub = StrVersion.substr(Begin, StrVersion.size() - Begin);
        if (IsDigitString(StrSub))
            NumList.push_back(StrSub);
    }
    if (NumList.size() < 2)
        return false;
    m_MajorNumber = atoi(NumList[0].c_str());
    m_MinorNumber = atoi(NumList[1].c_str());
    if (NumList.size() > 2)
        m_RevisionNumber = atoi(NumList[2].c_str());
    if (NumList.size() > 3)
        m_BuildNumber = atoi(NumList[3].c_str());
    return true;
}

int WTVersion::Compare(const WTVersion &Ver)
{
    // if (m_MajorNumber > Ver.m_MajorNumber)
    //     return 1;
    // if (m_MajorNumber < Ver.m_MajorNumber)
    //     return -1;
    // if (m_MinorNumber > Ver.m_MinorNumber)
    //     return 1;
    // if (m_MinorNumber < Ver.m_MinorNumber)
    //     return -1;
    // if (m_RevisionNumber > Ver.m_RevisionNumber)
    //     return 1;
    // if (m_RevisionNumber < Ver.m_RevisionNumber)
    //     return -1;
    if (m_BuildNumber > Ver.m_BuildNumber)
        return 1;
    if (m_BuildNumber < Ver.m_BuildNumber)
        return -1;
    return 0;
}
