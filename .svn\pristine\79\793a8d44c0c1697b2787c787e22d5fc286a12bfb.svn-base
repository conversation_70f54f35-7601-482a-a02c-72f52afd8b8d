/*
 * scpiutils.cpp
 *
 *  Created on: 2019-3-12
 *      Author: Administrator
 */
//#include "scpi/scpi.h"
//#include "basehead.h"
#include "scpiutils.h"
#include <iostream>
#include <cstdio>
#include "wterror.h"
#include "commonhandler.h"
#include "vsahandler.h"
#include "vsghandler.h"
#include "scpi_signaling_mode.h"
#include "vsa_tf_information.h"
#include "vsa_ofdma_info.h"
#include "vsa_data_info.h"
#include "vsa_vht_mumimo.h"
#include "vsa_psdu.h"
#include "vsa_eht.h"
#include "vsa_11ba.h"
#include "vsa_11az.h"
#include "vsa_11ah.h"
#include "scpi_pac.h"
#include "scpi_wavegenerator.h"
#include "scpi_gen_11ax_mumimo.h"
#include "scpi_gen_11ax_mu.h"
#include "scpi_gen_11ax_su.h"
#include "scpi_gen_11ax_tbmumimo.h"
#include "scpi_gen_tf.h"
#include "scpi_gen_wifi_psdu.h"
#include "scpi_gen_bt.h"
#include "scpi_gen_11ax_er.h"
#include "scpi_gen_11ac_mumimo.h"
#include "scpi_gen_11be_mu.h"
#include "scpi_gen_11be_tb.h"
#include "scpi_gen_11ah_su.h"
#include "scpi_gen_11ah_mu.h"
#include "scpi_gen_channel_mode.h"
#include "scpi_gen_CBF.h"
#include "scpi_internal_cmd.h"
#include "scpi_cal_cmd.h"
#include "scpi_admintool.h"
#include "scpi_digital_IQ.h"
#include "scpi_query.h"
#include "server/scpi_config.h"
#include "server/scpi_conninfo.h"
#include "scpi_monitor.h"
#include "scpi_gen_11ba.h"
#include "scpi_hw_cmd.h"
#include "scpi_gen_11az.h"
#include "scpi_gen_11az_tb.h"
#include "scpi_gen_gle.h"
#include "scpi_3gpp_utils.h"  // 蜂窝LTE SCPI
#include "scpi_gen_wi_sun.h"
#include "scpi_listmod.h"
#include <vector>
#include <string>
#include <thread>
#include <future>
#include "wtlog.h"

using namespace std;

static vector<scpi_command_t> scpi_vector_cmd;
#define scpi_cmd_insert(name) scpi_vector_cmd.insert(std::end(scpi_vector_cmd), std::begin(name), std::end(name))

// units definition IEEE 488.2-1992 tab 7-1
static const scpi_unit_def_t scpi_units_def[] = {

    {"UV", SCPI_UNIT_VOLT, 1e-6},
    {"MV", SCPI_UNIT_VOLT, 1e-3},
    {"V", SCPI_UNIT_VOLT, 1},
    {"KV", SCPI_UNIT_VOLT, 1e3},

    {"UA", SCPI_UNIT_AMPER, 1e-6},
    {"MA", SCPI_UNIT_AMPER, 1e-3},
    {"A", SCPI_UNIT_AMPER, 1},
    {"KA", SCPI_UNIT_AMPER, 1e3},

    {"OHM", SCPI_UNIT_OHM, 1},
    {"KOHM", SCPI_UNIT_OHM, 1e3},
    {"MOHM", SCPI_UNIT_OHM, 1e6},

    {"HZ", SCPI_UNIT_HERTZ, 1},
    {"KHZ", SCPI_UNIT_HERTZ, 1e3},
    {"MHZ", SCPI_UNIT_HERTZ, 1e6},
    {"GHZ", SCPI_UNIT_HERTZ, 1e9},

    {"CEL", SCPI_UNIT_CELSIUS, 1},
    {"DBM", SCPI_UNIT_POWER, 1},
    {"DB", SCPI_UNIT_POWER, 1},

    {"PS", SCPI_UNIT_SECONDS, 1e-12},
    {"NS", SCPI_UNIT_SECONDS, 1e-9},
    {"US", SCPI_UNIT_SECONDS, 1e-6},
    {"MS", SCPI_UNIT_SECONDS, 1e-3},
    {"S", SCPI_UNIT_SECONDS, 1},
    {"MIN", SCPI_UNIT_SECONDS, 60},
    {"HR", SCPI_UNIT_SECONDS, 3600},

    SCPI_UNITS_LIST_END,
};

static const scpi_command_t scpi_system_cmd[] = {
    // IEEE Mandated Commands (SCPI std V1999.0 4.1.1)
    {"*CLS", SCPI_CoreCls, 0, SCPI_SEQUENTIAL},
    {"*ESE", SCPI_CoreEse, 0, SCPI_SEQUENTIAL},
    {"*ESE?", SCPI_CoreEseQ, 0, SCPI_QUERY_CMD},
    {"*ESR?", SCPI_CoreEsrQ, 0, SCPI_QUERY_CMD},
    {"*IDN?", GetDeviceIDN, 0, SCPI_QUERY_CMD},
    {"*OPC", SCPI_CoreOpc, 0, SCPI_SEQUENTIAL},
    {"*OPC?", GetOPC, 0, SCPI_QUERY_CMD},
    {"*RST", SCPI_CoreRst, 0, SCPI_SEQUENTIAL},
    {"*SRE", SCPI_CoreSre, 0, SCPI_SEQUENTIAL},
    {"*SRE?", SCPI_CoreSreQ, 0, SCPI_QUERY_CMD},
    {"*STB?", SCPI_CoreStbQ, 0, SCPI_QUERY_CMD},
    {"*WAI", SetSleep, 0, SCPI_SEQUENTIAL},

    {"SYSTem:GET:DEVice:LICense?", SCPI_GetLicenseInfo, 0, SCPI_QUERY_CMD},
    {"WT:GET:SPECification?", SCPI_GetLicenseInfo, 0, SCPI_QUERY_CMD},
    {"SYSTem:GET:DEVice:LICense:STRing?", SCPI_GetLicenseInfoStr, 0, SCPI_QUERY_CMD},
    // Required SCPI commands (SCPI std V1999.0 4.2.1)
    {"SYSTem:ERRor[:NEXT]?", SCPI_SystemErrorNextQ, 0, SCPI_QUERY_CMD},
    {"SYSTem:ERRor:COUNt?", SCPI_SystemErrorCountQ, 0, SCPI_QUERY_CMD},
    {"SYSTem:VERSion?", SCPI_SystemVersionQ, 0, SCPI_QUERY_CMD},
    {"SYSTem:PRESet", SCPI_StatusPreset, 0, SCPI_SEQUENTIAL}, // 3.1.5.2  Reset

    {"WT:SYSTem:FORCeconnect", SCPI_ForceConnect, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:SUBConnect", SCPI_SubConnect, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:MONIconnect", SCPI_MonitorConnect, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:DIAGconnect", SCPI_DiagnosisConnect, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_query_cmd[] = {
    {"WT:SYSTem:SCAN?", SCPI_QueryScan, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:SUBDev?", SCPI_QuerySubdev, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:CONNinfo?", SCPI_QueryConnInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:SELF:IP:INFO?", SCPI_QuerySelfInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:LICense:STATus?", SCPI_QueryLicInfo, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_system_cmd[] = {
    {"WT:CONFigure:SUBNetinfo", SetSubNet, 0, SCPI_SEQUENTIAL},            //配置子网口
    {"WT:CONFigure:SUBNetinfo?", GetSubNet, 0, SCPI_QUERY_CMD},            //查询子网口
    {"WT:CONFigure:SUBNetlink?", GetSubNetLink, 0, SCPI_QUERY_CMD},        //查询子网口Link状态
    {"WT:CONFigure:TFTP:NETInfo", SetSubNetTftp, 0, SCPI_SEQUENTIAL},      //配置子网口FTFP
    {"WT:CONFigure:TFTP:NETInfo?", GetSubNetTftp, 0, SCPI_QUERY_CMD},      //查询子网口FTFP
    {"WT:CONFigure:SUBNet:AUTOneg", SetSubNetAutoNeg, 0, SCPI_SEQUENTIAL}, //配置仪器子网口网速自动协商开关
    {"WT:CONFigure:SUBNet:AUTOneg?", GetSubNetAutoNeg, 0, SCPI_QUERY_CMD}, //获取仪器子网口网速自动协商开关
    {"WT:QUERy:WAVE?", GetWaveNameList, 0, SCPI_QUERY_CMD},                //查询信号所有信号文件
    {"WT:SYSTem:QUERy:WAVE:LIST?", GetWaveNameList, 0, SCPI_QUERY_CMD},    //查询信号所有信号文件
    {"WT:CONFigure:SUBNet:CONNected:DUT:MAC?", GetMACAddr, 0, SCPI_QUERY_CMD},

    {"WT:REM:CONNect", AddRemoteTester, 0, SCPI_SEQUENTIAL},
    {"WT:REM:CONNect:DELete", DelRemoteTester, 0, SCPI_SEQUENTIAL},
    {"WT:MEMeory:DUMp", DumpMemoryParam, 0, SCPI_SEQUENTIAL},

    // VSA command map
    {"WT:HAVE:WAVE:FILE", CheckWaveFileExist, 0, SCPI_SEQUENTIAL},        //查询是否有指定的信号文件
    {"WT:SYSTem:HAVE:WAVE:FILE?", CheckWaveFileExist, 0, SCPI_QUERY_CMD}, //查询是否有指定的信号文件

    {"WT:DELete:WAVE:FILE", DelWaveFile, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:DELete:WAVE:FILE", DelWaveFile, 0, SCPI_SEQUENTIAL},

    {"WT:SYSTem:QUERy:FILE:MD5:CODE?", QueryfileMD5code, 0, SCPI_QUERY_CMD}, // 查询仪器内部信号文件MD5码
    {"WT:SYSTem:QUERy:FILE:LENGth?", Queryfilelength, 0, SCPI_QUERY_CMD},    // 查询信号文件大小

    {"WT:SYSTem:CONFigure:RUN:MODE?", SCPI_GetTesterRunMode, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:CMD:RESPonse", SCPI_ReponseEnable, 0, SCPI_SEQUENTIAL},                          //是否回应普通SCPI命令
    {"WT:SYSTem:UPload:FILE", SCPI_UpLoadFile, 0, SCPI_SEQUENTIAL},                              //上传文件到仪器内部
    {"WT:SYSTem:UPload:FILE:RELAtive:PATH", SCPI_UpLoadFileRelativePath, 0, SCPI_SEQUENTIAL},    //上传文件到仪器内部
    {"WT:SYSTem:DOWNload:FILE", SCPI_DownLoadFile, 0, SCPI_QUERY_CMD},                           //从仪器下载文件到本地
    {"WT:SYSTem:DOWNload:FILE:RELAtive:PATH", SCPI_DownLoadFileRelativePath, 0, SCPI_QUERY_CMD}, //从仪器下载文件到本地
    {"WT:SYSTem:EXE:CMD", SCPI_ExeCmd, 0, SCPI_QUERY_CMD},                                       //执行shell命令
    {"WT:SYSTem:IP:INFOrmation?", GetTesterIPInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:NAME:INFOrmation?", GetTesterNameInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:ALG:VERsion:INFOrmation?", GetTesterAlgVersionInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:ALG3gpp:VERsion:INFOrmation?", GetTester3GPPAlgVersionInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:CAL:VERsion:INFOrmation?", GetTesterCalVersionInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:METEr:VERsion:INFOrmation?", GetTesterMeterVersionInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:FT4222:INFOrmation?", GetTesterFt4222Info, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:MODUle:COUNt?", GetTesterModuleCnt, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:MODUle:VSA#:INFOrmation?", GetTesterModuleVSA, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:MODUle:VSG#:INFOrmation?", GetTesterModuleVSG, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:MODUle:BP:INFOrmation?", GetTesterModuleBP, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:TIME:INFOrmation?", GetTesterTimeInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:MEMory:SIZE?", GetTesterMemorySize, 0, SCPI_QUERY_CMD},                         //目前仅generater使用，内部使用不对外，获取内存大小

    {"WT:SYSTem:SLAVe:IP:INFOrmation?", GetSlaveTesterIPInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:SLAVe:NAME:INFOrmation?", GetSlaveTesterNameInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:SLAVe:ALG:VERsion:INFOrmation?", GetSlaveTesterAlgVersionInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:SLAVe:MODUle:COUNt?", GetSlaveTesterModuleCnt, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:SLAVe:MODUle:VSA#:INFOrmation?", GetSlaveTesterModuleVSA, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:SLAVe:MODUle:VSG#:INFOrmation?", GetSlaveTesterModuleVSG, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:SLAVe:MODUle:BP:INFOrmation?", GetSlaveTesterModuleBP, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:SLAVe:TESTer:INFO:ARB?", GetSlaveTesterInfoARB, 0, SCPI_QUERY_CMD},

    {"WT:SYSTem:CONFigure:VOLT:INFO?", GetVoltAge, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:CONFigure:VOLT:INFO:ARB?", GetVoltAgeArb, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:CONFigure:TEST:MODE", SetTestMode, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:CONFigure:SCPI:TIME:USED:RECOrd:ENABle", SetTimerEnable, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:CONFigure:SCPI:TIME:USED?", GetCommandUsedTime, 0, SCPI_QUERY_CMD},
    {"WT:CONFigure:STORe:PATHloss:CORRection:TABLe",SCPI_CreateJSONFile,0,SCPI_SEQUENTIAL},
    {"WT:CONFigure:STORe:PATHloss:CORRection:TABLe:ARB",SCPI_UploadJSONFile,0,SCPI_SEQUENTIAL},
    {"WT:CONFigure:PATHloss:CORRection:TABLe?",SCPI_ParseJSONFile,0,SCPI_QUERY_CMD},

    // 全双工
    {"WT:CONFigure:FULL:DUPLex:ENABle", SetFullDuplexEnable, 0, SCPI_SEQUENTIAL},
    {"WT:CONFigure:FULL:DUPLex:ENABle?", GetFullDuplexEnable, 0, SCPI_QUERY_CMD},
    {"WT:CONFigure:GET:FULL:DUPLex:ENABle?", GetFullDuplexEnable, 0, SCPI_QUERY_CMD},   // 后续会废弃改命令
};

static const scpi_command_t scpi_wt_set_vsa_cmd[] = {
    {"WT:SENSe:CONFigure:WAVE:CAL:COMPensate", SetVsaFlatnessCalCompensateEnable, 0, SCPI_SEQUENTIAL},
    {"WT:SENSe:CONFigure:IQImb:COMPensate", SetVsaIQImbCompensateEnable, 0, SCPI_SEQUENTIAL},
    {"WT:SENSe:CONFigure:TIME:DOMAin:IQ:COMPensate:FORCe:ENABle", SetVsaDomainIQCompensateForceEnable, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:FREQuency", SetVsaFrequency, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:MAXPower", SetVsaMaxPower, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:SMPTime", SetVsaSamplingTime, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:RFPOrt", SetVsaPort, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:TRIGer:TYPE", SetVsaTriggerType, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:TRIGer:LEVEl", SetVsaTriggerLevel, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:TRIGer:TMO", SetVsaTriggerTimeOut, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:ANALy:FRAMe:INDEx", SetVsaAlzFrameIndex, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:TMOWaitting", SetVsaTimeOutWaittingSec, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:SAMPle:RATE:MODE", SetVsaSampleRateMode, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:SAMPle:RATE", SetVsaSampleRate, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:MAX:IFG", SetVsaMaxIFG, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:EXT#:GAIN", SetVsaExtGain, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:DEMOd", SetVsaDemod, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:TRIGer:PRETime", SetVsaTriggerPreTime, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:FREQuency:OFFSet", SetVsaFreqOffset, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:LTE][:NR][:NIOT]:SENSe:CONFigure:TRIGer:GAPTime", SetVsaTriggerGapTime, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:LTE][:NR][:NIOT]:SENSe:CONFigure:TRIGer:FrameTime", SetVsaTriggerFrameTime, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:LTE][:NR][:NIOT]:SENSe:CONFigure:TRIGer:EDGE", SetVsaTriggerEdge, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:PATHloss:CORRection",SetVSAAutoPowerCorrect,0,SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:DC:OFFSet:I", SetVsaDCOffsetI, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:DC:OFFSet:Q", SetVsaDCOffsetQ, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:UNIT:MASK", SetVsaUnitMask, 0, SCPI_SEQUENTIAL},

    {"WT[:WIFI]:SENSe:CONFigure:EXTEnd:EVM:MODE", SetVsaExtendEVMStatus, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI]:SENSe:CONFigure:ANALy:ITERative:MODE", SetVsaIterativeEVMStatus, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI]:SENSe:CONFigure:ANALy:SNC:MODE", SetVsaSncEVMStatus, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI]:SENSe:CONFigure:ANALy:CC:EVM:MODE", SetVsaCcEVMStatus, 0, SCPI_SEQUENTIAL},

};

static const scpi_command_t scpi_wt_set_vsa_analyze_cmd[] = {
    //公共部分
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:ANALy:TIME:OUT", SetVsaAlzTimeOut, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:ANALy:IQ:REVerse", SetVsaAlzIQReversedFlag, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:ANALy:PKT:STARt", SetVsaAlzPacketStartPoint, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:ZWAVe]:SENSe:CONFigure:ANALy:PKT:FILTer:TIME", SetVsaAlzFilterTime, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:ANALy:PKT:FILTer:TYPE", SetVsaAlzFilterType, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:ANALy:CCDF:TYPE", SetVsaAlzCCDFFlag, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:ZWAVe]:SENSe:CONFigure:ANALy:SPECtrum:TYPE", SetVsaAlzSpectrumFlag, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:ZWAVe]:SENSe:CONFigure:ANALy:IQ:SWAP", SetVsaAlzIQSwapFlag, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE]:SENSe:CONFigure:ANALy:FREQuency:OFFSet", SetVsaAlzFreqOffset, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI]:SENSe:CONFigure:ANALy:AVG", SetVsaAlzAvgTimes, 0, SCPI_SEQUENTIAL},
    //获取算法的ADC频率值
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:FETCh:ANALy:ADC:SMP:FREQuency?", GetAlzAdcSMPFreq, 0, SCPI_QUERY_CMD},
    {"WT:SENSe:FETCh:ANALy:SMPTime?", GetVsaAlzSMPTime, 0, SCPI_QUERY_CMD},
    // WIFI分析参数

    {"WT:WIFI:SENSe:CONFigure:ANALy:BANDwidth:MODE", SetVsaAlzBandwidthMode, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:GLE][:SLE][:WSUN]:SENSe:CONFigure:ANALy:DEMOd", SetVsaAlzDemod, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:SPECtrum:MODE", SetVsaAlzSpectrumWideMode, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:WSUN]:SENSe:CONFigure:ANALy:OFDM:PH:CORR", SetVsaOFDMPhaseTracking, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:WSUN]:SENSe:CONFigure:ANALy:OFDM:CH:ESTImate", SetVsaOFDMChannelEstimation, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:WSUN]:SENSe:CONFigure:ANALy:OFDM:SYM:TIME:CORR", SetVsaOFDMTimingTracking, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:WSUN]:SENSe:CONFigure:ANALy:OFDM:FREQ:SYNC", SetVsaOFDMFrequencySync, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:WSUN]:SENSe:CONFigure:ANALy:OFDM:AMPL:TRACk", SetVsaOFDMAmplitudeTracking, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:OFDM:MIMO:MAX:POWer:DIFF", SetVsaOFDMMimoMaxPowerDiff, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:DSSS:EVM:METHod", SetVsaDSSSEvmMethod, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:DSSS:DC:REMOval", SetVsaDSSSDCRemove, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:DSSS:EQ:TAPS", SetVsaDSSSEqualizerTypes, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:DSSS:PH:CORR", SetVsaDSSSPhaseTracking, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:SPECtrum:MASK:VERSion", SetVsa11nSpectrumMaskVersion, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:WSUN]:SENSe:CONFigure:ANALy:CLOCk:RATE", SetVsaAlzClockRate, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MIMO:MODE", SetVsaAlzWifiMimoMode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:ALL:PSDU:BIT", SetVsaAlzPSDUBits, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:OFDM:NON:HT:DUPLicate:BW", SetVsaAlzOFDMNonHTDuplicateBW, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:OFDM:PREAmble:AVG", SetVsaAlzOFDMPreambleAverage, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:OFDM:EQUAlizer:SMOOthing", SetVsaAlzOFDMEqualizerSmoothing, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:WSUN]:SENSe:CONFigure:ANALy:OFDM:IQ:COMPensation", SetVsaAlzOFDMIQCompensation, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:OFDM:LDPC:DECOde:ITERation:TIMEs", SetVsaAlzOFDMLdpcDecodeIterationTimes , 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:HW:DECOde", SetVsaAlzHardwareDecodeSwitch, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:OFDM:ICISuppression", SetVsaAlzOFDMICISuppression, 0, SCPI_SEQUENTIAL},

    //H矩阵
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:ANALy:H:MATrix", SetVsaAlzHMatrix, 0, SCPI_SEQUENTIAL},

    //配置wifi mac层解密的配置,TKIP、CCMP、GCMP
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:SECurity:ALGorithm", SetVsaAlzMacSecurityMode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:WEP", SetVsaAlzMacDecryptionWEP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:KEY:TYPE", SetVsaAlzMacDecryptionKeyTypeName, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:SSID", SetVsaAlzMacDecryptionSSID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:PASS:PHRAse", SetVsaAlzMacDecryptionPassPhrase, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:NONCe", SetVsaAlzMacDecryptionNonce, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:PSK", SetVsaAlzMacDecryptionPSK, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:TK", SetVsaAlzMacDecryptionTK, 0, SCPI_SEQUENTIAL},
    //CCMP、GCMP
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:CCMP:OR:GCMP:AMSDU:CAPable", SetVsaAlzMacDecryptionCCMPGCMPAmsduCapable, 0, SCPI_SEQUENTIAL},

    //帧过滤
    {"WT:WIFI:SENSe:CONFigure:ANALy:FRAMe:FILTer", SetVsaAlzFrameFilter, 0, SCPI_SEQUENTIAL},

    //WAPI
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:WAPI:KEY:TYPE", SetVsaAlzMacWAPIDecryptionKeyTypeName, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:WAPI:PASS:PHRAse", SetVsaAlzMacWAPIDecryptionPassPhrase, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:WAPI:N12", SetVsaAlzMacWAPIDecryptionN12, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:WAPI:BK", SetVsaAlzMacWAPIDecryptionBK, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:WAPI:TK", SetVsaAlzMacWAPIDecryptionTK, 0, SCPI_SEQUENTIAL},
    //WAPI AAD 配置
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:WAPI:AAD:VERsion", SetVsaAlzMacWAPIDecryptionAADVersion, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:WAPI:FCTL:HTC", SetVsaAlzMacWAPIDecryptionAADFrameControlHTC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:MAC:DECryption:WAPI:AMSDU:CAPable", SetVsaAlzMacWAPIDecryptionAmsduCapableField, 0, SCPI_SEQUENTIAL},

    //把.tb参考文件转换成TB结构体
    {"WT:WIFI:SENSe:GET:ANALy:AX:FILE:TO:TB:STRUct?", GetVsaFile2TBParam, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:GET:ANALy:DOT:TB:FILE:TO:TB:STRUct?", GetVsaFile2TBParam, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:GET:ANALy:DOT:TB:FILE:TO:TB:JSON?", GetVsaFile2TBParam_Json, 0, SCPI_QUERY_CMD},

    // 把.gle参考文件转换成AlzParamSparkLink结构体
    {"WT:SLE:SENSe:GET:ANALy:SLE:FILE:TO:STRUct?", GetVsaFile2SLEParam, 0, SCPI_QUERY_CMD},

    // BT 分析参数
    {"WT:BT:SENSe:CONFigure:ANALy:BTRAte", SetVsaBTAlzRate, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SENSe:CONFigure:ANALy:PACKet:TYPE", SetVsaBTAlzPacketType, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SENSe:CONFigure:ANALy:ACP:VIEW:RANGe:TYPE", SetVsaBTAlzACPViewRangeType, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SENSe:CONFigure:ANALy:ACP:SWEEp:TIMEs", SetVsaBTAlzACPSweepTimes, 0, SCPI_SEQUENTIAL},
    //BLE
    {"WT:BT:SENSe:CONFigure:ANALy:BLE:ENHAnced:MODE", SetVsaBTAlzBleEnhancedMode, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SENSe:CONFigure:ANALy:BLE:PHYsical:CHANnel:PDU:TYPE", SetVsaBTAlzBlePhysicalChannelPDUType, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SENSe:CONFigure:ANALy:BLE:SYNC:MODE", SetVsaBTAlzBleSyncMode, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SENSe:CONFigure:ANALy:BLE:ACCEss:ADDRess", SetVsaBTAlzBleAccessAddress, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SENSe:CONFigure:ANALy:BLE:CHANnel:INDEx", SetVsaBTAlzBleChannelIndex, 0, SCPI_SEQUENTIAL},
    // Zigbee分析参数
    {"WT:ZIGBee:SENSe:CONFigure:ANALy:OPTImise", SetVsaZigbeeAnalyzeOptimise, 0, SCPI_SEQUENTIAL},
    // GPRF
    {"WT:GPRF:SENSe:CONFigure:ANALy:RBW", SetVsaAlzRBW, 0, SCPI_SEQUENTIAL},
    {"WT:GPRF:SENSe:CONFigure:ANALy:WINDowtype", SetVsaAlzWindowType, 0, SCPI_SEQUENTIAL},
    //SFO
    {"WT[:WIFI][:WSUN]:SENSe:CONFigure:ANALy:OFDM:SAMPling:FREQuency:OFFSet:COMPensation", SetVsaAlzSfoCompensation, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:OFDM:OBW:CALculate:METHod", SetVsaAlzObwCalculateMethod, 0, SCPI_SEQUENTIAL},
    //GLE vsa 分析参数
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:FRAMe:TYPE", SetVsaGleAlzFrameType, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:BAND:WIDTh", SetVsaGleAlzBandWidth, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:CTRL:INFO:TYPE", SetVsaGleAlzCtrlinfoType, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:PID", SetVsaGleAlzPID, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:PAYLoad:CRC:TYPE", SetVsaGleAlzPayloadCrcType, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:PAYLoad:CRC:SEED", SetVsaGleAlzPayloadCrcSeed, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:PILOt:DENSity", SetVsaGleAlzPilotDensity, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:POLAr:ENCOde:PATH:NUM", SetVsaGleAlzPolarEncodePathNum, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:SCRAmble", SetVsaGleAlzScramble, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:CHANnel:TYPE", SetVsaGleAlzChannelType, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:FREQ:RANGe", SetVsaGleAlzFreqRange, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:BOARd:INDEx", SetVsaGleAlzBroadcastChannelNo, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:SLOT:INDEx", SetVsaGleAlzSlotIndex, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:SYNC:MODE", SetVsaGleAlzSyncMode, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:M:SEQUence:NUM", SetVsaGleAlzMSequenceNum, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:ACCEss:CODE", SetVsaGleAlzAccessCode, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:RAISed:ROOT:COSIne:FILTer", SetVsaGleAlzRRCFilter, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:MCS", SetVsaGleAlzMCS, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SENSe:CONFigure:ANALy:PAYLoad:MODE", SetVsaGleAlzPayloadAlzMode, 0, SCPI_SEQUENTIAL},
    
    //Wisun vsa 分析参数
    {"WT:WSUN:SENSe:CONFigure:ANALy:OFDM:OPTIon", SetVsaWiSUNAlzOFDMOption, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SENSe:CONFigure:ANALy:PHY:OFDM:INTEr:LEAVing", SetVsaWiSUNAlzPhyOFDMInterLeaving, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SENSe:CONFigure:ANALy:OQPSk:FREQ:BAND", SetVsaWiSUNAlzOQPSKFreqBand, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SENSe:CONFigure:ANALy:FSK:DATA:RATE", SetVsaWiSUNAlzFSKDataRate, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SENSe:CONFigure:ANALy:FSK:ACP:CALCulation:MODE", SetVsaWiSUNAlzFSKAcpCalMode, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SENSe:CONFigure:ANALy:FSK:CHANnel:SPACing", SetVsaWiSUNAlzFSKChannelSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SENSe:CONFigure:ANALy:FSK:MODUlation:INDEx", SetVsaWiSUNAlzFSKModulationIndex, 0, SCPI_SEQUENTIAL},

    //ZWAVE 分析参数
    {"WT:ZWAVe:SENSe:CONFigure:ANALy:DATA:RATE", GetVsaZWAVeDataRate, 0, SCPI_SEQUENTIAL},

    // 双工分析参数
    {"WT:SENSe:CONFigure:FULL:DUPLex:NOISe:COMP:FLAG", SetFullDuplexVsaNoiseCompFlag, 0, SCPI_SEQUENTIAL},
    {"WT:SENSe:CONFigure:FULL:DUPLex:NOISe:COMP:FLAG?", GetFullDuplexVsaNoiseCompFlag, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_set_vsa_tb_analyze_cmd[] = {
    // HE-TB VSA分析参数设置
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB", SetVsaAxTbAlzParam, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:FLAG", SetVsaAxTbAlzParamFlag, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:MU:MIMO", SetVsaAxTbAlzParamMUFlag, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:USER:ID", SetVsaAxTbAlzParamUserID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:GLTF:SIZE", SetVsaAxTbAlzParamGILTFSize, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:NUMber:HLTF", SetVsaAxTbAlzParamNumLTF, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:LDPC:EXTRa", SetVsaAxTbAlzParamLDPCExtra, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:PE", SetVsaAxTbAlzParamPE, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:AFACtor", SetVsaAxTbAlzParamAFactor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:STBC", SetVsaAxTbAlzParamStbc, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:DOPPler", SetVsaAxTbAlzParamDoppler, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:MIDamble:PERIodicity", SetVsaAxTbAlzParamMidPeriodicity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:USER:NUMber", SetVsaAxTbAlzParamUserNum, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:NSS[:USER#]", SetVsaAxTbAlzParamUserNSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:MCS[:USER#]", SetVsaAxTbAlzParamUserMCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:SEGment[:USER#]", SetVsaAxTbAlzParamUserSegment, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:RU:INDEx[:USER#]", SetVsaAxTbAlzParamUserRUIndex, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:CODing:TYPE[:USER#]", SetVsaAxTbAlzParamUserCodingType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:DCM[:USER#]", SetVsaAxTbAlzParamUserDCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:AID[:USER#]", SetVsaAxTbAlzParamUserAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AX:TB:NSS:STARt[:USER#]", SetVsaAxTbAlzParamUserNNstart, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_get_vsa_cmd[] = {
    {"WT:SENSe:CONFigure:WAVE:CAL:COMPensate?", GetVsaFlatnessCalCompensate, 0, SCPI_QUERY_CMD},
    {"WT:SENSe:CONFigure:IQImb:COMPensate?", GetVsaIQImbCompensate, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:LTE][:NR][:NIOT]:SENSe:CONFigure:FREQuency?", GetVsaCfgFrequency, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:MAXPower?", GetVsaCfgMaxPower, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:SMPTime?", GetVsaCfgSamplingTime, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:RFPOrt?", GetVsaCfgPort, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:TRIGer:TYPE?", GetVsaCfgTriggerType, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:TRIGer:LEVEl?", GetVsaCfgTriggerLevel, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:TRIGer:TMO?", GetVsaCfgTriggerTimeOut, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:EXT#:GAIN?", GetVsaCfgExtGain, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:DEMOd?", GetVsaDemod, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:TRIGer:PRETime?", GetVsaCfgTriggerPreTime, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:TMOWaitting?", GetVsaTimeOutWaittingSec, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:ANALy:FRAMe:INDEx?", GetVsaAlzFrameIndex, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:MAX:IFG?", GetVsaMaxIFG, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:ACTUal:MAXPower?", GetVsaActualMaxPower, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:TRIGer:GAPTime?", GetVsaTriggerGapTime, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:TRIGer:EDGE?", GetVsaTriggerEdge, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_get_11agb_datainfo_cmd[] = {

    // 11ag information
    {"WT:WIFI:SENSe:FETCh:AG:DATA:RATE?", GetVsaRst_11ag_DataRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AG:SYMbol:NUMber?", GetVsaRst_11ag_SymbolCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AG:PSDU:LENgth?", GetVsaRst_11ag_PSDULen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AG:CODE:RATE?", GetVsaRst_11ag_CodingRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AG:MODulation:TYPE?", GetVsaRst_11ag_Modulation, 0, SCPI_QUERY_CMD},
    // 11b data information
    {"WT:WIFI:SENSe:FETCh:B:DATA:RATE?", GetVsaRst_11b_DataRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:DATA:LENgth?", GetVsaRst_11b_DataLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:PSDU:LENgth?", GetVsaRst_11b_PSDULen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:PREAmble:TYPE?", GetVsaRst_11b_PreambleType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:SDF:PASS?", GetVsaRst_11b_SDFPass, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:HEADer:PASS?", GetVsaRst_11b_HeaderPass, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_get_11n_datainfo_cmd[] = {
    // 11n data information
    {"WT:WIFI:SENSe:FETCh:N:DATA:RATE?", GetVsaRst_11n_DataRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:SYMbol:NUMber?", GetVsaRst_11n_SymbolCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:PSDU:LENgth?", GetVsaRst_11n_PSDULen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:CODE:RATE?", GetVsaRst_11n_CodingRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:MODulation:TYPE?", GetVsaRst_11n_Modulation, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:HT:SIG:CRC?", GetVsaRst_11n_HTSIGCRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:MCS?", GetVsaRst_11n_MCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:BW?", GetVsaRst_11n_BW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:HT:LENgth?", GetVsaRst_11n_HTLength, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:SMOOthing?", GetVsaRst_11n_Smoothing, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:NOT:SOUNding?", GetVsaRst_11n_Sounding, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:AGGregation?", GetVsaRst_11n_Aggregation, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:STBC?", GetVsaRst_11n_STBC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:CODing:TYPE?", GetVsaRst_11n_CodingType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:GI:TYPE?", GetVsaRst_11n_GIType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:EXT:SSTRms?", GetVsaRst_11n_SSTrms, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:CRC?", GetVsaRst_11n_Crc, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:HT:SIG:TAIL?", GetVsaRst_11n_HTSIGTail, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:LSIG:VALId?", GetVsaRst_11n_LSIGValid, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:LSIG:PARIty?", GetVsaRst_11n_LSIGParity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:LSIG:RATE?", GetVsaRst_11n_LSIGRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:LSIG:LENgth?", GetVsaRst_11n_LSIGLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:LSIG:TAIL?", GetVsaRst_11n_LSIGTail, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_get_11ac_datainfo_cmd[] = {
    // 11ac data information
    {"WT:WIFI:SENSe:FETCh:AC:MU:MIMO?", GetVsaRst_11ac_PPDU, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:DATA:RATE?", GetVsaRst_11ac_DataRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:SYMbol:NUMber?", GetVsaRst_11ac_SymbolCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:PSDU:LENgth?", GetVsaRst_11ac_PSDULen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:CODing:RATE?", GetVsaRst_11ac_CodingRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MODulation:TYPE?", GetVsaRst_11ac_Modulation, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:SIGA:CRC?", GetVsaRst_11ac_SIGACRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MCS?", GetVsaRst_11ac_MCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:BW?", GetVsaRst_11ac_BW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:LTF:NUMber?", GetVsaRst_11ac_LTFCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:NSTS?", GetVsaRst_11ac_NSTS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:NSS?", GetVsaRst_11ac_NSS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:STBC?", GetVsaRst_11ac_STBC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:CODing:TYPE?", GetVsaRst_11ac_CodingType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:GI:TYPE?", GetVsaRst_11ac_GIType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:GROUp:ID?", GetVsaRst_11ac_GroupID, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:SIGB:CRC?", GetVsaRst_11ac_SIGBCRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:SIGB:MCS?", GetVsaRst_11ac_SIGBMCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:SIGB:LENgth?", GetVsaRst_11ac_SIGBLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:LSIG:PARIty?", GetVsaRst_11ac_LSIGParity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:LSIG:RATE?", GetVsaRst_11ac_LSIGRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:LSIG:LENgth?", GetVsaRst_11ac_LSIGLen, 0, SCPI_QUERY_CMD},
    // 11ac MU-MIMO user information
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER:NUMber?", GetVsaRst_VHT_MUMIMO_UserNumber, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:SYMbol:NUMber?", GetVsaRst_VHT_MUMIMO_SymbolNumber, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:BW?", GetVsaRst_VHT_MUMIMO_BW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:STBC?", GetVsaRst_VHT_MUMIMO_STBC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:LTF:NUMber?", GetVsaRst_VHT_MUMIMO_LTFNumber, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:GI:TYPE?", GetVsaRst_VHT_MUMIMO_GIType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:GROUp:ID?", GetVsaRst_VHT_MUMIMO_GroupID, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:DATA:RATE?", GetVsaRst_VHT_MUMIMO_UserDataRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:PSDU:LENgth?", GetVsaRst_VHT_MUMIMO_UserPSDULen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:CRC?", GetVsaRst_VHT_MUMIMO_UserCRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:MCS?", GetVsaRst_VHT_MUMIMO_UserMCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:CODing:TYPE?", GetVsaRst_VHT_MUMIMO_UserCodingType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:MODulation:TYPE?", GetVsaRst_VHT_MUMIMO_UserModulation, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:CODing:RATE?", GetVsaRst_VHT_MUMIMO_UserCodingRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:SIGB:LENgth?", GetVsaRst_VHT_MUMIMO_UserSIGBLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:NSTS?", GetVsaRst_VHT_MUMIMO_UserNSTS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:NSS?", GetVsaRst_VHT_MUMIMO_UserNSS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:POWer?", GetVsaRst_VHT_MUMIMO_UserPower, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:NSS#:POWer?", GetVsaRst_VHT_MUMIMO_UserPower, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:EVM:ALL?", GetVsaRst_VHT_MUMIMO_UserEVMALL, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:NSS#:EVM:ALL?", GetVsaRst_VHT_MUMIMO_UserEVMALL, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:EVM:DATA?", GetVsaRst_VHT_MUMIMO_UserEVMData, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:NSS#:EVM:DATA?", GetVsaRst_VHT_MUMIMO_UserEVMData, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:EVM:PILOt?", GetVsaRst_VHT_MUMIMO_UserEVMPilot, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:USER#:NSS#:EVM:PILOt?", GetVsaRst_VHT_MUMIMO_UserEVMPilot, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:MU:ALL:USER:INFOrmation?", GetVsaRst_VHT_MUMIMO_AllUserInformation, 0, SCPI_QUERY_CMD},

};

static const scpi_command_t scpi_wt_get_11ax_datainfo_cmd[] = {
    // 11ax data information
    {"WT:WIFI:SENSe:FETCh:AX:PPDU?", GetVsaRst_11ax_PPDU, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGA:CRC?", GetVsaRst_11ax_SIGACRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:BW?", GetVsaRst_11ax_BW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:RU:NUMber?", GetVsaRst_11ax_RUCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:RU:USER:NUMber?", GetVsaRst_11ax_UserCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SYMbol:NUMber?", GetVsaRst_11ax_SymbolCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:LTF:NUMber?", GetVsaRst_11ax_LTFCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:LTF:TYPE?", GetVsaRst_11ax_LTFType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:LTF:LENgth?", GetVsaRst_11ax_LTFLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:GI:LENgth?", GetVsaRst_11ax_GILen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:DATA:RATE?", GetVsaRst_11ax_DataRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:DATA:SYMbol:LENgth?", GetVsaRst_11ax_DataSymbolLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:DATA:LENgth?", GetVsaRst_11ax_FrameLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:BSS:COLOr?", GetVsaRst_11ax_BSSColor, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:TX:BEAM:CHANge?", GetVsaRst_11ax_BeamChange, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:TX:BEAM?", GetVsaRst_11ax_BeamChange, 0, SCPI_QUERY_CMD},//功能同上一条，历史原因先保留（后续不用要删除）
    {"WT:WIFI:SENSe:FETCh:AX:LDPC:EXTra:SYMbol?", GetVsaRst_11ax_LDPCExtra, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:PEDisambiguity?", GetVsaRst_11ax_PEDisambiguity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:PE:LENgth?", GetVsaRst_11ax_PELen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:PRE:FEC:FACTor?", GetVsaRst_11ax_PreFecPadFactor, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:DOPPler?", GetVsaRst_11ax_Doppler, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:MIDamble:PERiodicity?", GetVsaRst_11ax_MidamblePeriodicity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:CRC?", GetVsaRst_11ax_SIGBCRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:DCM?", GetVsaRst_11ax_SIGBDCM, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:MCS?", GetVsaRst_11ax_SIGBMCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:SYMbol:NUMber?", GetVsaRst_11ax_SIGBSymbolCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:COMPression?", GetVsaRst_11ax_SIGBCompression, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:EVM:ALL?", GetVsaRst_11ax_SIGBEVMALL, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:EVM:DATA?", GetVsaRst_11ax_SIGBEVMData, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:EVM:PILOt?", GetVsaRst_11ax_SIGBEVMPilot, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:EVM:ALL:COMPosite?", GetVsaRst_11ax_SIGBEVMALL_Composite, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:EVM:DATA:COMPosite?", GetVsaRst_11ax_SIGBEVMData_Composite, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:EVM:PILOt:COMPosite?", GetVsaRst_11ax_SIGBEVMPilot_Composite, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:COMMon:BIT?", GetVsaRst_11ax_Common8Bit, 0, SCPI_QUERY_CMD},
    // 11AX MU-MIMO
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU:COUNt:INFO?", GetVsaRstMuRuCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU:INFO?", GetVsaRstMuRuInfo, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU:USER:INFO?", GetVsaRstMuRuUserInfo, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU[:RU#][:USER#][:NSS#]:INFO?", GetVsaRstMuRuUserInfo_V2, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:MU:MIMO?", GetVsaIS_11axMUMIMO, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:VALId?", GetVsaRst_11axMUMIMO_RUValid, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:NSTS?", GetVsaRst_11axMUMIMO_RU_NSTS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:ULDL?", GetVsaRst_11axMUMIMO_ULDL, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:TONE:WIDE?", GetVsaRst_11axMUMIMO_ToneWide, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:TONE:INDEx?", GetVsaRst_11axMUMIMO_ToneIndex, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:SEGMent?", GetVsaRst_11axMUMIMO_Segment, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:MCS?", GetVsaRst_11axMUMIMO_MCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:MODulation:TYPE?", GetVsaRst_11axMUMIMO_Modulation, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:CODing:TYPE?", GetVsaRst_11axMUMIMO_CodingType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:CODing:RATE?", GetVsaRst_11axMUMIMO_CodingRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:STBC?", GetVsaRst_11axMUMIMO_STBC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:DCM?", GetVsaRst_11axMUMIMO_DCM, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:NSTS?", GetVsaRst_11axMUMIMO_NSTS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:NSS?", GetVsaRst_11axMUMIMO_NSS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:PSDU:CRC?", GetVsaRst_11axMUMIMO_PSDUCRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:PSDU:LENgth?", GetVsaRst_11axMUMIMO_PSDULen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:AID?", GetVsaRst_11axMUMIMO_AID, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:BEAM:FORMed?", GetVsaRst_11axMUMIMO_Beamformed, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:BEAMformed?", GetVsaRst_11axMUMIMO_Beamformed, 0, SCPI_QUERY_CMD},//功能同上一条，历史问题原因，先保留（后续不用要删除）
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:POWer:FACTor?", GetVsaRst_11axMUMIMO_PowerFactor, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:DATA:RATE?", GetVsaRst_11axMUMIMO_DataRate, 0, SCPI_QUERY_CMD},
    //下面这几个命令不能合成一个命令类似RU#[:NSS#]模式。SCPI库会把可选NSS默认值配置成1，所以分开写
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:POWer?", GetVsaRst_11axMUMIMO_Power, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:NSS#:POWer?", GetVsaRst_11axMUMIMO_Power, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:EVM:ALL?", GetVsaRst_11axMUMIMO_EVMAll, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:NSS#:EVM:ALL?", GetVsaRst_11axMUMIMO_EVMAll, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:EVM:DATA?", GetVsaRst_11axMUMIMO_EVMData, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:NSS#:EVM:DATA?", GetVsaRst_11axMUMIMO_EVMData, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:EVM:PILOt?", GetVsaRst_11axMUMIMO_EVMPilot, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:NSS#:EVM:PILOt?", GetVsaRst_11axMUMIMO_EVMPilot, 0, SCPI_QUERY_CMD},

    //Lsig
    {"WT:WIFI:SENSe:FETCh:AX:LSIG:PARIty?", GetVsaRst_11ax_LSIGParity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:LSIG:RATE?", GetVsaRst_11ax_LSIGRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:LSIG:LENgth?", GetVsaRst_11ax_LSIGLen, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_get_11ax_tf_cmd[] = {
    // 11ax trigger frame information
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:INFO?", GetVsaRstTriggerFrameInfo, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:TRIGger:TYPE?", GetVsaRstTF_TrigType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:MORE:TF?", GetVsaRstTF_MoreTF, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:LENgth?", GetVsaRstTF_Length, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:CS:REQuired?", GetVsaRstTF_CSRequired, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:BW?", GetVsaRstTF_BandWidth, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:MU:MODE:LTF?", GetVsaRstTF_MModeLTF, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:GI:LTF?", GetVsaRstTF_GILTF, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:LTF:SYMbol:NUMber?", GetVsaRstTF_LTFSymNum, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:STBC?", GetVsaRstTF_STBC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:LDPC:EXTra:SYMbol?", GetVsaRstTF_LDPCExtra, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:AP:TX:POWer?", GetVsaRstTF_APTxPower, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:AFACtor?", GetVsaRstTF_AFactor, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:PEDisambiguity?", GetVsaRstTF_PEDisambiguity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:DOPPler?", GetVsaRstTF_Doppler, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:MID:PERiodicity?", GetVsaRstTF_MidPeriodicity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:SREUse?", GetVsaRstTF_SReuse, 0, SCPI_QUERY_CMD},
    // TF user信息
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:USER:NUMber?", GetVsaRstTF_TBUserNumber, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:AID[:USER#]?", GetVsaRstTF_AID, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:RU:INDEx[:USER#]?", GetVsaRstTF_RUIndex, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:SEGMent[:USER#]?", GetVsaRstTF_Segment, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:CODing:TYPE[:USER#]?", GetVsaRstTF_Coding, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:MCS[:USER#]?", GetVsaRstTF_MCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:DCM[:USER#]?", GetVsaRstTF_DCM, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:SSSTream[:USER#]?", GetVsaRstTF_SSStream, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:NSS[:USER#]?", GetVsaRstTF_NSS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:RSSI[:USER#]?", GetVsaRstTF_RSSI, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:MU:SFACtor[:USER#]?", GetVsaRstTF_MUSpaingFactor, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:AGG[:USER#]?", GetVsaRstTF_TidAggLimit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:TFrame:PAC[:USER#]?", GetVsaRstTF_PreferredAC, 0, SCPI_QUERY_CMD},

};

static const scpi_command_t scpi_wt_get_bt_result_cmd[] = {
    // vsa BT part
    {"WT:BT:SENSe:FETCh:BT:INFO?", GetVsaBTInfo, 0, SCPI_QUERY_CMD},//all bt info except datainfo
    {"WT:BT:SENSe:FETCh:PACKet:TYPE?", GetVsaBTPacketType, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:PACKet:LENgth?", GetVsaBTPacketLength, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:PACKet:DATA:RATE?", GetVsaBTPacketDataRate, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:PACKet:INIT:FREQuency:ERRor?", GetVsaBTPacketInitFrequencyError, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:CARRer:FREQuency:DRIFt?", GetVsaBTFreqDrift, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:CARRer:FREQuency:DRIFt:RATE?", GetVsaBTFreqDriftRate, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:MAX:CARRer:FREQuency?", GetVsaBTMaxCarrierFreq, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F1:VALId?", GetVsaBTDeltaF1Valid, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F1:AVGrage?", GetVsaBTDeltaF1Avgrage, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F1:MAXimun?", GetVsaBTDeltaF1Maximun, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F2:VALId?", GetVsaBTDeltaF2Valid, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F2:AVGrage?", GetVsaBTDeltaF2Avgrage, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F2:MAXimun?", GetVsaBTDeltaF2Maximun, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DEVM:VALId?", GetVsaEdrEvmValid, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DEVM:CARRer:FREQuency:BUF?", GetVsaEdrEvmCarrierFreqBuf, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DEVM?", GetVsaEdrEvm, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DEVM:PEAK?", GetVsaEdrEvmPeak, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:POWer:DIFF?", GetVsaBTPowerDiff, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EDR:PERCent?", GetVsaBT99Pct, 0, SCPI_QUERY_CMD},   //20%/30%Devm
    {"WT:BT:SENSe:FETCh:EDR:DEVM:BELOw:THREshold:PERCent?", GetVsaBT99Pct, 0, SCPI_QUERY_CMD},//20%/30%Devm,历史修改原因导致有两条获取20%的
    {"WT:BT:SENSe:FETCh:EDR:DEVM:NINEty:NINE:PERCent?", GetVsaBTEDRBelowThresholdPct, 0, SCPI_QUERY_CMD},//99%Devm
    {"WT:BT:SENSe:FETCh:EDR:OMEGa:I?", GetVsaBTOmegaI, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EDR:OMEGa:O?", GetVsaBTOmegaO, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EDR:OMEGa:IO?", GetVsaBTOmegaIO, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BW20db:PASSed?", GetVsaBTBw20dbPassed, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BW20db?", GetVsaBTBw20dbSpectrum, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BW20db:ORBW?", GetVsaBTBw20dbOBWandRBW, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:DRIFt:DETAIL:VALId?", GetVsaBLEDriftDetailValid, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:FN:MAXimun?", GetVsaBLEFnMaximun, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:F0FN:MAXimun?", GetVsaBLEF0FnMaximun, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:DELTa:F1F0?", GetVsaBLEDeltaF1F0, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:DELTa:FNFN5:MAXimun?", GetVsaBLEDeltaFnFn5Maximun, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:DELTa:F0F3?", GetVsaBLEDeltaF0F3, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:DELTa:F0FN3?", GetVsaBLEDeltaF0Fn3, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:SPECTrum:ACP?", GetVsaBTSpectAcp, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:SPECTrum:ACP:MASK?", GetVsaBTSpectAcpMask, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F2:PASS:PERCent?", GetVsaBTDeltaF2PassPercent, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:FREQ:OFFSet:HEADer?", GetVsaBTFreqOffsetHeader, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:FREQ:OFFSet:SYNC?", GetVsaBTFreqOffsetSync, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:FREQ:DEVIation?", GetVsaBTFreqDeviation, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:FREQ:DEVIation:PEAK:TO:PEAK?", GetVsaBTFreqDeviationPeaktoPeak, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F2:AV:ACCEss?", GetVsaBTDeltaF2AvAccess, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F2:MAX:ACCEss?", GetVsaBTDeltaF2MaxAccess, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DEVM:VS:TIME?", GetVsaBTEdrEvmVsTime, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:FREQuency:ERROr:BUFfer?", GetVsaBTFrequencyError, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F1:MINimun?", GetVsaBTDeltaF1MINimun, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F2:MINimun?", GetVsaBTDeltaF2MINimun, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EDR:MAXimum:FREQ:VAR?", GetVsaBTMaxFreqVar, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:F0FN:AVG?", GetVsaBLEF0FnAvg, 0, SCPI_QUERY_CMD},

    {"WT:BT:SENSe:FETCh:BR:EDR:LAP?", GetVsaBTBREDRLAP, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BR:EDR:UAP?", GetVsaBTBREDRUAP, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:INFO?", GetVsaBTBLECTEInfo, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:TYPE?", GetVsaBTBLECTEType, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:DURAtion:TIME?", GetVsaBTBLECTEDurationTime, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:LT:ADDRess?", GetVsaBTLT_ADDR, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:FLOW:CTRL?", GetVsaBTFlowCtrl, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:ACK:INDIcation?", GetVsaBTACKIndication, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:SYNC:SEQuence:NUMber?", GetVsaBTSeqnum, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:LLID?", GetVsaBTLLID, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:FLOW?", GetVsaBTFlow, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:PAYLoad:SIZE?", GetVsaBTPayloadsize, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EIR?", GetVsaBTEIR, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:SR?", GetVsaBTSR, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:CLASs:DEVIce?", GetVsaBTClassOfDevice, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:PAYLoad:LT:ADDRess?", GetVsaBTLtaddr, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:CLK27b2?", GetVsaBTClk27b2, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:MAPPers?", GetVsaBTBLEMappers, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:VOICe:FIELd?", GetVsaBTVoiceField, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:PAYLoad:HEADer?", GetVsaBTPayloadHeader, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:PATTern?", GetVsaBTPattern, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EDR:GFSK:POWer?", GetVsaBTEDRGFSKPower, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EDR:DPSK:POWer?", GetVsaBTEDRDPSKPower, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EDR:GFSK:POWEr:PEAK?",GetVsaBTEDRGFSKPowerPeak, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EDR:DPSK:POWEr:PEAK?",GetVsaBTEDRDPSKPowerPeak, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EDR:GUARd:TIME?", GetVsaBTEDRGuardtime, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:POWer:AVG?", GetVsaBTBLECTEPwrAvg, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:POWer:PEAK?", GetVsaBTBLECTEPwrPeak, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:POWer:PEAK:SUB:AVG?", GetVsaBTBLECTEPwrSubAvg, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:FSI:MAX?", GetVsaBTBLECTEFsiMax, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:FSI:MIN?", GetVsaBTBLECTEFsiMin, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:FS1:SUB:FP?", GetVsaBTBLECTEFs1SubFp, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:FSI:SUB:F0:MAX?", GetVsaBTBLECTEFsiSubF0Max, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:FSI:SUB:FSI3:MAX?", GetVsaBTBLECTEFsiSubFsi3Max, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:POWer:REFerence:AVG?", GetVsaBTBLECTEPwrRefAvg, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:POWer:REFerence:DEV:DIV:AVG?", GetVsaBTBLECTEPwrRefDev, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:POWer:PN:DEV:DIV:AVG:MAX?", GetVsaBTBLECTEPwrRefDevMax, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BR:EDR:HEADer:BITS?", GetVsaBTBREDRHeaderBin, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:CTE:POWer:AVG:SLOT?", GetVsaBTBLECTEPwrAvgSlot, 0, SCPI_QUERY_CMD},

    {"WT:BT:SENSe:FETCh:EDR:SYNSeq:ERRor:BIT:NUMber?", GetVsaBTEDRSynSeqErrorBitNumber, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EDR:TRAIler:ERRor:BIT:NUMber?", GetVsaBTEDRTrailerErrorBitNumber, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F1:P99p9:PERCent?", GetVsaBTDeltaF199p9Precent, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DELTa:F2:P99p9:PERCent?", GetVsaBTDeltaF299p9Precent, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:PACKet:PAYLoad:HEADer:BITS?", GetVsaBTPacketPayloadHeaderBits, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:PSDU:BITS?", GetVsaBTPsduBits, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:PSDU:DECOde:INFO?", GetVsaRst_PSDU_tshark_V2, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:PAYLoad:CRC?", GetVsaBTPsduCRC, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:ENHAnced:MODE?", GetVsaBLEEnhancedMode, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:LEAKage:POWer?", GetVsaBTLeakagePower, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DIFFerence:POWer:PEAK:MINUs:AVG?", GetVsaBTDifferencebetweenPowerPeakAndAverage, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:EDR:PHASe:DIFFerence:ARB?", GetVsaBTEDRPhaseDifferenceArbData, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BR:BLE:FREQuency:DEVIation:ARB?", GetVsaBTBrBLEFrequencyDeviationArbData, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BW20db:FREQuency:LOW?", GetVsaBTBw20dbSpectrumFrequencyLow, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BW20db:FREQuency:HIGH?", GetVsaBTBw20dbSpectrumFrequencyHigh, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:BLE:DELTa:F2:AVG:DIV:DELTa:F1:AVG?", GetVsaBLEDeltaF2AvgDivDeltaF1Avg, 0, SCPI_QUERY_CMD},
    //meter view arb data return,inline cmd
    {"WT:BT:SENSe:FETCh:BW20db:ARB?", GetVsaBTBw20dbSpectrumArbData, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:SPECTrum:ACP:ARB?", GetVsaBTSpectAcpArbData, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:SPECTrum:ACP:MASK:ARB?", GetVsaBTSpectAcpMaskArbData, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:DEVM:VS:TIME:ARB?", GetVsaBTEdrEvmVsTimeArbData, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:FREQuency:ERROr:BUFfer:ARB?", GetVsaBTFrequencyErrorArbData, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_get_zigbee_result_cmd[] = {
    {"WT:ZIGBee:SENSe:FETCh:EVM:PSDU?", GetVsaZigbeeEvmPsdu, 0, SCPI_QUERY_CMD},
    {"WT:ZIGBee:SENSe:FETCh:EVM:PSDU:PERCent?", GetVsaZigbeeEvmPsduPercent, 0, SCPI_QUERY_CMD},
    {"WT:ZIGBee:SENSe:FETCh:EVM:SHRPhr?", GetVsaZigbeeEvmShrPhr, 0, SCPI_QUERY_CMD},
    {"WT:ZIGBee:SENSe:FETCh:EVM:SHRPhr:PERCent?", GetVsaZigbeeEvmShrPhrPercent, 0, SCPI_QUERY_CMD},
    {"WT:ZIGBee:SENSe:FETCh:EVM:OFFSet?", GetVsaZigbeeEvmOffset, 0, SCPI_QUERY_CMD},
    {"WT:ZIGBee:SENSe:FETCh:EVM:OFFSet:PERCent?", GetVsaZigbeeEvmOffsetPercent, 0, SCPI_QUERY_CMD},

    //arb data
    {"WT:ZIGBee:SENSe:FETCh:EYE:REAL:DATA:ARB?", GetVsaZigbeeEyeRealGraphic, 0, SCPI_QUERY_CMD},
    {"WT:ZIGBee:SENSe:FETCh:EYE:IMAG:DATA:ARB?", GetVsaZigbeeEyeImagGraphic, 0, SCPI_QUERY_CMD},
    {"WT:ZIGBee:SENSe:FETCh:PHASe:ERRor:CHIP:ARB?", GetVsaZigbeePhaseErrorVsChip, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_vsa_sle_result_cmd[] = {
    //SLE vsa结果
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:EVM:DATA?", GetVsaGleCtrlInfoRstSymoblConst, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:EVM:DATA:ARB?", GetVsaGleCtrlInfoRstSymoblConstARB, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:EVM:POINt?", GetVsaGleCtrlInfoRstSymoblPilotConst, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:EVM:POINt:ARB?", GetVsaGleCtrlInfoRstSymoblPilotConstARB, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:CONStell:REFS?", GetVsaGleCtrlInfoRstSymoblRefConst, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:CONStell:REFS:ARB?", GetVsaGleCtrlInfoRstSymoblRefConstARB, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:EVM:POINt:PERCent?", GetVsaGleCtrlInfoRstEVMTime,  0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:EVM:POINt:PERCent:ARB?", GetVsaGleCtrlInfoRstEVMTimeARB,  0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:ACP:DATA?", GetVsaGleAcpData,  0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:ACP:DATA:ARB?", GetVsaGleAcpDataARB,  0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:ACP:MASK?", GetVsaGleAcpMask,  0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:ACP:MASK:ARB?", GetVsaGleAcpMaskARB,  0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:EYE:POINt?", GetVsaGleEyePoint,  0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:EYE:POINt:ARB?", GetVsaGleEyePointARB,  0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:FRAMe:TYPE?", GetVsaGleFrameType, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:BAND:WIDTh?", GetVsaGleBandWidth, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:PID?", GetVsaGlePID, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:PAYLoad:LEN?", GetVsaGlePayloadLen, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:PAYLoad:CRC:TYPE?", GetVsaGlePayloadCrcType, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:PAYLoad:CRC?", GetVsaGlePayloadCrc, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:SYNC:SIGNal?", GetVsaGleSyncSignal, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO?", GetVsaGleCtrlInfo, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:PAYLoad:BIN?", GetVsaGlePayloadBin, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:DELTa:FD1:AVG?", GetVsaGleDeltaFd1Avg, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:DELTa:FD1:MAX?", GetVsaGleDeltaFd1Max, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:DELTa:FD1:MIN?", GetVsaGleDeltaFd1Min, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:DELTa:FD2:AVG?", GetVsaGleDeltaFd2Avg, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:DELTa:FD2:MIN?", GetVsaGleDeltaFd2Min, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:ZERO:CROSsing:ERR?", GetVsaGleZeroCrossingErr, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:EVM:AVG?", GetVsaGleCtrlInfoEvmAvg, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:EVM:PEAK?", GetVsaGleCtrlInfoEvmPeak, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:EVM:P99Pct?", GetVsaGleCtrlInfoEvm99PCT, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:EVM:AVG?", GetVsaGleEvmAvg, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:EVM:PEAK?", GetVsaGleEvmPeak, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:EVM:P99Pct?", GetVsaGleEvm99PCT, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:INIT:FREQ:ERROr?", GetVsaGleInitFreqErr, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:FREQ:DRIFt?", GetVsaGleFreqDrift, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:FREQ:DRIFt:RATE?", GetVsaGleFreqDriftRate, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:SYMBol:CLK:ERR?", GetVsaGleSymbolClkErr, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:MAX:TIME:DEV?", GetVsaGleMaxTimeDev, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:TYPE?", GetVsaGleCtrlInfoType, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTRL:INFO:CRC?", GetVsaGleCtrlInfoCRC, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:DELTa:FD2:P99Pct?", GetVsaGleDeltaFd299PCT, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_vsa_wisun_result_cmd[] = {

    {"WT:WSUN:SENSe:FETCh:PHR:CRC:CHECk?", GetVsaWiSUNPHRCRCCheck, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:PHR:RATE:FIELd?", GetVsaWiSUNPHRRateField, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:PHR:FRAMe:LENgth?", GetVsaWiSUNPHRFrameLength, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:PHR:SCRAmbler?", GetVsaWiSUNPHRScrambler, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:PHR:BIT:INFO?", GetVsaWiSUNPHRBit, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:PHR:CRC:BIT:INFO?", GetVsaWiSUNPHRCRCBit, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:DATA:RATE?", GetVsaWiSUNDataRate, 0, SCPI_QUERY_CMD},

    {"WT:WSUN:SENSe:FETCh:BW?", GetVsaWiSUNBW, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:SYMBol:COUNt?", GetVsaWiSUNSymbolCount, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:CODIng:RATE?", GetVsaWiSUNCodingRate, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:MODUlation:TYPE?", GetVsaWiSUNModulationType, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:PSDU:LENgth?", GetVsaWiSUNPSDULength, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:PSDU:CRC:CHECk?", GetVsaWiSUNPSDUCRC, 0, SCPI_QUERY_CMD},

    {"WT:WSUN:SENSe:FETCh:FSK:EYE:POINt:ARB?", GetVsaWiSUNFSKEyePoint, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:FSK:SHR:SFD:BITS:ARB?", GetVsaWiSUNFSKShrSfdBits, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:FSK:PHR:BITS:ARB?", GetVsaWiSUNFSKPhrBits, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:FSK:PHY:PAYLoad:BITS:ARB?", GetVsaWiSUNFSKPhyPayloadBits, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:FSK:CRC:BITS:ARB?", GetVsaWiSUNFSKCrcBits, 0, SCPI_QUERY_CMD},

    {"WT:WSUN:SENSe:FETCh:ACP:VALId:DATA:NUMber?", GetVsaWiSUNAcpValidnum, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:ACP:POWEr:DATA:ARB?", GetVsaWiSUNAcpPwrData, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:ACP:CHANnel:DATA:ARB?", GetVsaWiSUNAcpChannelData, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:ACP:MASK:ARB?", GetVsaWiSUNAcpMask, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:ACP:WIDTh?", GetVsaWiSUNAcpWidth, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:PHASe:ERROr:CHIP:ARB?", GetVsaWiSUNPhaseErrorVsChip, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:EYE:REAL:DATA:ARB?", GetVsaWiSUNEyeRealGraphic, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:EYE:IMAG:DATA:ARB?", GetVsaWiSUNEyeImagGraphic, 0, SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:PSDU:STReam:BITS:INFO:ARB?", GetVsaWiSUNPSDUBit, 0 , SCPI_QUERY_CMD},
    {"WT:WSUN:SENSe:FETCh:PSDU:STReam:BITS:INFO:LENGth?", GetVsaWiSUNPSDUBitLength, 0 , SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_vsa_zwave_result_cmd[] = {
    {"WT:ZWAVe:SENSe:FETCh:EYE:DATA:ARB?", GetVsaZWAVeEyeGraphic, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_set_vsg_cmd[] = {
    {"WT:SOURce:CONFigure:WAVE:CAL:COMPensate", SetVsgWaveFlatnessCalCompensateEnable, 0, SCPI_SEQUENTIAL},
    {"WT:SOURce:CONFigure:IQImb:COMPensate", SetVsgIQImbCompensateEnable, 0, SCPI_SEQUENTIAL},
    {"WT:SOURce:CONFigure:TIME:DOMAin:IQ:COMPensate:FORCe:ENABle", SetVsgDomainIQCompensateForceEnable, 0, SCPI_SEQUENTIAL},
    // SISO,MIMO通用
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:STARt:ASYnchronous", SetVsgStartAsy, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:STOP", SetVsgStop, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:SAMPle:RATE", SetVsgSampleRate, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:FREQuency", SetVsgFreq, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:FREQuency:IMMEdiately", SetVsgFreqFast, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:POWer", SetVsgPower, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:RFPOrt", SetVsgPort, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE]:SOURce:CONFigure:POWer:IMMEdiately", SetVsgPowerFast, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:WAVE", SetVsgWaveName, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:SAVE:WAVE", SaveVsgWave, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:WAVE:GAP", SetVsgWaveGap, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:WAVE:GAP:MODE", SetVsgWaveGapMode, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:WAVE:GAP:MAX", SetVsgRandomWaveGapMax, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:WAVE:GAP:MIN", SetVsgRandomWaveGapMin, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:REPEat", SetVsgRepeat, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:TMOWaitting", SetVsgTimeOut, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:WSUN][:LTE][:NIOT]:SOURce:CONFigure:DEMOd", SetVsgDemod, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:STARt", SetVsgStart, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:EXT#:GAIN", SetVsgExtGain, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:SAMPle:RATE:MODE", SetVsgSampleRateMode, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:FREQuency:OFFSet", SetVsgFreqOffset, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE]:SOURce:CONFigure:EXT#:GAIN:IMMEdiately", SetVsgExtGainFast, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:LTE][:NIOT]:SOURce:CONFigure:GAP:POWEr:ENABle", SetVsgGapPowerEnable, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:UNIT:MASK", SetVsgUnitMask, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:PATTern", SCPI_SetVSGPattern, 0, SCPI_SEQUENTIAL},
    //自动线衰修正
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:PATHloss:CORRection",SetVSGAutoPowerCorrect,0,SCPI_SEQUENTIAL},
    //模拟IQ参数
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:DC:OFFSet:I", SetVsgDCOffsetI, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:DC:OFFSet:Q", SetVsgDCOffsetQ, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:COMMon:MODE:VOLTage", SetVsgCommModeVolt, 0, SCPI_SEQUENTIAL},
    //计算IQ不平衡参数
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:CALculate:IQ:IMBalance:SEGMent#", SetVsgImbalanceCal, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:BROAdcast", SetVsgBroadcastStatus, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:BROAdcast:DEBUg", SetVsgBroadcastDebugPower, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:BROAdcast:TMOWaitting", SetVsgBroadcastTimeout, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_get_vsg_cmd[] = {
    {"WT:SOURce:CONFigure:WAVE:CAL:COMPensate?", GetVsgWaveFlatnessCalCompensate, 0, SCPI_QUERY_CMD},
    {"WT:SOURce:CONFigure:IQImb:COMPensate?", GetVsgIQImbCompensate, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:FREQuency?", GetVsgFreq, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:POWer?", GetVsgPower, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:RFPOrt?", GetVsgPort, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE?", GetVsgWaveName, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:GAP?", GetVsgWaveGap, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:REPEat?", GetVsgRepeat, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:TMOWaitting?", GetVsgTimeOut, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:EXT#:GAIN?", GetVsgExtGain, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CURRent:STATe?", GetVsgStatus, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:GAP:POWEr:ENABle?", GetVsgGapPowerEnable, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:ACTUal:POWEr?", GetVsgActualPower, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:BROAdcast?", GetVsgBroadcastStatus, 0, SCPI_QUERY_CMD}, 
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:BROAdcast:STATus?", GetBroadcastRunStatus, 0, SCPI_QUERY_CMD},
    //{"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:SAMPle:RATE:MODE?", GetVsgSampleRateMode, 0,SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_wavegenerator_cmd[] = {
    // WAVE GENERATOR
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:WSUN][:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:DEMOd", SetWaveGenDemod, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:WSUN][:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:SAMPling:RATE", SetWaveGenSampleRate, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:CLOCk:RATE", SetWaveGenOFDMClockRate, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE]:SOURce:CONFigure:WAVE:BW", SetWaveGenBandWidth, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:WSUN][:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:FREQuency:OFFSet", SetWaveGenFreqOffset, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:WSUN][:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:IQ:IMB:AMP", SetWaveGenIQImbAmp, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:WSUN][:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:IQ:IMB:PHASe", SetWaveGenIQImbPhase, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:WSUN][:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:DC:OFFSet:I", SetWaveGenDCOffsetI, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:WSUN][:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:DC:OFFSet:Q", SetWaveGenDCOffsetQ, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:WSUN]:SOURce:CONFigure:WAVE:IFG", SetWaveGenIFG, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:HEAD:TAIL:IFG", SetWaveGenHeadandTailIFG, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:WSUN][:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:SNR", SetWaveGenSNR, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:SPATial:EXTension", SetWaveGenSpatialExtension, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:NTX", SetWaveGenNtx, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:PHASe:NOISe:ENABle", SetWaveGenPhaseNoiseEnable, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:PHASe:NOISe", SetWaveGenPhaseNoise, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:PHASe:NOISe:PLL:BW", SetWaveGenPhaseNoisePllBW, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:CLOCk:ERRor", SetWaveGenClockError, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:TIMEout", SetWaveGenTimeout, 0, SCPI_SEQUENTIAL},

    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:PPDU", SetWaveGenAxPPDU, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:WSUN][:LTE][:NIOT]:SOURce:CONFigure:WAVE:SAVE:PN", SCPIWaveGenGo, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:WSUN]:SOURce:CONFigure:WAVE:GEN:PN:AS:SENSe:LOAD:FILE:CAPTure", SCPIWaveGentoVsaLoadAsCapture, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:WSUN]:SOURce:CONFigure:WAVE:GEN:PN:AS:SOURce:LOAD:FILE", SCPIWaveGentoSetVsgWave, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SOURce:CONFigure:WAVE:DUPLicate", SetWaveGenWifiDuplicate, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:AWGN", SetWaveGenAWGN, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:PEAK:AVG:RATio:OPT", SetWaveGenPeakAvgRatioOption, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE][:NR][:NIOT]:SOURce:CONFigure:WAVE:CLOCk:ERRor", SetWaveGenClockError, 0, SCPI_SEQUENTIAL},

    //多PN信号配置
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE]:SOURce:CONFigure:WAVE:MULTiple:PN:REPEat:GAP", SetMPRepeatGap, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE]:SOURce:CONFigure:WAVE:MULTiple:PN:REPEat:COUNt", SetMPRepeatCount, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE]:SOURce:CONFigure:WAVE:MULTiple:PN:GAP", SetMPGap, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE]:SOURce:CONFigure:WAVE:MULTiple:PN:SAVE:TEMP:PN?", SetMPSaveTempPn, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE]:SOURce:CONFigure:WAVE:MULTiple:PN:CREAte:PN?", SetMPCreatePn, 0, SCPI_QUERY_CMD},


    // 11B
    {"WT:WIFI:SOURce:CONFigure:WAVE:B:RATE", Set11B_DataRate, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:B:PREAmble", Set11B_Preamble, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:B:M22:FILTer", Set11B_FilterEnable, 0, SCPI_SEQUENTIAL},
    // 11AG
    {"WT:WIFI:SOURce:CONFigure:WAVE:AG:RATE", Set11AG_DataRate, 0, SCPI_SEQUENTIAL},
    // 11N
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:MCS", Set11N_MCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:FRAMe:TYPE", Set11N_FrameType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:SOUNding", Set11N_Sounding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:STBC", Set11N_STBC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:SMOOthing", Set11N_Smoothing, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:AGGregation", Set11N_AGG, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:GI", Set11N_GI, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:CODing:TYPE", Set11N_Coding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:SOUNding:NDP", Set11N_SoundingNDP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:ESS", Set11N_ESS, 0, SCPI_SEQUENTIAL},
    //11n Qmat
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:QMAT", Set11N_QMat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:QMAT:NTX", Set11N_QMatNtx, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:QMAT:TYPE", Set11N_QMatType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:QMAT:DELAy", Set11N_QMatDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:N:QMAT:MAT", Set11N_QMatMap, 0, SCPI_SEQUENTIAL},
    // 11AC
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MCS", Set11AC_MCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:STBC", Set11AC_STBC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:NSS", Set11AC_NSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:GI", Set11AC_GI, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:CODing:TYPE", Set11AC_Coding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:BEAM:FORMed", Set11AC_Beamformed, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:BEAMformed", Set11AC_Beamformed, 0, SCPI_SEQUENTIAL},//功能同上一条，历史问题原因，先保留（后续不用要删除）
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:SOUNding:NDP", Set11AC_SoundingNDP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:GID", Set11AC_GroupID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:PARTial:AID", Set11AC_PartialAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:TXOP", Set11AC_TXOPPS, 0, SCPI_SEQUENTIAL},
    // 11AC SU QMAT
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:SU:QMAT", Set11AC_SU_RUQMat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:SU:QMAT:NTX", Set11AC_SU_RUQMatNtx, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:SU:QMAT:TYPE", Set11AC_SU_RUQMatType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:SU:QMAT:DELAy", Set11AC_SU_RUQMatDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:SU:QMAT:MAT", Set11AC_SU_RUQMatMap, 0, SCPI_SEQUENTIAL},
    // 11ax SU
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:STBC", Set11AX_SU_STBC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:ULDL", Set11AX_SU_ULDL, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:SREUse", Set11AX_SU_SpatialReuse, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:GLTF:SIZE", Set11AX_SU_GLTFSize, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:TXOP", Set11AX_SU_TXOP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:BSS:COLOr", Set11AX_SU_BSSColor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:DOPPler", Set11AX_SU_Doppler, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:MIDPeriod", Set11AX_SU_MidamblePeriodicity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:PE", Set11AX_SU_PE, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:CODing:TYPE", Set11AX_SU_Coding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:DCM", Set11AX_SU_DCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:MCS", Set11AX_SU_MCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:NSS", Set11AX_SU_NSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:BEAM:CHANge", Set11AX_SU_BeamChange, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:BEAM:FORMed", Set11AX_SU_Beamformed, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:BEAMchange", Set11AX_SU_BeamChange, 0, SCPI_SEQUENTIAL},//功能同上一条，历史问题原因，先保留（后续不用要删除）
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:BEAMformed", Set11AX_SU_Beamformed, 0, SCPI_SEQUENTIAL},//功能同上一条，历史问题原因，先保留（后续不用要删除）
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:SOUNding:NDP", Set11AX_SU_SoundingNDP, 0, SCPI_SEQUENTIAL},
    // 11ax SU QMAT
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:QMAT", Set11AX_SU_RUQMat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:QMAT:NTX", Set11AX_SU_RUQMatNtx, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:QMAT:TYPE", Set11AX_SU_RUQMatType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:QMAT:DELAy", Set11AX_SU_RUQMatDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:SU:QMAT:MAT", Set11AX_SU_RUQMatMap, 0, SCPI_SEQUENTIAL},
    // 11ax HE-MU
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:RUTPlan:USER", Set11AX_MU_MIMO_ToneUserCnt, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:RUTPlan:AND:USER?", Get11AX_MU_MIMO_ToneUserCnt, 0, SCPI_QUERY_CMD},

    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:RUTPlan", Set11AX_MU_TonePlan, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:RU:VALId", Set11AX_MU_RUValid, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:SIGB:MCS", Set11AX_MU_SIGBMCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:SIGB:DCM", Set11AX_MU_SIGBDCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:CENTer:RU", Set11AX_MU_CenterRU, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:STBC", Set11AX_MU_STBC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:ULDL", Set11AX_MU_ULDL, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:SREUse", Set11AX_MU_SpatialReuse, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:GLTF:SIZE", Set11AX_MU_GLTFSize, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:TXOP", Set11AX_MU_TXOP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:BSS:COLOr", Set11AX_MU_BSSColor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:DOPPler", Set11AX_MU_Doppler, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIDPeriod", Set11AX_MU_MidamblePeriodicity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:PE", Set11AX_MU_PE, 0, SCPI_SEQUENTIAL},
    // 11ax HE-MU RU
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:AID[:RU#]", Set11AX_MU_RUAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:POWer:FACT[:RU#]", Set11AX_MU_RUPowerFactor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:CODing:TYPE[:RU#]", Set11AX_MU_RUCoding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:DCM[:RU#]", Set11AX_MU_RUDCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MCS[:RU#]", Set11AX_MU_RUMCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:NSS[:RU#]", Set11AX_MU_RUNSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:BEAM:FORMed[:RU#]", Set11AX_MU_RUBeamformed, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:BEAMformed[:RU#]", Set11AX_MU_RUBeamformed, 0, SCPI_SEQUENTIAL},//功能同上一条，历史问题原因，先保留（后续不用要删除）
    // 11ax HE-MU MU-MIMO
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:FBANd", Set11AX_MU_MIMO_FullBand, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:RUTPlan", Set11AX_MU_MIMO_TonePlan, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:SIGB:MCS", Set11AX_MU_MIMO_SIGBMCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:SIGB:DCM", Set11AX_MU_MIMO_SIGBDCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:CENTer:RU", Set11AX_MU_MIMO_CenterRU, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:STBC", Set11AX_MU_MIMO_STBC, 0, SCPI_SEQUENTIAL}, //该命令目前wave工具没有配置
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:ULDL", Set11AX_MU_MIMO_ULDL, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:SREUse", Set11AX_MU_MIMO_SpatialReuse, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:GLTF:SIZE", Set11AX_MU_MIMO_GLTFSize, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:TXOP", Set11AX_MU_MIMO_TXOP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:BSS:COLOr", Set11AX_MU_MIMO_BSSColor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:DOPPler", Set11AX_MU_MIMO_Doppler, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:MIDPeriod", Set11AX_MU_MIMO_MidamblePeriodicity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:PE", Set11AX_MU_MIMO_PE, 0, SCPI_SEQUENTIAL},
    // 11ax HE-MU-MIMO RU
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:USER:COUNt[:RU#]", Set11AX_MU_MIMO_RUUserCnt, 0, SCPI_SEQUENTIAL},
    // 11ax HE-MU-MIMO QMAT
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:QMAT[:RU#]", Set11AX_MU_MIMO_RUQMat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:QMAT:NTX[:RU#]", Set11AX_MU_MIMO_RUQMatNtx, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:QMAT:TYPE[:RU#]", Set11AX_MU_MIMO_RUQMatType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:QMAT:DELAy[:RU#]", Set11AX_MU_MIMO_RUQMatDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:QMAT:MAT[:RU#]", Set11AX_MU_MIMO_RUQMatMap, 0, SCPI_SEQUENTIAL},
    // 11ax HE-MU-MIMO User in RU
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:AID[:RU#][:USER#]", Set11AX_MU_MIMO_RUAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:POWer:FACT[:RU#][:USER#]", Set11AX_MU_MIMO_RUPowerFactor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:CODing:TYPE[:RU#][:USER#]", Set11AX_MU_MIMO_RUCoding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:DCM[:RU#][:USER#]", Set11AX_MU_MIMO_RUDCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:MCS[:RU#][:USER#]", Set11AX_MU_MIMO_RUMCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:NSS[:RU#][:USER#]", Set11AX_MU_MIMO_RUNSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:BEAM:FORMed[:RU#][:USER#]", Set11AX_MU_MIMO_RUBeamformed, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:MU:MIMO:BEAMformed[:RU#][:USER#]", Set11AX_MU_MIMO_RUBeamformed, 0, SCPI_SEQUENTIAL},//功能同上一条，历史问题原因，先保留（后续不用要删除）

    // 11ax TB NDP
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:NDP:MODE", Set11AX_TB_MUMIMO_NDP_Mode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:NDP:STARting:AID", Set11AX_TB_MUMIMO_NDP_StartingAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:NDP:MULTplexing", Set11AX_TB_MUMIMO_NDP_Multiplexing, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:NDP:FEED:BACK:STATus", Set11AX_TB_MUMIMO_NDP_FeedbackStatus, 0, SCPI_SEQUENTIAL},

    // 11ax TB MU-MIMO
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:NLTF:SYMBols", Set11AX_TB_MUMIMO_NumLTFSymbols, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:GLTF:SIZE", Set11AX_TB_MUMIMO_GLTFSize, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:SREUse", Set11AX_TB_MUMIMO_SpatialReuse, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:TXOP", Set11AX_TB_MUMIMO_TXOP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:STBC", Set11AX_TB_MUMIMO_STBC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:LDPC:EXTRa:SYMBol", Set11AX_TB_MUMIMO_LDPCExtraSymobl, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:BSS:COLOr", Set11AX_TB_MUMIMO_BSSColor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:DOPPler", Set11AX_TB_MUMIMO_Doppler, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:MIDPeriod", Set11AX_TB_MUMIMO_MidamblePeriodicity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:PE", Set11AX_TB_MUMIMO_PE, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:LTF:MODE", Set11AX_TB_MUMIMO_LTFMode, 0, SCPI_SEQUENTIAL},
    // 11ax TB MU-MIMO RU
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:RU:COUNt[:SEGment#]", Set11AX_TB_MUMIMO_RUCnt, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:RUINdex[:RU#][:SEGment#]", Set11AX_TB_MUMIMO_RUIndex, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:NUMber:USER[:RU#][:SEGment#]", Set11AX_TB_MUMIMO_RUUserCnt, 0, SCPI_SEQUENTIAL},
    // 11ax TB MU-MIMO QMAT
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:QMAT[:RU#][:SEGment#]", Set11AX_TB_MUMIMO_RUQMat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:QMAT:NTX[:RU#][:SEGment#]", Set11AX_TB_MUMIMO_RUQMatNtx, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:QMAT:TYPE[:RU#][:SEGment#]", Set11AX_TB_MUMIMO_RUQMatType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:QMAT:DELAy[:RU#][:SEGment#]", Set11AX_TB_MUMIMO_RUQMatDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:QMAT:MAT[:RU#][:SEGment#]", Set11AX_TB_MUMIMO_RUQMatMap, 0, SCPI_SEQUENTIAL},
    // 11ax TB MU-MIMO User in RU
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:AID[:RU#][:USER#][:SEGment#]", Set11AX_TB_MUMIMO_RUUserAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:POWer:FACT[:RU#][:USER#][:SEGment#]", Set11AX_TB_MUMIMO_RUUserPowerFactor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:CODing:TYPE[:RU#][:USER#][:SEGment#]", Set11AX_TB_MUMIMO_RUUserCoding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:DCM[:RU#][:USER#][:SEGment#]", Set11AX_TB_MUMIMO_RUUserDCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:MCS[:RU#][:USER#][:SEGment#]", Set11AX_TB_MUMIMO_RUUserMCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:NSS[:RU#][:USER#][:SEGment#]", Set11AX_TB_MUMIMO_RUUserNSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:NSS:STARt[:RU#][:USER#][:SEGment#]", Set11AX_TB_MUMIMO_RUUserNSSStart, 0, SCPI_SEQUENTIAL},

    //11ax tb Time domain multi-user
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:BUILt:UP:MODE", Set11AX_TB_MUMIMO_UserBuiltUpMode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:CYCLic:SHIFt:DIVErsity:TIME[:RU#][:USER#][:SEGment#]", Set11AX_TB_MUMIMO_RUUserCSDTime, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:FREQuency:OFFSet[:RU#][:USER#][:SEGment#]", Set11AX_TB_MUMIMO_RUUserFreqOffset, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:TB:MU:POWer:SCALe[:RU#][:USER#][:SEGment#]", Set11AX_TB_MUMIMO_RUUserPowerScale, 0, SCPI_SEQUENTIAL},

    // 11ax TF PSDU
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:ULBW", Set11AX_TF_ULBW, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:STBC", Set11AX_TF_STBC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:NLTF:SYMBols", Set11AX_TF_NumLTFSymbols, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:SREUse", Set11AX_TF_SpatialReuse, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:GLTF:SIZE", Set11AX_TF_GLTFSize, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:LDPC:EXTRa:SYMBol", Set11AX_TF_LDPCExtra, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:UL:LENgth", Set11AX_TF_Length, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:DOPPler", Set11AX_TF_Doppler, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:MIDPeriod", Set11AX_TF_MidamblePeriodicity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:PE", Set11AX_TF_PE, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:POWer", Set11AX_TF_TxPower, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:MPAD", Set11AX_TF_AFactor, 0, SCPI_SEQUENTIAL},
    // be tf 
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:TRIGger:AP:TYPE", Set11AX_TF_TriggerAPType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:PRIMary:M160", Set11AX_TF_Primary160MFlag, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:SPECial:USER:INFO:FIELd:FLAG", Set11AX_TF_SpecialUserInfoFieldFlag, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:UL:BW:EXTEnsion", Set11AX_TF_TBULBandwideExtension, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:EHT:SREUse", Set11AX_TF_EHRSpatialReuse, 0, SCPI_SEQUENTIAL},

    // 11ax TF user
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:NUMber:USER", Set11AX_TF_UserNumber, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:AID[:USER#]", Set11AX_TF_UserAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:RUINdex[:USER#]", Set11AX_TF_UserRUIndex, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:SEGMent[:USER#]", Set11AX_TF_UserSegment, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:CODing:TYPE[:USER#]", Set11AX_TF_UserCoding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:DCM[:USER#]", Set11AX_TF_UserDCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:MCS[:USER#]", Set11AX_TF_UserMCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:SS:STARt[:USER#]", Set11AX_TF_UserSSStart, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:SS:COUNt[:USER#]", Set11AX_TF_UserSSCnt, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:RSSI[:USER#]", Set11AX_TF_UserRSSI, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:MPDU:MU:SFACtor[:USER#]", Set11AX_TF_UserSpacingFactor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:TID:AGG[:USER#]", Set11AX_TF_UserTIDAgg, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:PAC[:USER#]", Set11AX_TF_UserPrefferedAC, 0, SCPI_SEQUENTIAL},
    //be tf user
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:TF:PS160[:USER#]", Set11AX_TF_UserPS160, 0, SCPI_SEQUENTIAL},

    // 11ax er
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:MCS", Set11AX_ER_MCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:CODing:TYPE", Set11AX_ER_Coding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:GLTF:SIZE", Set11AX_ER_GLTFSize, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:DOPPler", Set11AX_ER_Doppler, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:BEAM:CHANge", Set11AX_ER_BeamChange, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:BEAMchange", Set11AX_ER_BeamChange, 0, SCPI_SEQUENTIAL},//功能同上一条，历史问题原因，先保留（后续不用要删除）
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:PE", Set11AX_ER_PE, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:TXOP", Set11AX_ER_TXOP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:ULDL", Set11AX_ER_ULDL, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:NSS", Set11AX_ER_NSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:DCM", Set11AX_ER_DCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:SREUse", Set11AX_ER_SpatialReuse, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:STBC", Set11AX_ER_STBC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:BSS:COLOr", Set11AX_ER_BssColor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:MIDPeriod", Set11AX_ER_MidamblePeriodicity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:BEAM:FORMed", Set11AX_ER_Beamformed, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:BEAMformed", Set11AX_ER_Beamformed, 0, SCPI_SEQUENTIAL},//功能同上一条，历史问题原因，先保留（后续不用要删除）
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:SOUNding:NDP", Set11AX_ER_SoundingNDP, 0, SCPI_SEQUENTIAL},
    // 11ax ER QMAT,实际使用结构和su是同一个，直接用su的函数，只是改下命令
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:QMAT", Set11AX_SU_RUQMat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:QMAT:NTX", Set11AX_SU_RUQMatNtx, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:QMAT:TYPE", Set11AX_SU_RUQMatType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:QMAT:DELAy", Set11AX_SU_RUQMatDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AX:ER:QMAT:MAT", Set11AX_SU_RUQMatMap, 0, SCPI_SEQUENTIAL},
    // PSDU MAC
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:PAYload:DATA[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDUPayloadData, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:PAYload:DATA:NO:CRC:AND:DELImiter[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDUPayloadDataWithoutCRCandDelimiter, 0, SCPI_SEQUENTIAL},

    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:PAYload:TYPE[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDUPayloadType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:SCRAmbler[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDUScrambler, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:CRC[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDUCRC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:LENgth[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDULength, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:BASE:BAND:TEST[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDU_BaseBandTest, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:MAC:HEADer[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDUMACEnable, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:MAC:FCTRl:USER[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDUMACFCtrlUser, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:MAC:FCTRl:DURation[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDUMACFCtrlDuration, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:MAC:FCTRl:SEQuence[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDUMACFCtrlSequence, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:MAC:ADDRess#[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDUMACAddress, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:MPDU[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDUMPDU, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:RANDom:SEED[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDURandomSeed, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:PSDU:EOF:PADding:TYPE[:RU#][:USER#][:SEGment#]", SetWaveGen_PSDU_EOFPaddingType, 0, SCPI_SEQUENTIAL},

    // WIFI user defined SIG field
    {"WT:WIFI:SOURce:CONFigure:WAVE:USER:DEFined:PHY:SIGA", SetWaveGen_UserDefined_SIGA, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:USER:DEFined:PHY:LSIG", SetWaveGen_UserDefined_LSIG, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:USER:DEFined:PHY:HT:SIG", SetWaveGen_UserDefined_HT_SIG, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:USER:DEFined:PHY:VHT:SIGB", SetWaveGen_UserDefined_VHT_SIGB, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:USER:DEFined:PHY:HE:SIGB", SetWaveGen_UserDefined_HE_SIGB, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:USER:DEFined:PHY:EHT:SIG", SetWaveGen_UserDefined_EHT_SIG, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:USER:DEFined:PHY:EHT:USIG", SetWaveGen_UserDefined_EHT_USIG, 0, SCPI_SEQUENTIAL},

    //Post FEC padding
    {"WT:WIFI:SOURce:CONFigure:WAVE:USER:DEFined:PHY:HE:POST:FEC:PADding[:RU#][:USER#][:SEGment#]", SetWaveGen_UserDefined_HE_PostFECPadding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:USER:DEFined:PHY:EHT:POST:FEC:PADding[:RU#][:USER#][:SEGment#]", SetWaveGen_UserDefined_EHT_PostFECPadding, 0, SCPI_SEQUENTIAL},
    // BT
    {"WT:BT:SOURce:CONFigure:WAVE:SIGnal:TYPE", SetWaveGen_BT_SignalType, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:CRC", SetWaveGen_BT_CRC, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BLE:ENHAnced:MODE", SetWaveGen_BT_BLE_EnhancedMode, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:PHY:MODUlation", SetWaveGen_BT_ModulationIndex, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:PHY:FILTer:PRODuct", SetWaveGen_BT_ProductIndex, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:PHY:EDR:FILTer", SetWaveGen_BT_EDRFliter, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BR:EDR:PHY:GUARd", SetWaveGen_BT_EDRGuardTime, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BR:EDR:DEVice:ADDRess:LAP", SetWaveGen_BT_BR_EDR_LAP, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BR:EDR:DEVice:ADDRess:UAP", SetWaveGen_BT_BR_EDR_UAP, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BR:EDR:DEVice:ADDRess:NAP", SetWaveGen_BT_BR_EDR_NAP, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BR:EDR:PACKer:HEADer:LTADdress", SetWaveGen_BT_BR_EDR_LTAddress, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BR:EDR:PACKer:HEADer:FLCOntrol", SetWaveGen_BT_BR_EDR_FlowControl, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BR:EDR:PACKer:HEADer:ACK", SetWaveGen_BT_BR_EDR_ACKReponse, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BR:EDR:PACKer:HEADer:SEQNumber", SetWaveGen_BT_BR_EDR_SequenceNum, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BR:EDR:PAYLoad:LLID", SetWaveGen_BT_BR_EDR_PayLoadLLID, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:PAYLoad:TYPE", SetWaveGen_BT_PayLoadType, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BR:EDR:PAYload:FLOW", SetWaveGen_BT_BR_EDR_PayLoadFlow, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BR:EDR:PAYload:WHITening", SetWaveGen_BT_BR_EDR_PayLoadWhitening, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:PAYload:SIZE", SetWaveGen_BT_PayLoadSize, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:PACKer:RAMPtime", SetWaveGen_BT_PacketRampTime, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BLE:PACKet:SYNCword", SetWaveGen_BT_LESynWord, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BLE:MAPPers", SetWaveGen_BT_LEMappers, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:FHS:PAYload:EIR", SetWaveGen_BT_FHS_PayloadEIR, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:FHS:PAYload:SR", SetWaveGen_BT_FHS_PayloadSR, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:FHS:PAYload:CLASs:OF:DEVice", SetWaveGen_BT_FHS_PayloadClassOfDevice, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:FHS:PAYload:LT:ADDRess", SetWaveGen_BT_FHS_LTAddress, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:FHS:PAYload:CLK27b2", SetWaveGen_BT_FHS_PayloadClock27b2, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:DV:PAYload:VOICe:FIELd", SetWaveGen_BT_DV_PayloadVoiceField, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BLE:CTE:FLAG", SetWaveGen_BT_BLE_CTEFlag, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BLE:CTE:TIME", SetWaveGen_BT_BLE_CTETime, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BLE:CTE:TYPE", SetWaveGen_BT_BLE_CTEType, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BLE:ANTEnna:GAIN", SetWaveGen_BT_BLE_AntennaGain, 0, SCPI_SEQUENTIAL},

    //BLE 数据帧广播包
    //{"WT:BT:SOURce:CONFigure:WAVE:BLE:MAPPers", SetWaveGen_BT_LEMappers, 0, SCPI_SEQUENTIAL}, //BLE test和广播包要配置该值，归类备注下
    {"WT:BT:SOURce:CONFigure:WAVE:BLE:ADVErtise:PACKet:ACCEss:ADDRess", SetWaveGen_BT_BLE_Advertise_AccessAddress, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:WAVE:BLE:ADVErtise:PACKet:CHANnel:INDEx", SetWaveGen_BT_BLE_Advertise_ChannelIndex, 0, SCPI_SEQUENTIAL},

    {"WT:BT:SOURce:CONFigure:WAVE:PDU:PAYload:DATA", SetWaveGen_BT_PDUPayloadData, 0, SCPI_SEQUENTIAL},

    // GPRF
    {"WT:CW:SOURce:CONFigure:WAVE:PSDU:LENgth", SetWaveGen_CW_psduLength, 0, SCPI_SEQUENTIAL},
    {"WT:GPRF:SOURce:CONFigure:WAVE:PSDU:LENgth", SetWaveGen_CW_psduLength, 0, SCPI_SEQUENTIAL},
    {"WT:GPRF:SOURce:CONFigure:WAVE:TYPE", SetWaveGen_CW_Type, 0, SCPI_SEQUENTIAL},

    // Seting extern data to waveform and save
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:EXTern:SETting:DATA", SetPNFileExternSettingData, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SOURce:CONFigure:WAVE:CLS:EXTern:SETting:DATA", CleanPNFileExternSettingData, 0, SCPI_SEQUENTIAL},

    // SparkLink (gle)
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:FRAMe:TYPE", SetWaveGen_GLE_FrameType, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:PHY:ID", SetWaveGen_GLE_PhyID, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:SLOT:ID", SetWaveGen_GLE_SlotID, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CRC:DATA:SEED", SetWaveGen_GLE_CrcDataSeed, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:M:ID", SetWaveGen_GLE_MID, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:DATA:TYPE", SetWaveGen_GLE_DataType, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:PILOt:DENSity", SetWaveGen_GLE_PilotDensity, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:MODulation:INDEx", SetWaveGen_GLE_ModulationInsex, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:SCRAmble:TYPE", SetWaveGen_GLE_ScarmbleType, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:TYPE", SetWaveGen_GLE_CtrlInfoType, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:FREQ:BAND", SetWaveGen_GLE_FreqBand, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CHANnel:ID", SetWaveGen_GLE_ChannelNo, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:SYNC:SOURce", SetWaveGen_GLE_SyncSource, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:SYNC:SEQ", SetWaveGen_GLE_SyncSeq, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:SCHEslot:LEN", SetWaveGen_GLE_ScheSlotLen, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:SCHEslot:NUM", SetWaveGen_GLE_ScheSlotNum, 0, SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:A1:BROadcast:TYPE", SetWaveGen_GLE_A1_BroadcastType, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:A6:END:INDicate", SetWaveGen_GLE_A6_EndInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:A#]:MCS", SetWaveGen_GLE_A1__A7_MCS, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:A#]:EMPTy:PACKet:INDicate", SetWaveGen_GLE_A2__A5_EmptyPacketInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:A#]:SEND:AND:RECV:SN", SetWaveGen_GLE_A2__A5_SendAndRecvSN, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:A#]:FLOW", SetWaveGen_GLE_A2__A5_FlowCtrlInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:A#]:RECV:SYStem:FRAMe:INDEx", SetWaveGen_GLE_A2_A6_FrameInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:A#]:PACKet:TYPE", SetWaveGen_GLE_A1__A6_PacketType, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:A#]:DATA:LENgth", SetWaveGen_GLE_A1__A7_DataLen, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:A#]:SCHedule:INDicate", SetWaveGen_GLE_A3__A5_SchedileInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:A#]:DATA:PACKet:SN:AND:GROUp", SetWaveGen_GLE_A6__A7_SNAndGroup, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:B#]:MCS", SetWaveGen_GLE_B1_B3__B5_MCS, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:B#]:DATA:LENgth", SetWaveGen_GLE_B1_B3__B5_DataLen, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:B#]:FLOW", SetWaveGen_GLE_B1__B4_CtrlInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:B#]:DATA:PACKet:SN", SetWaveGen_GLE_B1_B3_B4_DataPacketSN, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:B#]:UPPer:LINK:INDicate", SetWaveGen_GLE_B1_B2_LinkInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:B#]:HARQ:FEEDback", SetWaveGen_GLE_B1_B2_Feedback, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:B1:FRAMe:FORMat:INDicate", SetWaveGen_GLE_B1_FormatInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:B3:DATA:PACKet:GROUp", SetWaveGen_GLE_B3_DataPacketGroup, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO[:B#]:MAX:DATA:PACKet:SN:INDicate", SetWaveGen_GLE_B3_B4_MaxDataPacketSN, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:B4:BROadcast:FLAG", SetWaveGen_GLE_B4_SetFlag, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:B4:BROadcast:UP:DATE:INDicate", SetWaveGen_GLE_B4_UpdataInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:B5:MESSage:TYPE:INDicate", SetWaveGen_GLE_B5_MestypeInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:B5:CONNect:INDicate", SetWaveGen_GLE_B5_ConnInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:B5:DIScover:INDicate", SetWaveGen_GLE_B5_DiscoverInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:B5:DIREct:INDicate", SetWaveGen_GLE_B5_DirectInd, 0 ,SCPI_SEQUENTIAL},
    {"WT[:GLE][:SLE]:SOURce:CONFigure:WAVE:CTRL:INFO:B5:DATA:UP:DATE:INDicate", SetWaveGen_GLE_B5_DataUpdataInd, 0 ,SCPI_SEQUENTIAL},

    //WISUN
    // OFDM
    {"WT:WSUN:SOURce:CONFigure:WAVE:SIGnal:TYPE", SetWaveGen_WISUN_SignalType, 0, SCPI_SEQUENTIAL},
    
    {"WT:WSUN:SOURce:CONFigure:WAVE:OPTIon", SetWaveGen_WISUN_Option, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:MCS", SetWaveGen_WISUN_MCS, 0, SCPI_SEQUENTIAL},

    {"WT:WSUN:SOURce:CONFigure:WAVE:SCRAmbler", SetWaveGen_WISUN_Scrambler, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PHY:OFDM:INTEr:LEAVing", SetWaveGen_WISUN_PhyOFDMInterleaving, 0, SCPI_SEQUENTIAL},
     
    //OQPSK
    {"WT:WSUN:SOURce:CONFigure:WAVE:FREQuency:BAND", SetWaveGen_WISUN_FrequencyBand, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:SPREading:MODE", SetWaveGen_WISUN_SpreadingMode, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:RATE:MODE", SetWaveGen_WISUN_RateMode, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:CHIP:RATE", SetWaveGen_WISUN_ChipRate, 0, SCPI_SEQUENTIAL},

    //FSK
    {"WT:WSUN:SOURce:CONFigure:WAVE:PHYSics:FSK:PREAmble:LENGth", SetWaveGen_WISUN_PhyFSKPreambleLength, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:MODE:SWITch", SetWaveGen_WISUN_ModeSwitch, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PHYSics:SUN:FSK:SFD", SetWaveGen_WISUN_PhySunFSKSfd, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:FCS:TYPE", SetWaveGen_WISUN_FCSType, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PHYSics:FSK:FEC:ENABled", SetWaveGen_WISUN_PhyFSKFecEnabled, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PHYSics:FSK:FEC:SCHEme", SetWaveGen_WISUN_PhyFSKFecScheme, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PHYSics:FSK:FEC:INTEr:LEAVing:RSC", SetWaveGen_WISUN_PhyFSKFecInterLeavingRsc, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PHYSics:FSK:SCRAmble:PSDU", SetWaveGen_WISUN_PhyFSKScramblePsdu, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:DATA:RATE", SetWaveGen_WISUN_DataRate, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:MODUlation", SetWaveGen_WISUN_Modulation, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:MODUlation:INDEx", SetWaveGen_WISUN_ModulationIndex, 0, SCPI_SEQUENTIAL},

    
    //wisun公共
    {"WT:WSUN:SOURce:CONFigure:WAVE:PSDU:TYPE", SetWaveGen_WISUN_PSDU_PsduType, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PSDU:LENGth", SetWaveGen_WISUN_PSDU_PsduLength, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PSDU:CRC:CHECk:ENABle", SetWaveGen_WISUN_PSDU_CRCEnable, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PSDU:MAC:HEADer:ENABle", SetWaveGen_WISUN_PSDU_MacEnable, 0, SCPI_SEQUENTIAL},

    {"WT:WSUN:SOURce:CONFigure:WAVE:PSDU:FRAMe:CTRL", SetWaveGen_WISUN_PSDU_FrameCtrl, 0, SCPI_SEQUENTIAL},
    
    {"WT:WSUN:SOURce:CONFigure:WAVE:PSDU:DESTination:PAN:ID", SetWaveGen_WISUN_PSDU_Destination_PANID, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PSDU:DESTination:ADDRess", SetWaveGen_WISUN_PSDU_Destination_Address, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PSDU:SOURce:PAN:ID", SetWaveGen_WISUN_PSDU_Source_PANID, 0, SCPI_SEQUENTIAL},
    {"WT:WSUN:SOURce:CONFigure:WAVE:PSDU:SOURce:ADDRess", SetWaveGen_WISUN_PSDU_Source_Address, 0, SCPI_SEQUENTIAL},

};

static const scpi_command_t scpi_wt_get_ofdma_result_cmd[] = {
    // OFDMA信息，非MU-MIMO
    {"WT:WIFI:SENSe:FETCh:OFDM:RU:USER:INFO?", GetVsaRstOfdmaInfo, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:VALId?", GetVsaRstOFDMA_UserValid, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:ULDL?", GetVsaRstOFDMA_ULDL, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:TONE:WIDE?", GetVsaRstOFDMA_ToneWide, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:TONE:INDEx?", GetVsaRstOFDMA_ToneIndex, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:SEGMent?", GetVsaRstOFDMA_Segment, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:MCS?", GetVsaRstOFDMA_MCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:MODulation:TYPE?", GetVsaRstOFDMA_Modulation, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:CODing:TYPE?", GetVsaRstOFDMA_CodingType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:CODing:RATE?", GetVsaRstOFDMA_CodingRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:STBC?", GetVsaRstOFDMA_STBC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:DCM?", GetVsaRstOFDMA_DCM, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:NSTS?", GetVsaRstOFDMA_NSTS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:NSS?", GetVsaRstOFDMA_NSS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:PSDU:CRC?", GetVsaRstOFDMA_PSDUCRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:PSDU:LENgth?", GetVsaRstOFDMA_PSDULen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:AID?", GetVsaRstOFDMA_AID, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:BEAM:FORMed?", GetVsaRstOFDMA_Beamformed, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:BEAMformed?", GetVsaRstOFDMA_Beamformed, 0, SCPI_QUERY_CMD},//功能同上一条，历史问题原因，先保留（后续不用要删除）
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:POWer:FACTor?", GetVsaRstOFDMA_PowerFactor, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:DATA:RATE?", GetVsaRstOFDMA_DataRate, 0, SCPI_QUERY_CMD},
    //下面这几个命令不能合成一个命令类似RU#[:NSS#]模式。SCPI库会把可选NSS默认值配置成1，所以分开写
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:POWer?", GetVsaRstOFDMA_Power, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:NSS#:POWer?", GetVsaRstOFDMA_Power, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:EVM:ALL?", GetVsaRstOFDMA_EVMAll, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:NSS#:EVM:ALL?", GetVsaRstOFDMA_EVMAll, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:EVM:DATA?", GetVsaRstOFDMA_EVMData, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:NSS#:EVM:DATA?", GetVsaRstOFDMA_EVMData, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:EVM:PILOt?", GetVsaRstOFDMA_EVMPilot, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:NSS#:EVM:PILOt?", GetVsaRstOFDMA_EVMPilot, 0, SCPI_QUERY_CMD},

};

static const scpi_command_t scpi_wt_8080_2port_cmd[] = {
    // WIFI 8080双端口
    {"WT:WIFI:SENSe:CONFigure:SEGMent:MODE", SetVsa8080Mode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:SEGMent:RFPOrt", SetVsa8080Port, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:SEGMent:POWer:REFerence", SetVsa8080RefPower, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:SEGMent:TRIGger:LEVEl", SetVsa8080TriggerLevel, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:SEGMent:MODE?", GetVsa8080Mode, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:CONFigure:SEGMent:RFPOrt?", GetVsa8080Port, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:CONFigure:SEGMent:POWer:REFerence?", GetVsa8080RefPower, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:CONFigure:SEGMent:TRIGger:LEVEl?", GetVsa8080TriggerLevel, 0, SCPI_QUERY_CMD},

    {"WT:WIFI:SOURce:CONFigure:SEGMent:MODE", SetVsg8080Mode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:SEGMent:RFPOrt", SetVsg8080Port, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:SEGMent:POWer", SetVsg8080Power, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:SEGMent:MODE?", GetVsg8080Mode, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SOURce:CONFigure:SEGMent:RFPOrt?", GetVsg8080Port, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SOURce:CONFigure:SEGMent:POWer?", GetVsg8080Power, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_get_wifi_psdu_cmd[] = {
    // WIFI PSDU 信息
    {"WT:WIFI:SENSe:FETCh:PSDU:MAC:FCTRL:USER?", GetVsaRst_PSDU_FCtrlUser, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:MAC:FCTRL:DURAtion?", GetVsaRst_PSDU_FCtrlDuration, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:MAC:FCTRL:SEQuence?", GetVsaRst_PSDU_FCtrlSequence, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:MAC:ADDR#?", GetVsaRst_PSDU_MAC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:SCRambler?", GetVsaRst_PSDU_Scrambler, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:SERVice:FIELd:INFO?", GetVsaRst_PSDU_ServiceFieldInfo, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:MPDU?", GetVsaRst_PSDU_MPDU, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:BITS:DECOde:INFO?", GetVsaRst_PSDU_tshark, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:DECOde:INFO[:RU#][:USER#][:MPDU#]?", GetVsaRst_PSDU_tshark_V2, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:STReam:BITS:INFO?", GetVsaRst_ExportPSDUBits, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:STReam:BITS:DECOmposed:INFO[:RU#][:USER#]?", GetVsaRst_ExportPSDUBitsDecomposedInfo, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:MPDU:EOF:PADding:STReam:BITS:INFO?", GetVsaRst_PSDU_MPDU_EofPadding, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:DECOde:MSDU:INFO[:RU#][:USER#][:MPDU#]?", GetVsaRst_MSDU_tshark, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:DECRypt:INFO[:RU#][:USER#][:MPDU#]?", GetVsaRst_MSDU_DecryptDescription, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:DECOde:MSDU:BITS:INFO[:RU#][:USER#][:MPDU#]?", GetVsaRst_MSDUBits, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:LDPC:ERROr:CORRect:FLAG?", GetVsaRst_PSDU_LDPCErrorCorrectFlag, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:APEP:LENgth?", GetVsaRst_PSDU_APEPLength, 0, SCPI_QUERY_CMD},

};

static const scpi_command_t scpi_wt_wifi_ibf_cmd[] = {
    // IBF信息
    {"WT:WIFI:SENSe:FETCh:IBF:CALibration:CHANnel", SetVsaRstIBFCalibrationChannelEstDutTX, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:IBF:CALibration:CHANnel", SetVsaRstIBFCalibrationChannelEstDutRX, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:FETCh:IBF:ANGle?", GetVsaRstIBFCalibrationResult, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:IBF:DIFF:POWer?", GetVsaRstIBFPowerVerification, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:CONFigure:IBF:CALibration:PROFile", GetVsaRstIBFCalculateChannelProfile, 0, SCPI_SEQUENTIAL},//该条后续考虑删除
    //WT:WIFI:SENSe:FETCh:IBF:CALibration:PROFile?功能同WT:WIFI:SENSe:CONFigure:IBF:CALibration:PROFile，但WT:WIFI:SENSe:CONFigure:IBF:CALibration:PROFile命令
    //命名不符合实际内容，但已经发布有客户在使用，所以先保留代码，后续再考虑删除
    {"WT:WIFI:SENSe:FETCh:IBF:CALibration:PROFile?", GetVsaRstIBFCalculateChannelProfile, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:IBF:CALibration:CHANnel:AMPlitude:AND:ANGle?", GetVsaRstIBFCalculateChannelAmplitudeAngle, 0, SCPI_QUERY_CMD},//调用完WT:WIFI:SENSe:FETCh:IBF:CALibration:CHANnel分析后的结果获取

};

static const scpi_command_t scpi_wt_vsg_devm_cmd[] = {
    //VSG DEVM
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:DEVM:DUT:DUTY:RADIo", VsgDevmDutDutyRadio, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:DEVM:LEAD:TIME", VsgDevmLeadingTime, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:DEVM:DELAy:TIME", VsgDevmDelayTime, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:DEVM:MODE", VsgDevmEnable, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_wifi_tf_tb_pair_cmd[] = {
    // TB测试，仪器模拟AP
    {"WT:WIFI:TFTB:INITiate", SetTBInit_TesterAsAP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:TFTB:INITiate?", GetTBInit_TesterAsAP, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:TFTB:TERMinal", SetTBRelease_TesterAsAP, 0, SCPI_SEQUENTIAL},
    
    {"WT:WIFI:TFTB:STARt", SetTBStart_TesterAsAP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:TFTB:ASYN:STARt", SetTBAsynStart_TesterAsAP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:TFTB:STOP", SetTBStop_TesterAsAP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:TFTB:STATus?", GetTBStatus_TesterAsAP, 0, SCPI_QUERY_CMD},
    // TF测试，仪器模拟STA
    {"WT:WIFI:TBTF:INITiate", SetTFInit_TesterAsSTA, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:TBTF:INITiate?", GetTFInit_TesterAsSTA, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:TBTF:TERMinal", SetTFRelease_TesterAsSTA, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:TBTF:STARt", SetTFStart_TesterAsSTA, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:TBTF:ASYN:STARt", SetTFAsynStart_TesterAsSTA, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:TBTF:STOP", SetTFStop_TesterAsSTA, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:TBTF:STATus?", GetTFStatus_TesterAsSTA, 0, SCPI_QUERY_CMD},
    // TB TF
    {"WT:WIFI[:TBTF][:TFTB]:AGC", SetTBAGC_TesterAsAP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI[:TBTF][:TFTB]:FRAMe:DELAy", SetTFDelay_TesterAsSTA, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:FETCh:DIGtal:IQ:TFTB:SIFS?", GetInterModeSIFS_VSGtoVSA, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:RF:INTER:SIFS?", GetInterModeSIFS_VSGtoVSA, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_digtal_iq_cmd[] = {
    {"WT:DIGtal:IQ:CONFigure:TRIGer:TMO", SCPI_DigIQ_SetTriggerTimeOut, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:RECV:TMO", SCPI_DigIQ_SetRecvTimeOut, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:DEST:MAC", SCPI_DigIQ_SetDestMac, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:VSG:PACKet:TMO", SCPI_DigIQ_SetVSGPacketTimeOut, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:VSA:PACKet:TMO", SCPI_DigIQ_SetVSAPacketTimeOut, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:FIXture:MODE", SCPI_DigIQ_SetFixtureMode, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:FIXture:TRIGer:CNT", SCPI_DigIQ_SetFixtureTriggerCnt, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:FIXture:TRIGer:GAP", SCPI_DigIQ_SetFixtureTriggerGap, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:FIXture:TRIGer:PERIod", SCPI_DigIQ_SetFixtureTriggerPeriod, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:FIXture:SEND:TMO", SCPI_DigIQ_SetFixtureSendTimeOut, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:FIXture:PACKet:DELAy", SCPI_DigIQ_SetFixtureDelayPerPacket, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:GO", SCPI_DigIQ_SetApply, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:FIXture:GO", SCPI_DigIQ_SetFixtureApply, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:SENSe:ACTIon", SCPI_DigIQ_SetInterVsaAction, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:SOURce:ACTIon", SCPI_DigIQ_SetInterVsgAction, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:SENSe:MAX:BITS:COUNt", SCPI_DigIQ_SetVsaMaxBitCount, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:SOURce:MAX:BITS:COUNt", SCPI_DigIQ_SetVsgMaxBitCount, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:VSA:CELL:CHANnel:LIST", SCPI_DigIQ_SetVSACellChannelList, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:VSG:CELL:CHANnel:LIST", SCPI_DigIQ_SetVSGCellChannelList, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:VSA:CELL:CHANnel:LIST?", SCPI_DigIQ_GetVSACellChannelList, 0, SCPI_QUERY_CMD},
    {"WT:DIGtal:IQ:CONFigure:VSG:CELL:CHANnel:LIST?", SCPI_DigIQ_GetVSGCellChannelList, 0, SCPI_QUERY_CMD},
    {"WT:DIGtal:IQ:CONFigure:SOURce:WAVE:RATIo", SCPI_DigIQ_SetVSGWaveRatio, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:SENSe:PACKet:COUNt?", SCPI_DigIQ_GetVsaPacketCnt, 0, SCPI_QUERY_CMD},
    {"WT:DIGtal:IQ:CONFigure:SOURce:PACKet:COUNt?", SCPI_DigIQ_GetVsgPacketCnt, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_pac_cmd[] = {
    // PAC测试
    {"WT:PAC:CONFigure:SENSe:RFPOrt", PAC_SetVsaPort, 0, SCPI_SEQUENTIAL},
    {"WT:PAC:CONFigure:SENSe:MAXPower", PAC_SetVsaRefPowerLevel, 0, SCPI_SEQUENTIAL},
    {"WT:PAC:CONFigure:SENSe:SMPTime", PAC_SetVsaSmpTime, 0, SCPI_SEQUENTIAL},
    {"WT:PAC:CONFigure:SOURce:RFPOrt", PAC_SetVsgPort, 0, SCPI_SEQUENTIAL},
    {"WT:PAC:CONFigure:SOURce:POWer", PAC_SetVsgPower, 0, SCPI_SEQUENTIAL},
    {"WT:PAC:CONFigure:MODE", PAC_SetMode, 0, SCPI_SEQUENTIAL},
    {"WT:PAC:CONFigure:AVG", PAC_SetAvgCnt, 0, SCPI_SEQUENTIAL},
    {"WT:PAC:CONFigure:FREQuency", PAC_SetFreqList, 0, SCPI_SEQUENTIAL},
    {"WT:PAC:CONFigure:INIT", PAC_SetStarGetData, 0, SCPI_SEQUENTIAL},
    {"WT:PAC:GET:PATH:LOSS:DATA?", PAC_GetCalPathLoss, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_wifi_cmimo_cmd[] = {
    // CMIMO
    {"WT:WIFI:SENSe:SAVE:COMPosite:MIMO:REF:FILE", SetVsaSaveCMIMORefFile, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:COMPosite:MIMO:REF:FILE", SetVsaAnalyzeCMIMORefFile, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:COMPosite:MIMO:REF:FILE?", GetVsaAnalyzeCMIMORefFile, 0, SCPI_QUERY_CMD},

};

static const scpi_command_t scpi_wt_get_wifi_common_result_cmd[] = {
    // data information
    {"WT[:WIFI][:BT][:GLE][:SLE][:WSUN][:ZWAVe]:SENSe:FETCh:DATA:INFO?", GetVsaDataInfo, 0, SCPI_QUERY_CMD},
    {"WT[:GLE][:SLE]:SENSe:FETCh:CTR:DATA:INFO?", GetVsaSLECTRInfo, 0, SCPI_QUERY_CMD},
    //载波泄漏
    {"WT:WIFI:SENSe:FETCh:OFDM:LEAGkage?", GetVsaRstSpectCarrierLeakage, 0, SCPI_QUERY_CMD},
    //获取WIFI VSA结果
    {"WT:WIFI:SENSe:FETCh:IQ:MATCh:AMP?", GetVsaRstIQMatchAmp, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:IQ:MATCh:PHASe?", GetVsaRstIQMatchPhase, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:ZIGBEE]:SENSe:FETCh:IQ:PHASe:ERROr?", GetVsaRstIQMatchPhaseError, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:DATA:RATE:MBPS?", GetVsaRstDataRateMbps, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:RAMP:ON:TIME?", GetVsaRstRampOnTime, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:RAMP:OFF:TIME?", GetVsaRstRampOffTime, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:NUMber:SYMBols?", GetVsaRstOfdmNumberSymbols, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:EVM:DATA:DB?", GetVsaRstEvmDataDb, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:EVM:PILOt:DB?", GetVsaRstEvmPilotDb, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN][:LTE][:NR]:SENSe:FETCh:FLATness:PASSed?", GetVsaRstFlatnessPassed, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:OFDM:FLATness:SECTion:VALUe?", GetVsaRstOfdmFlatnessSectionValue, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:OFDM:FLATness:SECTion:MARGin?", GetVsaRstOfdmFlatnessSectionMargin, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:DSSS:IQ:OFFSet?", GetVsaRstDsssIQOffset, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:DSSS:CARRier:SUPPression?", GetVsaRstDsssCarrierSuppression, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:LENgth?", GetVsaRstPsduLength, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:PSDU:CRC?", GetVsaRstPsduCRC, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:OFDM:EVM:MARGin?", GetVsaRstEvmMargin, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:OFDM:LEAGkage:MARGin?", GetVsaRstLeagkageMargin, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:OFDM:FREQerr:MARGin?", GetVsaRstFreqerrMargin, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:OFDM:CLOCkerr:MARGin?", GetVsaRstClockErrMargin, 0, SCPI_QUERY_CMD},

    {"WT:WIFI:SENSe:FETCh:MIMO:POWer:TABLe?", GetVsaRstMimoPowerTable, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_vsg_analyze_param_cmd[] = {
    // VSG分析参数设置，给VSG视图使用
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:CONFigure:ANALy:TIME:OUT", SetVsgAlzTimeOut, 0, SCPI_SEQUENTIAL},
    {"WT:SOURce:CONFigure:ANALy:WAVE", VsgAlzWaveForm, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:WSUN]:SOURce:CONFigure:ANALy:DEMOd", SetVsgAlzDemod, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:ANALy:OFDM:PH:CORR", SetVsgOFDMPhaseTracking, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:ANALy:OFDM:CH:ESTImate", SetVsgOFDMChannelEstimation, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:ANALy:OFDM:SYM:TIME:CORR", SetVsgOFDMTimingTracking, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:ANALy:OFDM:FREQ:SYNC", SetVsgOFDMFrequencySync, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:ANALy:OFDM:AMPL:TRACk", SetVsgOFDMAmplitudeTracking, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:ANALy:DSSS:EVM:METHod", SetVsgDSSSEvmMethod, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:ANALy:DSSS:DC:REMOval", SetVsgDSSSDCRemove, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:ANALy:DSSS:EQ:TAPS", SetVsgDSSSEqualizerTypes, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:ANALy:DSSS:PH:CORR", SetVsgDSSSPhaseTracking, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:ANALy:SPECtrum:MASK:VERSion", SetVsg11nSpectrumMaskVersion, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:ANALy:CLOCk:RATE", SetVsgAlzClockRate, 0, SCPI_SEQUENTIAL},
    {"WT:GPRF:SOURce:CONFigure:ANALy:RBW", SetVsgAlzRBW, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:ANALy:BTRAte", SetVsgBTAlzRate, 0, SCPI_SEQUENTIAL},
    {"WT:BT:SOURce:CONFigure:ANALy:PACKet:TYPE", SetVsgBTAlzPacketType, 0, SCPI_SEQUENTIAL},
    {"WT:ZIGBee:SOURce:CONFigure:ANALy:OPTImise", SetVsgZigbeeAnalyzeOptimise, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE][:NIOT][:NR]:SOURce:CONFigure:ANALy:LINK:DIREct", SetVsgAlzLinkDirect, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE][:NIOT][:NR]:SOURce:CONFigure:ANALy[:STREam#]:RFBAnd", SetVsgAlzRFBand, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE][:NIOT][:NR]:SOURce:CONFigure:ANALy[:STREam#]:RFCHannel", SetVsgAlzRFChannel, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE][:NIOT][:NR]:SOURce:CONFigure:ANALy:POWEr:ENABle", SetVsgAlzPowerGraphEnable, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE][:NIOT][:NR]:SOURce:CONFigure:ANALy:SPECtrum:ENABle", SetVsgAlzSpectrumEnable, 0, SCPI_SEQUENTIAL},
    {"WT[:LTE][:NIOT][:NR]:SOURce:CONFigure:ANALy:CCDF:ENABle", SetVsgAlzCCDFEnable, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_vsg_result_cmd[] = {
    // VSG分析结果，只提供部分结果
    //获取算法的ADC频率值,采样率~
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:LTE][:NIOT]:SOURce:FETCh:ANALy:ADC:SMP:FREQuency?", GetVsgRstAlzAdcSMPFreq, 0, SCPI_QUERY_CMD},

    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:SPECtrum:OBW99?", GetVsgRstSpectOBW99, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:SPECtrum:MASK:ERROr:PERCent?", GetVsgRstSpectMaskErrorPercent, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:SPECtrum:PEAK:FREQuency?", GetVsgRstSpectPeakFrequency, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:LTE][:NIOT]:SOURce:FETCh:SPECtrum:DATA?", GetVsgRstSpectrumData, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:SPECtrum:MASK:DATA?", GetVsgRstSpectrumMaskData, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NIOT]:SOURce:FETCh:SPECtrum:MARGin?", GetVsgRstSpectMargin, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:LTE][:NIOT]:SOURce:FETCh:SPECtrum:FREQuency:SPAN?", GetVsgRstSpectrumSpan, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:LTE][:NIOT]:SOURce:FETCh:SPECtrum:RBW?", GetVsgRstSpectrumRBW, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:POINts:POWEr?", GetVsgRstPointPower, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:POINts:AVG:POWEr?", GetVsgRstPointAvgPower, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:POINts:IQ:DATA?", GetVsgRstPointIQ, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:WAVE:PN:DESCription?", GetWaveDescription, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:WAVE:PN:STRUct:DATA?", GetWavePnStructData, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:WAVE:PN:EXTern:SETting:DATA?", GetWaveExternSettingData, 0, SCPI_QUERY_CMD},

    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:POINts:POWEr:LITE?", GetVsgRstPointPowerLite, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:POINts:AVG:POWEr:LITE?", GetVsgRstPointAvgPowerLite, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SOURce:FETCh:POINts:IQ:DATA:LITE?", GetVsgRstPointIQLite, 0, SCPI_QUERY_CMD},

};

static const scpi_command_t scpi_wt_calibration_cmd[] = {
    {"WT:CONFigure:CALibration:MODE#", SCPI_SetCalibrationMode, 0, SCPI_SEQUENTIAL},
    {"WT:CONFigure:CALibration:MODE#?", SCPI_GetCalibrationMode, 0, SCPI_QUERY_CMD},
    {"WT:CONFigure:CALibration:RELoad", SCPI_ReLoadCalibrationData, 0, SCPI_SEQUENTIAL},
    {"WT:CONFigure:CALibration:FLAtness", SCPI_SetCalibrationFlatness, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_vsa_arb_result_cmd[] = {
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN]:SENSe:FETCh:POINts:POWEr?", GetVsaRstPointPower, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:POINts:POWEr:LITE?", GetVsaRstPointPowerLite, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:POINts:AVG:POWEr:LITE?", GetVsaRstPointAvgPowerLite, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN]:SENSe:FETCh:POINts:IQ:DATA:LITE?", GetVsaRstPointIQLite, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:SPECtrum:DATA:ARB?", GetVsaRstSpectrumDataARB, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:MASK:DATA:ARB?", GetVsaRstSpectrumMaskDataARB, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN]:SENSe:FETCh:SYMBol:CONSt:DATA?", GetVsaRstSymoblConstARB, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN]:SENSe:FETCh:SYMBol:PILOt:CONSt:DATA?", GetVsaRstSymoblPilotConstARB, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SYMBol:REFerence:CONSt:DATA?", GetVsaRstSymoblRefConstARB, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI]:SENSe:FETCh:CARRier:SYMBol:EVM?", GetVsaRstCarrierSymoblEvmARB, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI]:SENSe:FETCh:CARRier:SYMBol:EVM:AVG?", GetVsaRstCarrierSymoblEvmARB_AVG, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI]:SENSe:FETCh:PILOt:CARRier:SYMBol:EVM?", GetVsaRstPilotCarrierSymoblEvmARB, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI]:SENSe:FETCh:PILOt:CARRier:SYMBol:EVM:AVG?", GetVsaRstPilotCarrierSymoblEvmARB_AVG, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:SYMBol:EVM?", GetVsaRstSymoblEVM, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:SYMBol:EVM:AVG?", GetVsaRstSymoblEVM_AVG, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:SYMBol:EVM:PILOt?", GetVsaRstSymoblEVMPilot, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI]:SENSe:FETCh:SPECtrum:FLATness:DATA?", GetVsaRstFlatnessData, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI]:SENSe:FETCh:SPECtrum:FLATness:MASK:UP:DATA?", GetVsaRstFlatnessMaskUp, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI]:SENSe:FETCh:SPECtrum:FLATness:MASK:DOWN:DATA?", GetVsaRstFlatnessMaskDown, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI]:SENSe:FETCh:SPECtrum:FLATness:SECTion?", GetVsaRstFlatnessSection, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI]:SENSe:FETCh:SPECtrum:FLATness:SECTion:MARGin?", GetVsaRstFlatnessSectionMargin, 0, SCPI_QUERY_CMD},

    {"WT:WIFI:SENSe:FETCh:SIGB:SYMBol:CONSt:DATA?", GetVsaRstSIGBSymoblConstARB, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:SIGB:SYMBol:PILOt:CONSt:DATA?", GetVsaRstSIGBSymoblPilotConstARB, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:SIGB:SYMBol:REFerence:CONSt:DATA?", GetVsaRstSIGBSymoblRefConstARB, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:ALL:USER:SYMBol:CONSt:DATA?", GetVsaRstAllUserSymoblConstARB, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:ALL:USER:SYMBol:PILOt:CONSt:DATA?", GetVsaRstAllUserSymoblPilotConstARB, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:ALL:USER:SYMBol:REFerence:CONSt:DATA?", GetVsaRstAllUserSymoblRefConstARB, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:MU:ALL:USER:SYMBol:CONSt:DATA?", GetVsaRstMUAllUserSymoblConstARB, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:MU:ALL:USER:SYMBol:PILOt:CONSt:DATA?", GetVsaRstMUAllUserSymoblPilotConstARB, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:MU:ALL:USER:SYMBol:REFerence:CONSt:DATA?", GetVsaRstMUAllUserSymoblRefConstARB, 0, SCPI_QUERY_CMD},

    {"WT:WIFI:SENSe:FETCh:OFDM:TB:UN:USED:TONE:ERRor?", GetVsaRst_TBUnusedToneErr, 0, SCPI_QUERY_CMD},

    {"WT[:WIFI][:WSUN]:SENSe:FETCh:OFDM:CHANnel:PHASe:RESPonse?", GetVsaRstChannelPhaseResponse, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:OFDM:CHANnel:AMPLitude:RESPonse?", GetVsaRstChannelAmplitudeResponse, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:OFDM:SYMBol:PHASe:ERRor?", GetVsaRstSymbolPhaseError, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:OFDM:SYMBol:AMPLitude?", GetVsaRstSymbolAmplitude, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:SPECtrum:Point:Power?", GetVsaRstSpectrumPointPower, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_wavegenerator_11ac_mumimo_cmd[] = {
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:PPDU", SetWaveGenACPPDU, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:GID", Set11AC_MU_MIMO_GroupID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:TXOP", Set11AC_MU_MIMO_TxOPPS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:GI", Set11AC_MU_MIMO_GI, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:QMAT", Set11AC_MU_MIMO_QMat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:QMAT:NTX", Set11AC_MU_MIMO_QMatNtx, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:QMAT:TYPE", Set11AC_MU_MIMO_QMatType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:QMAT:DELAy", Set11AC_MU_MIMO_QMatDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:QMAT:MAT", Set11AC_MU_MIMO_QMatMap, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:USER:COUNt", Set11AC_MU_MIMO_UserCnt, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:MCS[:USER#]", Set11AC_MU_MIMO_MCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:NSS[:USER#]", Set11AC_MU_MIMO_NSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AC:MU:MIMO:CODing:TYPE[:USER#]", Set11AC_MU_MIMO_CodingType, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_cmd_ends[] = {SCPI_CMD_LIST_END};

// scpi command map
static const scpi_command_t scpi_common_cmd[] = {

    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN]:SENSe:AGC", SetVsaAutoRange, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:AGC:SMPTime", SetAgcSamplingTime, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:AGC:SMPTime?", GetAgcSamplingTime, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN]:SENSe:CAPTure", SetVsaCapture, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN]:SENSe:CAPTure:ASYN", SetVsaCaptureAsync, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN]:SENSe:CAPTure:STATe?", GetVsaCaptureStatus, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN]:SENSe:ANALy:DATA:INIT", SetVsaAnalyzeStart, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN]:SENSe:STOP:CAPTure", SetVsaStopCapture, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:ZWAVe]:SENSe:LOAD:FILE:CAPTure", SetVsaSignalFileAsCapture, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:SAVE:RAW:IQ:ENABle", SetSaveVsaRawIQDataEnable, 0, SCPI_SEQUENTIAL},

    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:ANALy:ERRor:RESUlt?", SetVsaAnalyzeResult, 0, SCPI_SEQUENTIAL},    //分析后获取下分析demo为无有效果的原因

    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:IQ:TRACe?", GetVsaIQDataBwv, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:ADC:IQ:TRACe?", GetVsaRawIQDataBwv, 0, SCPI_QUERY_CMD},

    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:EVM:INVAlid:NOTIce:ERRor?", GetVsaAnalyzFpOnSymbolCntNotEnoughErr, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:LTE][:NR][:NIOT]:SENSe:FETCh:BASEresult?", GetVsaBaseResult, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BASEresult:COMPosite?", GetVsaCompositeBaseResult, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:STReam:REAL:NSTS:INDex?", GetVsaMIMOStreamRealNstsIndex, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:NSS:COUNt?", GetVsaMIMOStreamCount, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:LTE]:SENSe:FETCh:DATA:INFO:MOD?", GetVsaDataInfoDemod, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:CARRier:LEAKage?", GetVsaRstSpectCarrierLeakage, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:OBW99?", GetVsaRstSpectOBW99, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN]:SENSe:FETCh:SPECtrum:MASK:ERROr:PERCent?", GetVsaRstSpectMaskErrorPercent, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:SPECtrum:PEAK:FREQuency?", GetVsaRstSpectPeakFrequency, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:DATA?", GetVsaRstSpectrumData, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE]:SENSe:FETCh:SPECtrum:MASK:DATA?", GetVsaRstSpectrumMaskData, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NIOT]:SENSe:FETCh:SPECtrum:MARGin?", GetVsaRstSpectMargin, 0, SCPI_QUERY_CMD},
    // VSA 功率，IQ视图
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:POWer:FRAMe:LOCAtion?", GetVsaRstFrameLocation, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:POINts:POWEr?", GetVsaRstPointPower, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WCDMA][:NR][:GSM]:SENSe:FETCh:POINts:AVG:POWEr?", GetVsaRstPointAvgPower, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:POINts:IQ:DATA?", GetVsaRstPointIQ, 0, SCPI_QUERY_CMD},

    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN]:SENSe:FETCh:RAWData?", GetVsaRstRawData, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE]:SENSe:FETCh:POWer:PEAK?", GetVsaRstPowerPeak, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE]:SENSe:FETCh:POWer:FRAMe?", GetVsaRstPowerFrame, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN]:SENSe:FETCh:POWer:ALL?", GetVsaRstPowerAll, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:POWer:FRAMe:COUNt?", GetVsaRstFrameCount, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GLE][:SLE][:WSUN]:SENSe:FETCh:EVM:ALL?", GetVsaRstEvmAll, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee]:SENSe:FETCh:EVM:ALL:PERCent?", GetVsaRstEvmAllPercent, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee]:SENSe:FETCh:EVM:PEAK?", GetVsaRstEvmPeak, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee]:SENSe:FETCh:EVM:PEAK:PERCent?", GetVsaRstEvmPeakPercent, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:LTE][:NR][:NIOT]:SENSe:FETCh:FREQerror?", GetVsaRstFreqError, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:WSUN]:SENSe:FETCh:CLKError?", GetVsaRstClkError, 0, SCPI_QUERY_CMD},

    {"WT:WIFI:SENSe:FETCh:SNR:LTF?", GetVsaRstLTFSNR, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:SNR:PSDU?", GetVsaRstPSDUSNR, 0, SCPI_QUERY_CMD},
    // GPRF
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SENSe:FETCh:FREQuency:OFFSet?", GetVsaRstFrequencyOffset, 0, SCPI_QUERY_CMD},

    // CMD
    {"WT:DIAGnosis:COMPonent:BOARd#:MODule#:DEVIce#[:CHIP#]:ADDR#?", GetComponentRegisterValue, 0, SCPI_QUERY_CMD},
    {"WT:DIAGnosis:COMPonent:BOARd#:MODule#:DEVIce#[:CHIP#]:ADDR#", SetComponentRegisterValue, 0, SCPI_SEQUENTIAL},

    //校准
    // 关于校准库上传下载文件，直接使用系统命令上传下载就可以了不用再另外写接口
    {"WT:CONFigure:CALibration:RELoad", CalibrationDataReload, 0, SCPI_SEQUENTIAL},
    {"WT:CONFigure:CALibration:MODE#", SetCalibrationState, 0, SCPI_SEQUENTIAL},
    {"WT:CONFigure:CALibration:MODE#?", GetCalibrationState, 0, SCPI_QUERY_CMD},
    {"WT:CONFigure:CALibration:INCalmode", SetInCalRunMode, 0, SCPI_SEQUENTIAL},
    {"WT:CONFigure:CALibration:STARt:OR:STOP", SetSelfCalStartorStop, 0, SCPI_SEQUENTIAL},
    {"WT:CONFigure:CALibration:FLAtness", SetCalibrationFlatnessMode, 0, SCPI_SEQUENTIAL},
    {"WT:CONFigure:CALibration:STATus?",GetSelfCalibrationStatus,0,SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB]:SENSe:FETCh:RESUlt:SPECification?", GetVsaRstSpecification, 0, SCPI_SEQUENTIAL},
    {"WT:CALibration:STARt:FAST:ATT?", SCPI_StartFastAttCal, 0, SCPI_QUERY_CMD},
    {"WT:CALibration:GET:DEFAult:PARAm?", SCPI_GetDefaultParam, 0, SCPI_QUERY_CMD},
    {"WT:CALibration:GET:VSG:PARAm?", SCPI_GetVSGParameter_V2, 0, SCPI_QUERY_CMD},
    {"WT:CALibration:GET:FILE?", SCPI_GetCalFile, 0, SCPI_QUERY_CMD},
    {"WT:CALibration:GET:VECTor:RESUlt:COUNt?", SCPI_GetVectorResultElementCount, 0, SCPI_QUERY_CMD},
    {"WT:CALibration:GET:VECTor:RESUlt?", SCPI_GetVectorResult, 0, SCPI_QUERY_CMD},
    {"WT:CALibration:SEND:CAL:FILE", SCPI_SendCalFile, 0, SCPI_QUERY_CMD},
    {"WT:CALibration:SET:VSA#:MOD#:DC:OFFSet", SetDcOffset, 0, SCPI_SEQUENTIAL},

    //获取H矩阵是能前的初始数据的通道结果，配合H矩阵使用
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:H:MATrix:INITial:NSS:COUNt?", GetVsaHMatInitMIMOStreamCount, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:H:MATrix:INITial:POWer:PEAK?", GetVsaRstHMatInitPowerPeak, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:H:MATrix:INITial:POWer:FRAMe?", GetVsaRstHMatInitPowerFrame, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:H:MATrix:INITial:POWer:ALL?", GetVsaRstHMatInitPowerAll, 0, SCPI_QUERY_CMD},

    {"WT:SENSe:NOISe:CALibration:STARt", SetVsaNoiseCalibrationStart, 0, SCPI_SEQUENTIAL},
    {"WT:SENSe:NOISe:CALibration:STATus?", GetVsaNoiseCalibrationStatus, 0, SCPI_QUERY_CMD},
    {"WT:SENSe:NOISe:CALibration:STOP", SetVsaNoiseCalibrationStop, 0, SCPI_SEQUENTIAL},
    {"WT:SENSe:NOISe:CALibration:PORT:VALId?", GetVsaNoiseCalibrationPortValid, 0, SCPI_QUERY_CMD},
    {"WT:SENSe:NOISe:CALibration:SELF:STARt", SetVsaNoiseCalSelfStart, 0, SCPI_SEQUENTIAL},
    {"WT:SENSe:NOISe:CALibration:SELF:STATus?", GetVsaNoiseCalSelfStatus, 0, SCPI_QUERY_CMD},
    {"WT:SENSe:NOISe:CALibration:SELF:STOP", SetVsaNoiseCalSelfStop, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_common_graphic_cmd[] = {
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:CONFigure:CALculate:IQ:IMBalance:SEGMent#", SetVsaImbalanceCal, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:SPECtrum:FREQuency:SPAN?", GetVsaRstSpectrumSpan, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WCDMA][:LTE][:NR][:NIOT]:SENSe:FETCh:SPECtrum:CENTer:FREQuency?", GetVsaRstSpectrumCenterFreq, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:SPECtrum:RBW?", GetVsaRstSpectrumRBW, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE][:NR][:NIOT][:GSM]:SENSe:FETCh:SPECtrum:OBW:FREQuency:SEGMent#?", GetVsaRstSpectrumOBW, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE]:SENSe:FETCh:CCDF:PROB?", GetVsaRstCCDFProb, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE]:SENSe:FETCh:CCDF:POWEr:REF?", GetVsaRstCCDFPowerRef, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE]:SENSe:FETCh:CCDF:STARt?", GetVsaRstCCDFStart, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WCDMA][:LTE]:SENSe:FETCh:CCDF:SCALe?", GetVsaRstCCDFScale, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:WSUN][:WCDMA][:LTE]:SENSe:FETCh:CCDF:PERcentage:POWEr?", GetVsaRstCCDFPercentPower, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF]:SENSe:FETCh:CALculate:IQ:IMBalance:SEGMent#?", GetVsaRstImbalanceCal, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:OFDM:PREAmble:FREQuency:ERRor?", GetVsaRstOFDM_PreambleFreqErr, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:PREAmble:FREQuency:ERRor:VALID?", GetVsaRstOFDM_PreambleFreqErrValid, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_vsa_11b_graphic_cmd[] = {
    {"WT:WIFI:SENSe:FETCh:B:EVM:TIME?", GetVsaRst11b_EVMTime, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:EVM:TIME:AVG?", GetVsaRst11b_EVMTimeAVG, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:EVM:TIME:CHIP?", GetVsaRst11b_EVMTimeChip, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:EYE:DATA?", GetVsaRst11b_EyeGraphic, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:RAMP:ON:POWEr:DATA?", GetVsaRst11b_RampOnPower, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:RAMP:ON:POWEr:PEAK:DATA?", GetVsaRst11b_RampOnPowerPeak, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:RAMP:ON:POWEr:MASK#?", GetVsaRst11b_RampOnMask, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:RAMP:OFF:POWEr:DATA?", GetVsaRst11b_RampOffPower, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:RAMP:OFF:POWEr:PEAK:DATA?", GetVsaRst11b_RampOffPowerPeak, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:RAMP:OFF:POWEr:MASK#?", GetVsaRst11b_RampOffMask, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:PHASe:ERRor?", GetVsaRst11b_PhaseError, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:BIT:RATE?", GetVsaRst11b_BitError, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:PREAmble:FREQuency:ERRor?", GetVsaRst11b_PreambleFreqError, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:SPECtrum:LO:LEAKage:RBW?", GetVsaRst11b_LO_RBW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:SPECtrum:LO:LEAKage:SPAN?", GetVsaRst11b_LO_Span, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:SPECtrum:LO:LEAKage?", GetVsaRst11b_LO_Value, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:FREQuency:ERRor:TIME?", GetVsaRst11b_FreqErrVsTime, 0, SCPI_QUERY_CMD},

    //11b 参考标准
    {"WT:WIFI:SENSe:FETCh:B:FREQerr:MARGin?", GetVsaRst11b_FreqerrMargin, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:B:CLOCkerr:MARGin?", GetVsaRst11b_ClockErrMargin, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_vsa_11be_result_cmd[] = {
    {"WT:WIFI:SENSe:FETCh:BE:MU:MIMO?", GetVsaRst_11Be_MUMIMO, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:PPDU?", GetVsaRst_11Be_PPDU, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:RU:NUMber?", GetVsaRst_11Be_RUCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USER:NUMber?", GetVsaRst_11Be_UserCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:ACTIve:USER:NUMber?", GetVsaRst_11Be_ActiveUserCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SYMbol:NUMber?", GetVsaRst_11Be_SymbolCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:LTF:LENgth?", GetVsaRst_11Be_LTFLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:GI:LENgth?", GetVsaRst_11Be_GILen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:DATA:RATE?", GetVsaRst_11Be_DataInfoDataRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:PE:LENgth?", GetVsaRst_11Be_PELen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:NSTS?", GetVsaRst_11Be_DataInfoNSTS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:DATA:LENgth?", GetVsaRst_11Be_DataLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:FRAMe:LENgth?", GetVsaRst_11Be_FrameLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:PREAmble:LENgth?", GetVsaRst_11Be_PreambleLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:DATA:SYMbol:LENgth?", GetVsaRst_11Be_DataSymbolLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:CRC?", GetVsaRst_11Be_USIG_CRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:PHY:VERsion?", GetVsaRst_11Be_USIG_PHY, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:BW?", GetVsaRst_11Be_USIG_BW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:ULDL?", GetVsaRst_11Be_USIG_ULDL, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:BSS:COLOr?", GetVsaRst_11Be_USIG_BSSColor, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:TXOP?", GetVsaRst_11Be_USIG_TXOP, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:DISregard?", GetVsaRst_11Be_USIG_Disregard, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:COMPression:MODE?", GetVsaRst_11Be_USIG_CompressionMode, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:B25?", GetVsaRst_11Be_USIG_B25, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:U2B2?", GetVsaRst_11Be_USIG_U2B2, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:U2B8?", GetVsaRst_11Be_USIG_U2B8, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:MCS?", GetVsaRst_11Be_USIG_MCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:SYMbol?", GetVsaRst_11Be_USIG_Symbol, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:PUNcturing:BIT?", GetVsaRst_11Be_USIG_PunturingBit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:CRC?", GetVsaRst_11Be_SIG_CRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:SREUse?", GetVsaRst_11Be_SIG_SpatialReuse, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:GI:LTF?", GetVsaRst_11Be_SIG_GILTFSize, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:LTF:SYMbol?", GetVsaRst_11Be_SIG_LTFSymobl, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:LDPC:EXTra:SYMbol?", GetVsaRst_11Be_SIG_LDPCExtra, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:PRE:FEC:FACTor?", GetVsaRst_11Be_SIG_PreFecPadFactor, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:PEDisambiguity?", GetVsaRst_11Be_SIG_PEDisambiguity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:DISregard?", GetVsaRst_11Be_SIG_Disregard, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:COMMon:BIT?", GetVsaRst_11Be_SIG_Common9Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:ADDitional:NUMber:LTF?", GetVsaRst_11Be_SIG_AddEhtLtfSym, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:LSIG:CRC?", GetVsaRst_11Be_LSIG_CRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:LSIG:PSDU:LENgth?", GetVsaRst_11Be_LSIG_PSDULength, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:LSIG:DATA:RATE?", GetVsaRst_11Be_LSIG_DataRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:LSIG:PARIty:BIT?", GetVsaRst_11Be_LSIG_ParityBit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:LSIG:PARIty?", GetVsaRst_11Be_LSIG_ParityCheck, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:NSP?", GetVsaRst_11Be_NSP, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:NSP?", GetVsaRst_11Be_MUMIMO_NSP, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:RU#:NSD?", GetVsaRst_11Be_NSD, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:OFDM:MU:RU#:USER#:NSD?", GetVsaRst_11Be_MUMIMO_NSD, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_set_vsa_11ba_analyze_cmd[] = {
    {"WT:WIFI:SENSe:CONFigure:ANALy:BA:EMBEdded:BSS:ID", SetVsaAlzBaEmbeddedBSSID, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_vsa_11ba_result_cmd[] = {
    {"WT:WIFI:SENSe:FETCh:BA:WUR:SPECtrum:DATA:ARB?", GetVsaRst_11Ba_WURDataSpectrumDataArb, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:WUR:SPECtrum:MASK:DATA:ARB?", GetVsaRst_11Ba_WURDataSpectrumMaskArb, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:WUR:SPECtrum:OBW:FREQuency?", GetVsaRst_11Ba_WURDataSpectrumOBW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:WUR:SPECtrum:RBW?", GetVsaRst_11Ba_WURDataSpectrumRBW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:WUR:SPECtrum:FREQuency:SPAN?", GetVsaRst_11Ba_WURDataSpectrumSpan, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:BW?", GetVsaRst_11Ba_BW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:LSIG:PARIty?", GetVsaRst_11Ba_LSigParityPassed, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:LSIG:RATE?", GetVsaRst_11Ba_LSigRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:LSIG:LENgth?", GetVsaRst_11Ba_LSigLength, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:LSIG:BIT:STREam?", GetVsaRst_11Ba_LSig_Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:SUB:CHANnel:COUNt?", GetVsaRst_11Ba_SubChannelCount, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:PUNCtured:INFO?", GetVsaRst_11Ba_PuncturedInfo, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:DATA:RATE:MODE?", GetVsaRst_11Ba_DataRateMode, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:PSDU:LENgth?", GetVsaRst_11Ba_PSUDLength, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:PSDU:CRC?", GetVsaRst_11Ba_PSDU_CRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:SYNC:SYMBol:POWer:RATIo?", GetVsaRst_11Ba_SyncSymPowerRatio, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:DATA:SYMBol:POWer:RATIo:MAX?", GetVsaRst_11Ba_DataSymPowerRatioMax, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:DATA:SYMBol:POWer:RATIo:AVG?", GetVsaRst_11Ba_DataSymPowerRatioAvg, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:DATA:SYMBol:POWer:RATIo:MIN?", GetVsaRst_11Ba_DataSymPowerRatioMin, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:CORRelation:METRic:MAX?", GetVsaRst_11Ba_CorrelationMetricMax, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:CORRelation:METRic:AVG?", GetVsaRst_11Ba_CorrelationMetricAvg, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:WUR:SPECtrum:MASK:ERROr:PERCent?", GetVsaRst_11Ba_WURDataSpectrumMaskErrPercent, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:TRANsmit:ON:OFF:SYMBols:POWer:RATIo:TEST:RESUlt?", GetVsaRst_11Ba_SymPowerRatioTestResult, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BA:CORRelation:TEST:RESUlt?", GetVsaRst_11Ba_CorrelationTestResult, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_admin_tool_cmd[] = {
    {"WT:SYSTem:ADMIn:TOOL:TESTer:RUN:MODE", SCPI_SetTesterRunMode, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:TESTer:RUN:MODE?", SCPI_GetTesterRunMode, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:ADMIn:TOOL:TESTer:DIG:MODE", SCPI_SetTesterDigMode, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:TESTer:DIG:MODE?", SCPI_GetTesterDigMode, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:ADMIn:TOOL:MANAger:MODE", SCPI_MangerLink, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:RE:BOOT:TESTer", SCPI_RebootTester, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:UP:GRADe:FW:STATus?", SCPI_FWThreadStatus, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:ADMIn:TOOL:UP:LOAD:FW", SCPI_FirmwareUpLoad, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:UP:GRADe:FW", SCPI_FirmwareUpdate, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:RESTore:FW", SCPI_FirmwareRestore, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:FACTory:RESet", SCPI_FactoryReset, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:UP:GRADe:LICense", SCPI_LicenseUpdate, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:DEL:ALL:LICense", SCPI_DeleteAllLicense, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:GET:SLAVe:ALL:LICense:DETAil?", SCPI_GetSlaveLicenseInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:ADMIn:TOOL:GET:ALL:LICense:DETAil?", SCPI_GetLicenseInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:ADMIn:TOOL:GET:PACK:LICense?", SCPI_GetLicensePack, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:ADMIn:TOOL:SET:NET:INFO", SCPI_SetTesterNetInfo, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:GET:THIRd:PARTy:APP:INFO?", SCPI_GetDockerAppInfo, 0, SCPI_QUERY_CMD},
    {"WT:SYSTem:ADMIn:TOOL:SET:THIRd:PARTy:APP:INFO", SCPI_SetDockerAppInfo, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:ADMIn:TOOL:DEL:THIRd:PARTy:APP", SCPI_DelDockerApp, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:ETH:AUTOneg", SCPI_SetTesterEthAutoneg, 0, SCPI_SEQUENTIAL},
    {"WT:DIGtal:IQ:CONFigure:ETH:AUTOneg?", SCPI_GetTesterEthAutoneg, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_wavegenerator_11be_cmd[] = {
    // 11be EHT-MU MU-MIMO
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:RUTPlan:USER", Set11BE_EHT_MU_ToneUserCnt, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:RUTPlan:AND:USER?", Get11BE_EHT_MU_ToneUserCnt, 0, SCPI_QUERY_CMD},

    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:PPDU", Set11BE_EHT_PPDU, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:FBANd", Set11BE_EHT_MU_FullBand, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:RUTPlan", Set11BE_EHT_MU_TonePlan, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:SIGB:MCS", Set11BE_EHT_MU_SIGBMCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:PUNCtured:RU", Set11BE_EHT_MU_Punctured, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:NON:HT:DUPLicate:PUNCtured", Set11BE_EHT_MU_NonHt_Duplicate_Punctured, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:STBC", Set11BE_EHT_MU_STBC, 0, SCPI_SEQUENTIAL}, //该命令目前wave工具没有配置
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:ULDL", Set11BE_EHT_MU_ULDL, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:SREUse", Set11BE_EHT_MU_SpatialReuse, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:GLTF:SIZE", Set11BE_EHT_MU_GLTFSize, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:TXOP", Set11BE_EHT_MU_TXOP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:BSS:COLOr", Set11BE_EHT_MU_BSSColor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:DOPPler", Set11BE_EHT_MU_Doppler, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:MIDPeriod", Set11BE_EHT_MU_MidamblePeriodicity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:PE", Set11BE_EHT_MU_PE, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:ADDitional:LTF", Set11BE_EHT_MU_ADDLTF, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:SOUNding:NDP", Set11BE_EHT_MU_SoundingNDP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:RU:ALLOcation:DIFFerent:M80", Set11BE_EHT_MU_RUAllocationDifferentIn80M, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:M320:FREQuency:NUMber", Set11BE_EHT_MU_320MCenterFrequencyNumber, 0, SCPI_SEQUENTIAL},
    // 11be EHT-MU MU-MIMO RU user count
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:USER:COUNt[:RU#]", Set11BE_EHT_MU_RUUserCnt, 0, SCPI_SEQUENTIAL},
    // 11be EHT-MU MU-MIMO QMAT
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:QMAT[:RU#]", Set11BE_EHT_MU_RUQMat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:QMAT:NTX[:RU#]", Set11BE_EHT_MU_RUQMatNtx, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:QMAT:TYPE[:RU#]", Set11BE_EHT_MU_RUQMatType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:QMAT:DELAy[:RU#]", Set11BE_EHT_MU_RUQMatDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:QMAT:MAT[:RU#]", Set11BE_EHT_MU_RUQMatMap, 0, SCPI_SEQUENTIAL},
    // 11be EHT-MU MU-MIMO User in RU
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:AID[:RU#][:USER#]", Set11BE_EHT_MU_RUAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:POWer:FACT[:RU#][:USER#]", Set11BE_EHT_MU_RUPowerFactor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:CODing:TYPE[:RU#][:USER#]", Set11BE_EHT_MU_RUCoding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:MCS[:RU#][:USER#]", Set11BE_EHT_MU_RUMCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:NSS[:RU#][:USER#]", Set11BE_EHT_MU_RUNSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:BEAM:FORMed[:RU#][:USER#]", Set11BE_EHT_MU_RUBeamformed, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:MU:MIMO:BEAMformed[:RU#][:USER#]", Set11BE_EHT_MU_RUBeamformed, 0, SCPI_SEQUENTIAL},//功能同上一条，历史问题原因，先保留（后续不用要删除）
};

static const scpi_command_t scpi_wt_wavegenerator_11ba_cmd[] = {
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:NSS", Set11BA_StreamCount, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PUNCturing:MODE", Set11BA_PuncturingMode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PSDU:SYNChronization:EXAMple:INDEx", Set11BA_SyncExampleIndex, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PSDU:DATA:EXAMple:INDEx", Set11BA_DataExampleIndex, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PSDU:DATA:RATE:MODE", Set11BA_DataRateMode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PSDU:TYPE", Set11BA_PsduType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PSDU:LENgth", Set11BA_PsduLength, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PSDU:MAC:FRAMe:CTRL:TYPE", Set11BA_MAC_FrameControlType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PSDU:MAC:PROTected", Set11BA_MAC_Protected, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PSDU:MAC:ID:FIELd", Set11BA_MAC_IDField, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PSDU:MAC:TYPE:DEPEndent:CTRL", Set11BA_MAC_TypeDependentControl, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PSDU:EMBEdded:BSS:ID:ENABle", Set11BA_EmbeddedBSSIDEnable, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BA:PSDU:EMBEdded:BSS:ID:VALUe", Set11BA_EmbeddedBSSIDValue, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_wavegenerator_channel_model[] = {
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:TYPE", Set_AWGN_Type, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:CARRier:FREQuency", Set_AWGN_CarrierFrequency, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:SPEEd", Set_AWGN_Speed, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:TX:RX:DISTance", Set_AWGN_TXRX_Distance, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:POWEr:LINE:FREQuency", Set_AWGN_PowerLineFrequency, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:LARGer:SCALe:FAD:EFFect", Set_AWGN_LargerScaleFadEffect, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:FLUO:EFFect", Set_AWGN_FluorescenceEffect, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:GAU:STD", Set_AWGN_GaussianStandardDeviation, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:GAU:MEAN", Set_AWGN_GaussianMeanValue, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:DIREct", Set_AWGN_Direct, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:RANDom:SEED", Set_AWGN_RandomSeed, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:TX:RX:ANT:SPACeing", Set_AWGN_TXRX_AntennaSpaceing, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:PATH:DELAy", Set_AWGN_PathDelay, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:POWEr:PER:ANGLe", Set_AWGN_PowerPerAngle, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:RX:AOA:DEGree", Set_AWGN_RX_AOA_Degree, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:RX:AS:DEGree", Set_AWGN_RX_AS_Degree, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:TX:AOD:DEGree", Set_AWGN_TX_AOD_Degree, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:TX:AS:DEGree", Set_AWGN_TX_AS_Degree, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:GPRF]:SOURce:CONFigure:WAVE:AWGN:DBP", Set_AWGN_DBP, 0, SCPI_SEQUENTIAL},
};


static const scpi_command_t scpi_wt_vsa_data_info_plus_cmd[] = {
    {"WT:WIFI:SENSe:FETCh:NON:HT:DUPLicate:PUNCtured:INFO?", GetVsaRst_NonHTDuplicatePuncturedInfo, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AG:DATA:DUPLicate?", GetVsaRst_11ag_duplicate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AG:LSIG:BIT:STREam?", GetVsaRst_11ag_LSIG_Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:LSIG:BIT:STREam?", GetVsaRst_11n_LSIGBit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:N:HT:SIG:BIT:STREam?", GetVsaRst_11n_HSIGBit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:PARTial:AID?", GetVsaRst_11ac_PartialAID, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:TXOP:PS?", GetVsaRst_11ac_TXOP_PS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:SOUNding:NDP?", GetVsaRst_11ac_SoundingNDP, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:BEAM:FORMed?", GetVsaRst_11ac_Beamformed, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:BEAMformed?", GetVsaRst_11ac_Beamformed, 0, SCPI_QUERY_CMD},//功能同上一条，历史问题原因，先保留（后续不用要删除）
    {"WT:WIFI:SENSe:FETCh:AC:LDPC:EXTRa?", GetVsaRst_11ac_LDPCExtra, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:NSYM:DISambiguity?", GetVsaRst_11ac_NsymDisambiguity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:LSIG:BIT:STREam?", GetVsaRst_11ac_LSIG_Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:SIGA:BIT:STREam?", GetVsaRst_11ac_SIGA_Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AC:SIGB:BIT:STREam?", GetVsaRst_11ac_SIGB_Bit, 0, SCPI_QUERY_CMD},

    {"WT:WIFI:SENSe:FETCh:AX:PUNcturing:MODE?", GetVsaRst_11ax_PunctureMode, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SOUNding:NDP?", GetVsaRst_11ax_SoundingNDP, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:TXOP:PS?", GetVsaRst_11ax_TXOP, 0, SCPI_QUERY_CMD},/* 第一版写错了，更正为TXOP非TXOP_PS */
    {"WT:WIFI:SENSe:FETCh:AX:TXOP?", GetVsaRst_11ax_TXOP, 0, SCPI_QUERY_CMD},/* 11ax TXOP更正后的命令 */
    {"WT:WIFI:SENSe:FETCh:AX:SREUse?", GetVsaRst_11ax_SReuse, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:BEAM:FORMed?", GetVsaRst_11ax_Beamformed, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:BEAMformed?", GetVsaRst_11ax_Beamformed, 0, SCPI_QUERY_CMD},//功能同上一条，历史问题原因，先保留（后续不用要删除）
    {"WT:WIFI:SENSe:FETCh:AX:LSIG:BIT:STREam?", GetVsaRst_11ax_LSIG_Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGA:BIT:STREam?", GetVsaRst_11ax_SIGA_Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:BIT1:STREam?", GetVsaRst_11ax_SIGB_Bit_1, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AX:SIGB:BIT2:STREam?", GetVsaRst_11ax_SIGB_Bit_2, 0, SCPI_QUERY_CMD},

    {"WT:WIFI:SENSe:FETCh:BE:LSIG:BIT:STREam?", GetVsaRst_11Be_LSIG_Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIGU:BIT:STREam?", GetVsaRst_11Be_USIG_Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:CTX1:BIT:STREam?", GetVsaRst_11Be_SIG_CTX1_Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:SIG:CTX2:BIT:STREam?", GetVsaRst_11Be_SIG_CTX2_Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:SOUNding:NDP?", GetVsaRst_11Be_USIG_SoundingNDP, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:TB:SIG:ONE:DISregard?", GetVsaRst_11Be_USIG_TBSIG1_Disregard, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:TB:SIG:TWO:DISregard?", GetVsaRst_11Be_USIG_TBSIG2_Disregard, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:TB:B:TWO:VALId?", GetVsaRst_11Be_USIG_TBValid_B2, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:BE:USIG:TB:SREUse?", GetVsaRst_11Be_USIG_TBSpaReuse, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:WSUN]:SENSe:FETCh:DATA:BURSt:INFOrmation?", GetVsaRst_DataburstInfo, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_wavegenerator_11be_tb_cmd[] = {

    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:NLTF:SYMBols", Set11BE_TB_MUMIMO_NumLTFSymbols, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:GLTF:SIZE", Set11BE_TB_MUMIMO_GLTFSize, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:SREUse", Set11BE_TB_MUMIMO_SpatialReuse, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:TXOP", Set11BE_TB_MUMIMO_TXOP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:BSS:COLOr", Set11BE_TB_MUMIMO_BSSColor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:DOPPler", Set11BE_TB_MUMIMO_Doppler, 0, SCPI_SEQUENTIAL}, //该命令目前wave工具没有配置
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:STBC", Set11BE_TB_MUMIMO_STBC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:LDPC:EXTRa", Set11BE_TB_MUMIMO_LDPCExtra, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:AFACtor", Set11BE_TB_MUMIMO_AFactor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:PE:DISAmbiguity", Set11BE_TB_MUMIMO_PEDisambiguity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:MIDPeriod", Set11BE_TB_MUMIMO_MidamblePeriodicity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:PE", Set11BE_TB_MUMIMO_PE, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:LTF:MODE", Set11BE_TB_MUMIMO_LTFMode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:SIG:ONE:DISregard", Set11BE_TB_MUMIMO_SIG1Dis, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:SIG:TWO:DISregard", Set11BE_TB_MUMIMO_SIG2Dis, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:B:TWO:VALId", Set11BE_TB_MUMIMO_B2Valid, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:M320:FREQuency:NUMber", Set11BE_TB_MUMIMO_320MCenterFrequencyNumber, 0, SCPI_SEQUENTIAL},
    // 11be TB NDP
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:NDP:MODE", Set11BE_TB_MUMIMO_NDP_Mode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:NDP:STARting:AID", Set11BE_TB_MUMIMO_NDP_StartingAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:NDP:MULTplexing", Set11BE_TB_MUMIMO_NDP_Multiplexing, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:NDP:FEED:BACK:STATus", Set11BE_TB_MUMIMO_NDP_FeedbackStatus, 0, SCPI_SEQUENTIAL},
    // 11be TB MU-MIMO RU
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:RU:COUNt[:SEGment#]", Set11BE_TB_MUMIMO_RUCnt, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:RUINdex[:RU#][:SEGment#]", Set11BE_TB_MUMIMO_RUIndex, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:NUMber:USER[:RU#][:SEGment#]", Set11BE_TB_MUMIMO_RUUserCnt, 0, SCPI_SEQUENTIAL},
    // 11be TB MU-MIMO QMAT
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:QMAT[:RU#][:SEGment#]", Set11BE_TB_MUMIMO_RUQMat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:QMAT:NTX[:RU#][:SEGment#]", Set11BE_TB_MUMIMO_RUQMatNtx, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:QMAT:TYPE[:RU#][:SEGment#]", Set11BE_TB_MUMIMO_RUQMatType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:QMAT:DELAy[:RU#][:SEGment#]", Set11BE_TB_MUMIMO_RUQMatDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:QMAT:MAT[:RU#][:SEGment#]", Set11BE_TB_MUMIMO_RUQMatMap, 0, SCPI_SEQUENTIAL},
    // 11be TB MU-MIMO User in RU
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:AID[:RU#][:USER#][:SEGment#]", Set11BE_TB_MUMIMO_RUUserAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:POWer:FACT[:RU#][:USER#][:SEGment#]", Set11BE_TB_MUMIMO_RUUserPowerFactor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:CODing:TYPE[:RU#][:USER#][:SEGment#]", Set11BE_TB_MUMIMO_RUUserCoding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:DCM[:RU#][:USER#][:SEGment#]", Set11BE_TB_MUMIMO_RUUserDCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:MCS[:RU#][:USER#][:SEGment#]", Set11BE_TB_MUMIMO_RUUserMCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:NSS[:RU#][:USER#][:SEGment#]", Set11BE_TB_MUMIMO_RUUserNSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:NSS:STARt[:RU#][:USER#][:SEGment#]", Set11BE_TB_MUMIMO_RUUserNSSStart, 0, SCPI_SEQUENTIAL},

    //11be tb Time domain multi-user
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:BUILt:UP:MODE", Set11BE_TB_MUMIMO_UserBuiltUpMode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:CYCLic:SHIFt:DIVErsity:TIME[:RU#][:USER#][:SEGment#]", Set11BE_TB_MUMIMO_RUUserCSDTime, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:FREQuency:OFFSet[:RU#][:USER#][:SEGment#]", Set11BE_TB_MUMIMO_RUUserFreqOffset, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:BE:TB:MU:POWer:SCALe[:RU#][:USER#][:SEGment#]", Set11BE_TB_MUMIMO_RUUserPowerScale, 0, SCPI_SEQUENTIAL},

};

    //共本振的设置获取命令。VSGA 集成/分离模式
static const scpi_command_t scpi_wt_hw_config_cmd[] = {
    {"WT:SYSTem:CONFigure:VSGA:MODE[:MOD#]", SCPI_SetLoMode, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:CONFigure:VSGA:MODE[:MOD#]?", SCPI_GetLoMode, 0, SCPI_QUERY_CMD},
    //WT418屏蔽模拟IQ
    // {"WT:SYSTem:CONFigure:ANALog:IQ:MODE", SCPI_SetIQMode, 0, SCPI_SEQUENTIAL},
    // {"WT:SYSTem:CONFigure:ANALog:IQ:MODE?", SCPI_GetIQMode, 0, SCPI_QUERY_CMD},
};


//监听相关的命令
static const scpi_command_t scpi_wt_monitor_cmd[] = {
    //{"WT:SYSTem:MONIconnect", SCPI_MonitorConnect, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:CONFigure:MONItor:RFPort", SCPI_SetMonitorRfPort, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:CONFigure:MONItor:ACTion", SCPI_SetMonitorAction, 0, SCPI_SEQUENTIAL},
};

//平均相关的命令
static const scpi_command_t scpi_wt_vsa_average_cmd[] = {
    {"WT[:WIFI]:SENSe:CONFigure:AVG:CLEAn", CleanAverage, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI]:SENSe:CONFigure:AVG:SETting", SetAverageSetting, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI]:SENSe:CONFigure:AVG:MIN:COUNt", SetAverageMinCount, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI]:SENSe:CONFigure:AVG:CUR:COUNt?", GetCurAverageCount, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI]:SENSe:FETCh:AVG:RESUlt?", GetAverageResultARB, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI]:SENSe:FETCh:AVG:BASE:RESUlt?", GetAverageBaseResult, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI]:SENSe:FETCh:MIMO:AVG:COMPosite:RESUlt?", GetMimoAverageVompositeResult, 0, SCPI_QUERY_CMD},
    {"WT:SLE:SENSe:FETCh:AVG:RESUlt?", GetSLEAverageResult, 0, SCPI_QUERY_CMD},
    {"WT:SLE:SENSe:FETCh:AVG:RESUlt:ARB?", GetSLEAverageResultARB, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:AVG:RESUlt?", GetBTAverageResult, 0, SCPI_QUERY_CMD},
    {"WT:BT:SENSe:FETCh:AVG:RESUlt:ARB?", GetBTAverageResultARB, 0, SCPI_QUERY_CMD},
};

//特殊命令接口，仅针对台湾模拟原api接口使用，不对外公开
static const scpi_command_t scpi_wt_vsa_special_api_cmd[] = {
    {"WT:WIFI:SENSe:FETCh:GET:VECTor:RESUlt?", GetVectorResult, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:GET:VECTor:RESUlt:ELEMent:COUNt?", GetVectorResultElementCount, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:GET:VECTor:RESUlt:ELEMent:SIZE?", GetVectorResultElementSize, 0, SCPI_QUERY_CMD},
};

//11az cmd
static const scpi_command_t scpi_wt_set_vsa_11az_analyze_cmd[] = {
    {"WT:WIFI:SENSe:CONFigure:ANALy:AZ:SECUre:MODE", SetVsaAlzAzSecureMode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AZ:TX:WINDow", SetVsaAlzAzTxWindow, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AZ:USER:COUNt", SetVsaAlzAzUserCount, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AZ:NSS[:USER#]", SetVsaAlzAzUserNss, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AZ:LTF:REPEat[:USER#]", SetVsaAlzAzUserLTFRepeat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AZ:LTF:IV:HEX[:USER#]", SetVsaAlzAzUserLTFIV, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SENSe:CONFigure:ANALy:AZ:LTF:KEY:HEX[:USER#]", SetVsaAlzAzUserLTFKey, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_vsa_11az_result_cmd[] = {
    //data info detail
    {"WT:WIFI:SENSe:FETCh:AZ:SIGA:CRC?", GetVsaRst_11az_SIGACRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:PUNcturing:MODE?", GetVsaRst_11az_PunctureMode, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:BW?", GetVsaRst_11az_BW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:PPDU?", GetVsaRst_11az_PPDU, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:RU:NUMber?", GetVsaRst_11az_RUCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:RU:USER:NUMber?", GetVsaRst_11az_UserCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:LTF:NUMber?", GetVsaRst_11az_LTFCnt, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:LTF:TYPE?", GetVsaRst_11az_LTFType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:LTF:LENgth?", GetVsaRst_11az_LTFLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:GI:LENgth?", GetVsaRst_11az_GILen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:DATA:LENgth?", GetVsaRst_11az_FrameLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:BSS:COLOr?", GetVsaRst_11az_BSSColor, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:TX:BEAM:CHANge?", GetVsaRst_11az_BeamChange, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:LDPC:EXTra:SYMbol?", GetVsaRst_11az_LDPCExtra, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:PEDisambiguity?", GetVsaRst_11az_PEDisambiguity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:PE:LENgth?", GetVsaRst_11az_PELen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:PRE:FEC:FACTor?", GetVsaRst_11az_PreFecPadFactor, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:DOPPler?", GetVsaRst_11az_Doppler, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:MIDamble:PERiodicity?", GetVsaRst_11az_MidamblePeriodicity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:TXOP?", GetVsaRst_11az_TXOP, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:SREUse?", GetVsaRst_11az_SReuse, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:SIGA:BIT:STREam?", GetVsaRst_11az_SigA_Bit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:LSIG:PARIty?", GetVsaRst_11az_LSIGParity, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:LSIG:RATE?", GetVsaRst_11az_LSIGRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:LSIG:LENgth?", GetVsaRst_11az_LSIGLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:LSIG:BIT:STREam?", GetVsaRst_11az_LSig_Bit, 0, SCPI_QUERY_CMD},

    //arb data
    {"WT:WIFI:SENSe:FETCh:AZ:ALL:USER:SPECtrum:FLATness:PASSed?", GetVsaRst_11az_Users_FlatnessPassed, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:ALL:USER:SPECtrum:FLATness:DATA?", GetVsaRst_11az_Users_FlatnessData, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:ALL:USER:SPECtrum:FLATness:MASK:UP:DATA?", GetVsaRst_11az_Users_FlatnessMaskUp, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:ALL:USER:SPECtrum:FLATness:MASK:DOWN:DATA?", GetVsaRst_11az_Users_FlatnessMaskDown, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:ALL:USER:SPECtrum:FLATness:SECTion?", GetVsaRst_11az_Users_FlatnessSection, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:ALL:USER:SPECtrum:FLATness:SECTion:MARGin?", GetVsaRst_11az_Users_FlatnessSectionMargin, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:ALL:USER:OFDM:CHANnel:PHASe:RESPonse?", GetVsaRst_11az_Users_ChannelPhaseResponse, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AZ:ALL:USER:OFDM:CHANnel:AMPLitude:RESPonse?", GetVsaRst_11az_Users_ChannelAmplitudeResponse, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_wavegenerator_11az_cmd[] = {
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:PPDU", Set11AZ_PPDUFormat, 0, SCPI_SEQUENTIAL},

    //HE Ranging
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:STBC", Set11AZ_HE_Ranging_STBC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:ULDL", Set11AZ_HE_Ranging_ULDL, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:MCS", Set11AZ_HE_Ranging_MCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:CODing:TYPE", Set11AZ_HE_Ranging_CodingType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:DCM", Set11AZ_HE_Ranging_DCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:GLTF:SIZE", Set11AZ_HE_Ranging_GILTFSize, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:SREUse", Set11AZ_HE_Ranging_SpatialReuse, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:BEAM:CHANge", Set11AZ_HE_Ranging_BeamChange, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:BSS:COLOr", Set11AZ_HE_Ranging_BssColor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:DOPPler", Set11AZ_HE_Ranging_Doppler, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:MIDAmble:PERIodicity", Set11AZ_HE_Ranging_MidamblePeriodicity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:TXOP", Set11AZ_HE_Ranging_TXOP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:BEAM:FORMed", Set11AZ_HE_Ranging_BeamFormed, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:SECUre:MODE", Set11AZ_HE_Ranging_SecureMode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:TX:WINDow", Set11AZ_HE_Ranging_TxWindow, 0, SCPI_SEQUENTIAL},

    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:USER:COUNt", Set11AZ_HE_Ranging_UserCount, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:USER#:NSTS", Set11AZ_HE_Ranging_User_NSTS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:USER#:LTF:REPEat", Set11AZ_HE_Ranging_User_LTFRepeat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:USER#:LTF:KEY:HEX", Set11AZ_HE_Ranging_User_LTFKey, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:HE:RANGing:USER#:LTF:IV:HEX", Set11AZ_HE_Ranging_User_LTFIV, 0, SCPI_SEQUENTIAL},
    
    //HE-TB Ranging
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:NLTF:SYMBols", Set11AZ_TB_MUMIMO_NumLTFSymbols, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:LDPC:EXTRa:SYMBol", Set11AZ_TB_MUMIMO_LDPCExtra, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:AFACtor", Set11AZ_TB_MUMIMO_AFactor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:GLTF:SIZE", Set11AZ_TB_MUMIMO_GLTFSize, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:DOPPler", Set11AZ_TB_MUMIMO_Doppler, 0, SCPI_SEQUENTIAL}, //该命令目前wave工具没有配置
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MIDPeriod", Set11AZ_TB_MUMIMO_MidamblePeriodicity, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:PE", Set11AZ_TB_MUMIMO_PE, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:STBC", Set11AZ_TB_MUMIMO_STBC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:BSS:COLOr", Set11AZ_TB_MUMIMO_BSSColor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:SREUse", Set11AZ_TB_MUMIMO_SpatialReuse, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:TXOP", Set11AZ_TB_MUMIMO_TXOP, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:SECUre:MODE", Set11AZ_TB_MUMIMO_SecureMode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:TX:WINDow", Set11AZ_TB_MUMIMO_TxWindow, 0, SCPI_SEQUENTIAL},
    // 11az TB RU setting
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:RU:COUNt[:SEGment#]", Set11AZ_TB_MUMIMO_RUCnt, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:RUINdex[:RU#][:SEGment#]", Set11AZ_TB_MUMIMO_RUIndex, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:NUMber:USER[:RU#][:SEGment#]", Set11AZ_TB_MUMIMO_RUUserCnt, 0, SCPI_SEQUENTIAL},

    // 11az TB MU-MIMO User in RU
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:AID[:RU#][:USER#][:SEGment#]", Set11AZ_TB_MUMIMO_RUUserAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:CODing:TYPE[:RU#][:USER#][:SEGment#]", Set11AZ_TB_MUMIMO_RUUserCoding, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:DCM[:RU#][:USER#][:SEGment#]", Set11AZ_TB_MUMIMO_RUUserDCM, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:MCS[:RU#][:USER#][:SEGment#]", Set11AZ_TB_MUMIMO_RUUserMCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:NSS[:RU#][:USER#][:SEGment#]", Set11AZ_TB_MUMIMO_RUUserNSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:NSS:STARt[:RU#][:USER#][:SEGment#]", Set11AZ_TB_MUMIMO_RUUserNSSStart, 0, SCPI_SEQUENTIAL},

    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:LTF:REP[:RU#][:USER#][:SEGment#]", Set11AZ_TB_MUMIMO_RUUser_LTFRepeat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:LTF:KEY:HEX[:RU#][:USER#][:SEGment#]", Set11AZ_TB_MUMIMO_RUUser_LTFKey, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AZ:TB:MU:LTF:IV:HEX[:RU#][:USER#][:SEGment#]", Set11AZ_TB_MUMIMO_RUUser_LTFIV, 0, SCPI_SEQUENTIAL},
};
//end 11az cmd

//11ah cmd
static const scpi_command_t scpi_wt_set_vsa_11ah_analyze_cmd[] = {
    {"WT:WIFI:SENSe:CONFigure:ANALy:AH:FRAMe:TYPE", SetVsaAlzAhFrameType, 0, SCPI_SEQUENTIAL},
};

static const scpi_command_t scpi_wt_vsa_11ah_result_cmd[] = {
    // ah data information
    {"WT:WIFI:SENSe:FETCh:AH:PREAmble:TYPE?", GetVsaRst_11ah_PreambleType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:USER:NUMber?", GetVsaRst_11ah_UserNumber, 0, SCPI_QUERY_CMD},

    // ah S1G short preamble
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:VALId:FLAG?", GetVsaRst_11ah_short_ValidFlag, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:RESErved:B0?", GetVsaRst_11ah_short_ReservedB0, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:BW?", GetVsaRst_11ah_short_BW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:STBC?", GetVsaRst_11ah_short_STBC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:ULDL?", GetVsaRst_11ah_short_ULDL, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:NSS?", GetVsaRst_11ah_short_NSS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:NSTS?", GetVsaRst_11ah_short_NSTS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:ID?", GetVsaRst_11ah_short_ID, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:GI?", GetVsaRst_11ah_short_GI, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:CODing:TYPE?", GetVsaRst_11ah_short_CodingType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:LDPC:EXTra?", GetVsaRst_11ah_short_LDPCExtra, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:MCS?", GetVsaRst_11ah_short_MCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:SMOOthing?", GetVsaRst_11ah_short_Smoothing, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:AGGregation?", GetVsaRst_11ah_short_Aggregation, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:LENgth?", GetVsaRst_11ah_short_Length, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:RESPonse:INDication?", GetVsaRst_11ah_short_RespIndication, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:TRAVeling:PILOts?", GetVsaRst_11ah_short_TravelingPilots, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:NDP:INDication?", GetVsaRst_11ah_short_NDPIndication, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:CRC?", GetVsaRst_11ah_short_CRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:TAIL?", GetVsaRst_11ah_short_Tail, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:SIG:BIT:LENgth?", GetVsaRst_11ah_short_SigBitLength, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:SHORt:PREAmble:SIG:BIT:STREam?", GetVsaRst_11ah_short_SigBit, 0, SCPI_QUERY_CMD},

    //S1G Long Preamble SU
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:VALId:FLAG?", GetVsaRst_11ah_long_SU_ValidFlag, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:BW?", GetVsaRst_11ah_long_SU_BW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:STBC?", GetVsaRst_11ah_long_SU_STBC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:ULDL?", GetVsaRst_11ah_long_SU_ULDL, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:NSS?", GetVsaRst_11ah_long_SU_NSS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:NSTS?", GetVsaRst_11ah_long_SU_NSTS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:ID?", GetVsaRst_11ah_long_SU_ID, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:GI?", GetVsaRst_11ah_long_SU_GI, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:CODing:TYPE?", GetVsaRst_11ah_long_SU_CodingType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:LDPC:EXTRa?", GetVsaRst_11ah_long_SU_LDPCExtRa, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:MCS?", GetVsaRst_11ah_long_SU_MCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:BEAM:CHANge?", GetVsaRst_11ah_long_SU_BeamChange, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:SMOOthing?", GetVsaRst_11ah_long_SU_Smoothing, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:AGGregation?", GetVsaRst_11ah_long_SU_Aggregation, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:LENgth?", GetVsaRst_11ah_long_SU_Length, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:RESPonse:INDication?", GetVsaRst_11ah_long_SU_RespIndication, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:TRAVeling:PILOts?", GetVsaRst_11ah_long_SU_TravelingPilots, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:RESErved:A2B12?", GetVsaRst_11ah_long_SU_ReservedA2B12, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:CRC?", GetVsaRst_11ah_long_SU_CRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:TAIL?", GetVsaRst_11ah_long_SU_Tail, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:SIGA:BIT:LENgth?", GetVsaRst_11ah_long_SU_SigABitLength, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:SU:SIGA:BIT:STREam?", GetVsaRst_11ah_long_SU_SigABit, 0, SCPI_QUERY_CMD},

    // S1G long preamble MU
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:VALId:FLAG?", GetVsaRst_11ah_long_MU_ValidFlag, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:BW?", GetVsaRst_11ah_long_MU_BW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:STBC?", GetVsaRst_11ah_long_MU_STBC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:RESErved:A1B2?", GetVsaRst_11ah_long_MU_ReservedA1B2, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:NSS?", GetVsaRst_11ah_long_MU_NSS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:NSTS?", GetVsaRst_11ah_long_MU_NSTS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:GID?", GetVsaRst_11ah_long_MU_GroupID, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:CODing:TYPE:I?", GetVsaRst_11ah_long_MU_CodingTypeI, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:CODing:TYPE:II?", GetVsaRst_11ah_long_MU_CodingTypeII, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:RESErved:A2B1?", GetVsaRst_11ah_long_MU_ReservedA2B1, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:LENgth?", GetVsaRst_11ah_long_MU_Length, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:RESPonse:INDication?", GetVsaRst_11ah_long_MU_RespIndication, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:TRAVeling:PILOts?", GetVsaRst_11ah_long_MU_TravelingPilots, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:CRC?", GetVsaRst_11ah_long_MU_CRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:TAIL?", GetVsaRst_11ah_long_MU_Tail, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:SIGA:BIT:LENgth?", GetVsaRst_11ah_long_MU_SigABitLength, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:LONG:PREAmble:MU:SIGA:BIT:STREam?", GetVsaRst_11ah_long_MU_SigABit, 0, SCPI_QUERY_CMD},

    // S1G S1M preamble
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:VALId:FLAG?", GetVsaRst_11ah_S1M_ValidFlag, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:BW?", GetVsaRst_11ah_S1M_BW, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:STBC?", GetVsaRst_11ah_S1M_STBC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:NSS?", GetVsaRst_11ah_S1M_NSS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:NSTS?", GetVsaRst_11ah_S1M_NSTS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:GI?", GetVsaRst_11ah_S1M_GI, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:CODing:TYPE?", GetVsaRst_11ah_S1M_CodingType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:LDPC:EXTRa?", GetVsaRst_11ah_S1M_LDPCExtra, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:RESErved:B6?", GetVsaRst_11ah_S1M_ReservedB6, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:MCS?", GetVsaRst_11ah_S1M_MCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:AGGregation?", GetVsaRst_11ah_S1M_Aggregation, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:LENgth?", GetVsaRst_11ah_S1M_Length, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:RESPonse:INDication?", GetVsaRst_11ah_S1M_RespIndication, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:SMOOthing?", GetVsaRst_11ah_S1M_Smoothing, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:TRAVeling:PILOts?", GetVsaRst_11ah_S1M_TravelingPilots, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:NDP:INDication?", GetVsaRst_11ah_S1M_NDPIndication, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:CRC?", GetVsaRst_11ah_S1M_CRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:TAIL?", GetVsaRst_11ah_S1M_Tail, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:SIG:BIT:LENgth?", GetVsaRst_11ah_S1M_SigBitLength, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:S1G:S1M:PREAmble:SIG:BIT:STREam?", GetVsaRst_11ah_S1M_SigABit, 0, SCPI_QUERY_CMD},

    // 11ah MU-MIMO user information
    {"WT:WIFI:SENSe:FETCh:AH:MU:ALL:USER:INFOrmation?", GetVsaRst_AH_MUMIMO_AllUserInformation, 0, SCPI_QUERY_CMD}, //一次性获取mu user的全部信息
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:DATA:RATE?", GetVsaRst_AH_MUMIMO_UserDataRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:PSDU:LENgth?", GetVsaRst_AH_MUMIMO_UserPSDULen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:PSDU:CRC?", GetVsaRst_AH_MUMIMO_UserPSDUCRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:MCS?", GetVsaRst_AH_MUMIMO_UserMCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:CODing:TYPE?", GetVsaRst_AH_MUMIMO_UserCodingType, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:MODulation:TYPE?", GetVsaRst_AH_MUMIMO_UserModulation, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:CODing:RATE?", GetVsaRst_AH_MUMIMO_UserCodingRate, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:SIGB:INFO?", GetVsaRst_AH_MUMIMO_UserSIGBInfos, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:SIGB:MCS?", GetVsaRst_AH_MUMIMO_UserSIGBMCS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:SIGB:CRC?", GetVsaRst_AH_MUMIMO_UserSIGBCRC, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:SIGB:TAIL?", GetVsaRst_AH_MUMIMO_UserSIGBTail, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:SIGB:BIT:LENgth?", GetVsaRst_AH_MUMIMO_UserSigBBitLen, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:SIGB:BIT:STREam?", GetVsaRst_AH_MUMIMO_UserSigBBit, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:NSTS?", GetVsaRst_AH_MUMIMO_UserNSTS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:NSS?", GetVsaRst_AH_MUMIMO_UserNSS, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:POWer?", GetVsaRst_AH_MUMIMO_UserPower, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:NSS#:POWer?", GetVsaRst_AH_MUMIMO_UserPower, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:EVM:ALL?", GetVsaRst_AH_MUMIMO_UserEVMALL, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:NSS#:EVM:ALL?", GetVsaRst_AH_MUMIMO_UserEVMALL, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:EVM:DATA?", GetVsaRst_AH_MUMIMO_UserEVMData, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:NSS#:EVM:DATA?", GetVsaRst_AH_MUMIMO_UserEVMData, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:EVM:PILOt?", GetVsaRst_AH_MUMIMO_UserEVMPilot, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SENSe:FETCh:AH:MU:USER#:NSS#:EVM:PILOt?", GetVsaRst_AH_MUMIMO_UserEVMPilot, 0, SCPI_QUERY_CMD},
};

//wave
static const scpi_command_t scpi_wt_wavegenerator_11ah_cmd[] = {
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:PPDU", Set11AH_PPDUFormat, 0, SCPI_SEQUENTIAL},

    //su
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:DUP:INDIcation", Set11AH_SU_DUPIndication, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:NDP:MODE", Set11AH_SU_NDPMode, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:NDP:CMAC:PPDU:BODY", Set11AH_SU_NDPCmacPPDUBodybit, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:STBC", Set11AH_SU_STBC, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:ULDL", Set11AH_SU_ULDL, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:GI", Set11AH_GuardInterval, 0, SCPI_SEQUENTIAL},//guard interval
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:NSS", Set11AH_SU_NSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:MCS", Set11AH_SU_MCS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:CODing:TYPE", Set11AH_SU_CodingType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:BSS:COLOr", Set11AH_SU_BSSColor, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:PARTial:AID", Set11AH_SU_PartialAID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:SMOOthing", Set11AH_SU_Smoothing, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:BEAM:FORMed", Set11AH_SU_Beamformed, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:AGGregation", Set11AH_SU_AGG, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:RESPonse:INDIcation", Set11AH_SU_ResponseIndication, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:TRAVeling:PILOts", Set11AH_SU_TravelingPilots, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:QMAT", Set11AH_SU_QMat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:QMAT:NTX", Set11AH_SU_QMatNtx, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:QMAT:TYPE", Set11AH_SU_QMatType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:QMAT:DELAy", Set11AH_SU_QMatDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:SU:QMAT:MAT", Set11AH_SU_QMatMap, 0, SCPI_SEQUENTIAL},

    //mu
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:GI", Set11AH_MU_GI, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:GID", Set11AH_MU_GroupID, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:RESPonse:INDIcation", Set11AH_MU_ResponseIndication, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:TRAVeling:PILOts", Set11AH_MU_TravelingPilots, 0, SCPI_SEQUENTIAL},

    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:QMAT", Set11AH_MU_QMat, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:QMAT:NTX", Set11AH_MU_QMatNtx, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:QMAT:TYPE", Set11AH_MU_QMatType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:QMAT:DELAy", Set11AH_MU_QMatDelay, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:QMAT:MAT", Set11AH_MU_QMatMap, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:USER:COUNt", Set11AH_MU_UserCnt, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:CODing:TYPE[:USER#]", Set11AH_MU_User_CodingType, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:NSS[:USER#]", Set11AH_MU_User_NSS, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:AH:MU:MCS[:USER#]", Set11AH_MU_User_MCS, 0, SCPI_SEQUENTIAL},
};
//end 11ah cmd

static const scpi_command_t scpi_wt_wavegenerator_CBF_cmd[] = {
    //CBF 输入
    {"WT:WIFI:SOURce:CONFigure:WAVE:CBF:FEED:BACK:BF:ENABle:FLAG", SetCBF_FeedBackBfEnable, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:CBF:MU:FLAG", SetCBF_MUFlag, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:CBF:NUMber:CARRier:GROUping", SetCBF_NumberofCarrierGrouping, 0, SCPI_SEQUENTIAL},
    {"WT:WIFI:SOURce:CONFigure:WAVE:CBF:CODE:BOOK:INDex", SetCBF_CodebookIndex, 0, SCPI_SEQUENTIAL},
    //H 矩阵设置
    {"WT:WIFI:SOURce:CONFigure:WAVE:CBF:H:MATrix", SetCBF_HMatrix, 0, SCPI_SEQUENTIAL},

    //生成完之后获取的组合数据内容
    {"WT:WIFI:SOURce:WAVE:GET:CBF:COMPress:REPOrt:FIELd?", GetCBF_CompressReportFieldArb, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SOURce:WAVE:GET:CBF:MU:EXCLusive:REPOrt:FIELd?", GetCBF_MuExclusiveReportFieldArb, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SOURce:WAVE:GET:CBF:HE:RU:STARt:END:INDEx?", GetCBF_HE_RUStartnEndIndex, 0, SCPI_QUERY_CMD},
    {"WT:WIFI:SOURce:WAVE:GET:CBF:EHT:PARTial:BW:INFO?", GetCBF_ETH_PartialBWInfo, 0, SCPI_QUERY_CMD},
};

static const scpi_command_t scpi_wt_authentication_cmd[] = {
    // 给产测软件认证用的命令，分别是设置秘钥和加密数据返回
    {"WT:SYSTem:CONFigure:AUTHentication:TK", SCPI_SetAuthenticationTK, 0, SCPI_SEQUENTIAL},
    {"WT:SYSTem:FETCh:AUTHentication:PLAIn:TXT?", SCPI_GetAuthenticationEncryptData, 0, SCPI_QUERY_CMD},
};

//listmod cmd
static const scpi_command_t scpi_wt_listmod_cmd[] = {

    //listmod使能/去使能
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:ENABle", SCPI_SetTxListModEnable, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:DISAble", SCPI_SetTxListModDisable, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:ENABle", SCPI_SetRxListModEnable, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:DISAble", SCPI_SetRxListModDisable, 0, SCPI_SEQUENTIAL},

    //启动/停止，区分TX/RX
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:STARt", SCPI_SetListTxSeqStart, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:STARt", SCPI_SetListRxSeqStart, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:CONFigure:LIST:SEQUence:STARt", SCPI_SetListTxRxSeqStart, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:STOP", SCPI_SetListTxSeqStop, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:STOP", SCPI_SetListRxSeqStop, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:CONFigure:LIST:SEQUence:STOP", SCPI_SetListTxRxSeqStop, 0, SCPI_SEQUENTIAL},

    //seq编辑，设置seq场景，0：非组合场景，组合场景下，只能有一个sequence，非组合场景下，最多两个sequence：TX Seq和RX Seq
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:CONFigure:LIST:SEQUence:MOD", SCPI_SetListSeqMod, 0, SCPI_SEQUENTIAL},
    //seq编辑，整个seq trigoffset设置， 只对第一个seg生效，只有TX
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:TRIGer:OFFSet", SCPI_SetListTxSeqTrigerOffset, 0, SCPI_SEQUENTIAL},

    //seq编辑，设置seq下seg最大个数，区分TX/RX
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:SIZE", SCPI_SetListTxSeqSize, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:SIZE", SCPI_SetListRxSeqSize, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:CONFigure:LIST:SEQUence:SIZE", SCPI_SetListTxRxSeqSize, 0, SCPI_SEQUENTIAL},

    //seq编辑，抓取参数设置,只有TX
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:FREQuency", SCPI_SetListTxSeqFreq, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:FREQuency:ALL", SCPI_SetListTxSeqFreqAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:MAXPower", SCPI_SetListTxSeqPower, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:MAXPower:ALL", SCPI_SetListTxSeqPowerAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:RFPOrt", SCPI_SetListTxSeqRfport, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:RFPOrt:ALL", SCPI_SetListTxSeqRfportAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:SAMPle:RATE", SCPI_SetListTxSeqSampleRate, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:SAMPle:RATE:ALL", SCPI_SetListTxSeqSampleRateAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:EXT#:GAIN", SCPI_SetListTxSeqExtGain, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:EXT#:GAIN:ALL", SCPI_SetListTxSeqExtGainAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:TRIGger:TYPE", SCPI_SetListTxSeqTriggerType, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:TRIGger:LEVEL", SCPI_SetListTxSeqTriggerLevel, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:TRIGger:TYPE:ALL", SCPI_SetListTxSeqTriggerTypeAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:TRIGger:LEVEL:ALL", SCPI_SetListTxSeqTriggerLevelAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:TRIGger:GAPTime", SCPI_SetListTxSeqTriggerGaptime, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:TRIGger:GAPTime:ALL", SCPI_SetListTxSeqTriggerGaptimeAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:TRIGger:FRAMetime", SCPI_SetListTxSeqTriggerFrametime, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:TRIGger:FRAMetime:ALL", SCPI_SetListTxSeqTriggerFrametimeAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:TRIGger:TIMEout:ALL", SCPI_SetListTxSeqTriggerTimeoutAll, 0, SCPI_SEQUENTIAL},

    //按跟fpga对齐，vsa应该在测量开始和duration结束都产生同步事件，再由VSG根据自身配置来确定接收或忽略对应事件，该命令只是用于前期调试
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:GENErate:TRIGger:TYPE", SCPI_SetListTxSeqGenTriggerType, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:GENErate:TRIGger:TYPE:ALL", SCPI_SetListTxSeqGenTriggerTypeAll, 0, SCPI_SEQUENTIAL},

    //seq编辑，VSG参数设置,只有RX
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:SAMPle:LIST:SEQUence:RATE", SCPI_SetListRxSeqSampleRate, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:SAMPle:LIST:SEQUence:RATE:ALL", SCPI_SetListRxSeqSampleRateAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:FREQuency", SCPI_SetListRxSeqFreq, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:FREQuency:ALL", SCPI_SetListRxSeqFreqAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:POWEr", SCPI_SetListRxSeqPower, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:POWEr:ALL", SCPI_SetListRxSeqPowerAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:RFPOrt", SCPI_SetListRxSeqRfport, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:RFPOrt:ALL", SCPI_SetListRxSeqRfportAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:WAVE", SCPI_SetListRxSeqWave, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:WAVE:ALL", SCPI_SetListRxSeqWaveAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:EXT#:GAIN", SCPI_SetListRxSeqExtGain, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:EXT#:GAIN:ALL", SCPI_SetListRxSeqExtGainAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:SYNC", SCPI_SetListRxSeqSync, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:SYNC:ALL", SCPI_SetListRxSeqSyncAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:ARB:REPEtion", SCPI_SetListRxSeqArbRepet, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:ARB:REPEtion:ALL", SCPI_SetListRxSeqArbRepetAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:ARB:EXTEnd", SCPI_SetListRxSeqArbExtend, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:ARB:EXTEnd:ALL", SCPI_SetListRxSeqArbExtendAll, 0, SCPI_SEQUENTIAL},
    //seq编辑，设置整个rx seq循环模式，1为sing，只执行一次，0为持续模式，持续seq发送
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:REPEtion", SCPI_SetListRxSeqRepet, 0, SCPI_SEQUENTIAL},

    //对齐CMW命令,enable命令表示第0个seg的开始方式，其中0表示直接启动，1表示测量事件同步，这里要注意当第0个seg配置了测量事件同步时，只接收vsa在测量开始时发过来的事件，否则忽略
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:ENABle", SCPI_SetListRxSeqEnable, 0, SCPI_SEQUENTIAL},
    //对齐CMW命令,incre命令表示除第0个seg的开始方式，其中0表示free run，1表示测量事件同步，这里要注意当除第0个seg配置了测量事件同步时，只接收vsa在duration结束时发过来的事件，否则忽略
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:NB][:GLE][:SLE][:LTE][:NIOT]:SOURce:CONFigure:LIST:SEQUence:INCRement", SCPI_SetListRxSeqIncre, 0, SCPI_SEQUENTIAL},

    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:TRIGger:TIMEout:ALL", SCPI_SetListRxSeqTriggerTimeoutAll, 0, SCPI_SEQUENTIAL},
    //配置list蜂窝模式，由于dut对接测试时，需要遵循严格的lte时序，所以通过该标志控制fpga vsg切换逻辑
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:CELLmod", SCPI_SetListRxSeqCellMod, 0, SCPI_SEQUENTIAL},

    //seq编辑， 相关时间参数设置，区分TX/RX
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:DURAtion", SCPI_SetListTxSeqDuration, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:DURAtion:ALL", SCPI_SetListTxSeqDurationAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:DURAtion", SCPI_SetListRxSeqDuration, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:DURAtion:ALL", SCPI_SetListRxSeqDurationAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:MEAOffset", SCPI_SetListTxSeqMeaoffset, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:MEAOffset:ALL", SCPI_SetListTxSeqMeaoffsetAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:OFFSet", SCPI_SetListRxSeqMeaoffset, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:OFFSet:ALL", SCPI_SetListRxSeqMeaoffsetAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:MEADura", SCPI_SetListTxSeqMeaDur, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:MEADura:ALL", SCPI_SetListTxSeqMeaDurAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:REPEat", SCPI_SetListTxSeqRepeat, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:REPEat:ALL", SCPI_SetListTxSeqRepeatAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:REPEat", SCPI_SetListRxSeqRepeat, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:REPEat:ALL", SCPI_SetListRxSeqRepeatAll, 0, SCPI_SEQUENTIAL},

    //seq编辑， 分析协议设定，只有TX
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:ANALy:DEMOd", SCPI_SetListTxSeqAnalDemod, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:ANALy:DEMOd:ALL", SCPI_SetListTxSeqAnalDemodAll, 0, SCPI_SEQUENTIAL},

    //seq编辑， 分析参数设定，只有TX，且跟具体协议相关，当分析协议为GPRF时，默认只在fpga侧分析功率，其他协议待补充
    //Lte相关参数设定
    {"WT:LTE:SENSe:CONFigure:LIST:SEQUence:SEGM#:SETUp", SCPI_SetListLteTxSeqParam, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:LIST:SEQUence:SEGM#:TDD", SCPI_SetListLteTxSeqTdd, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:LIST:SEQUence:SEGM#:RBALlocation", SCPI_SetListLteTxSeqRbAllocation, 0, SCPI_SEQUENTIAL},

    //seq编辑，相关分析开关，指标统计参数设定，只有TX，且跟具体协议相关，待补充
    //Lte相关参数设定
    {"WT:LTE:SENSe:CONFigure:LIST:SEQUence:SEGM#:MODUlation", SCPI_SetListLteTxSeqModulation, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:LIST:SEQUence:SEGM#:SEMAsk", SCPI_SetListLteTxSeqSemask, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:LIST:SEQUence:SEGM#:ACLR", SCPI_SetListLteTxSeqAclr, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:LIST:SEQUence:SEGM#:PMONitor", SCPI_SetListLteTxSeqPmonitor, 0, SCPI_SEQUENTIAL},
    {"WT:LTE:SENSe:CONFigure:LIST:SEQUence:SEGM#:POWEr", SCPI_SetListLteTxSeqPower, 0, SCPI_SEQUENTIAL},

    //NR5G相关参数设定
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:REDCap", SCPI_SetListNr5gTxSeqRedCap, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:SETUp", SCPI_SetListNr5gTxSeqParam, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:FREQuency", SCPI_SetListNr5gTxSeqFrequency, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:CHANnel:BW", SCPI_SetListNr5gTxSeqBandwidth, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:PLCId", SCPI_SetListNr5gTxSeqPhyCellID, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:TAPOsition", SCPI_SetListNr5gTxSeqDmrsTypeAPos, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:SCSPacing", SCPI_SetListNr5gTxSeqScsSpacing, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:TXBWidth:OFFSet", SCPI_SetListNr5gTxSeqTxBWidthOffset, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:BWPArt", SCPI_SetListNr5gTxSeqBwpPart, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:BWPArt:PUSCh:DMTA", SCPI_SetListNr5gTxSeqBwpPartPuschDmrsTypeA, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:BWPArt:PUSCh:DMTB", SCPI_SetListNr5gTxSeqBwpPartPuschDmrsTypeB, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:BWPArt:PUSCh:DMTA:DFTPrecoding", SCPI_SetListNr5gTxSeqBwpPartPuschDmrsTypeADftPrecoding, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:BWPArt:PUSCh", SCPI_SetListNr5gTxSeqBwpPartPusch, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:BWPArt:PUSCh:ADDItional", SCPI_SetListNr5gTxSeqBwpPartPuschAdditional, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:BWPArt:PUSCh:SGENeration", SCPI_SetListNr5gTxSeqBwpPartPuschSGeneneration, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:MODUlation", SCPI_SetListNr5gTxSeqModulation, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:SEMAsk", SCPI_SetListNr5gTxSeqSemask, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:ACLR", SCPI_SetListNr5gTxSeqAclr, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:PMONitor", SCPI_SetListNr5gTxSeqPmonitor, 0, SCPI_SEQUENTIAL},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:POWEr", SCPI_SetListNr5gTxSeqPower, 0, SCPI_SEQUENTIAL},
    //NR5G相关参数查询
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:BWPArt?", SCPI_GetListNr5gTxSeqBwpPart, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:BWPArt:PUSCh:DMTA?", SCPI_GetListNr5gTxSeqBwpPartPuschDmrsTypeA, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:BWPArt:PUSCh:DMTB?", SCPI_GetListNr5gTxSeqBwpPartPuschDmrsTypeB, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:CONFigure:LIST:SEQUence:SEGM#:BWPArt:PUSCh:DFTPrecoding?", SCPI_GetListNr5gTxSeqBwpPartPuschDftPrecoding, 0, SCPI_QUERY_CMD},

    //seq删除
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:DELEte", SCPI_DeleteListTxSeqSeg, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:CONFigure:LIST:SEQUence:DELEte:ALL", SCPI_DeleteListTxSeqSegAll, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:DELEte", SCPI_DeleteListRxSeqSeg, 0, SCPI_SEQUENTIAL},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:CONFigure:LIST:SEQUence:DELEte:ALL", SCPI_DeleteListRxSeqSegAll, 0, SCPI_SEQUENTIAL},

    //seq状态查询
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:FETCH:LIST:SEQUence:STATe?", SCPI_GetListTxSeqState, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:FETCH:LIST:SEQUence:ALL:STATe?", SCPI_GetListTxSeqAllState, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:FETCH:LIST:SEQUence:STATe?", SCPI_GetListRxSeqState, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:FETCH:LIST:SEQUence:ALL:STATe?", SCPI_GetListRxSeqAllState, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:FETCH:LIST:SEQUence:STATe?", SCPI_GetListTxRxSeqState, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:FETCH:LIST:SEQUence:ALL:STATe?", SCPI_GetListTxRxSeqAllState, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:FETCH:LIST:SEQUence:CAPTure:STATe?", SCPI_GetListTxSeqCapState, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:FETCH:LIST:SEQUence:ALL:CAPTure:STATe?", SCPI_GetListTxSeqAllCapState, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:FETCH:LIST:SEQUence:TRANs:STATe?", SCPI_GetListRxSeqTransState, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SOURce:FETCH:LIST:SEQUence:ALL:TRANs:STATe?", SCPI_GetListRxSeqAllTransState, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:FETCH:LIST:SEQUence:ANALy:STATe?", SCPI_GetListTxSeqAnalyState, 0, SCPI_QUERY_CMD},
    {"WT[:WIFI][:BT][:ZIGBee][:GPRF][:GLE][:SLE][:WSUN][:ZWAVe][:LTE][:NR][:NIOT]:SENSe:FETCH:LIST:SEQUence:ALL:ANALy:STATe?", SCPI_GetListTxSeqAllAnalyState, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:ALL:SEGMent:STATe?", SCPI_GetListLteTxSeqAllSegState, 0, SCPI_QUERY_CMD},

    //seq相关结果获取，跟具体协议相关，只有TX有效
    //GPRF
    {"WT:GPRF:SENSe:FETCH:LIST:SEQUence:pOWEr?", SCPI_GetListTxSeqPowerResult, 0, SCPI_QUERY_CMD},
    {"WT:GPRF:SENSe:FETCH:LIST:SEQUence:ALL:pOWEr?", SCPI_GetListTxSeqAllPowerResult, 0, SCPI_QUERY_CMD},

    //LTE
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:CURRent?", SCPI_GetListLteTxSeqSegModCurrent, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:AVERage?", SCPI_GetListLteTxSeqSegModAverage, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:SDEViation?", SCPI_GetListLteTxSeqSegModSdeviation, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:EXTReme?", SCPI_GetListLteTxSeqSegModExtreme, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:CURRent?", SCPI_GetListLteTxSeqSegIemissionMarginCurrent, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:AVERage?", SCPI_GetListLteTxSeqSegIemissionMarginAverage, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:EXTReme?", SCPI_GetListLteTxSeqSegIemissionMarginExtreme, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:SDEViation?", SCPI_GetListLteTxSeqSegIemissionMarginSdeviation, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:CURRent:RBINdex?", SCPI_GetListLteTxSeqSegIemissionMarginCurrentRbindex, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:EXTReme:RBINdex?", SCPI_GetListLteTxSeqSegIemissionMarginExtremeRbindex, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:ESFLatness:CURRent?", SCPI_GetListLteTxSeqSegEsflatnesCurrent, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:ESFLatness:AVERage?", SCPI_GetListLteTxSeqSegEsflatnesAverage, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:ESFLatness:EXTReme?", SCPI_GetListLteTxSeqSegEsflatnesExtreme, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:ESFLatness:SDEViation?", SCPI_GetListLteTxSeqSegEsflatnesSdeviation, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:ESFLatness:CURRent:SCINdex?", SCPI_GetListLteTxSeqSegEsflatnesCurrentScindex, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:CURRent?", SCPI_GetListLteTxSeqSegSemaskCurrent, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:AVERage?", SCPI_GetListLteTxSeqSegSemaskAverage, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:SDEViation?", SCPI_GetListLteTxSeqSegSemaskSdeviation, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:EXTReme?", SCPI_GetListLteTxSeqSegSemaskExtreme, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin?", SCPI_GetListLteTxSeqSegSemaskMargin, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin:CURRent:NEGativ?", SCPI_GetListLteTxSeqSegSemaskMarginCurrentNegativ, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin:CURRent:POSitiv?", SCPI_GetListLteTxSeqSegSemaskMarginCurrentPositiv, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin:AVERage:NEGativ?", SCPI_GetListLteTxSeqSegSemaskMarginAverageNegativ, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin:AVERage:POSitiv?", SCPI_GetListLteTxSeqSegSemaskMarginAveragePositiv, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin::MINimum:NEGativ?", SCPI_GetListLteTxSeqSegSemaskMarginMinimumNegativ, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin::MINimum:POSitiv?", SCPI_GetListLteTxSeqSegSemaskMarginMinimumPositiv, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:ACLR:CURRent?", SCPI_GetListLteTxSeqSegAclrCurrent, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:ACLR:AVERage?", SCPI_GetListLteTxSeqSegAclrAverage, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:PMONitor:RMS?", SCPI_GetListLteTxSeqSegPmonitorRms, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:PMONitor:PEAK?", SCPI_GetListLteTxSeqSegPmonitorPeak, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:POWEr:CURRent?", SCPI_GetListLteTxSeqSegPowerCurrent, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:POWEr:AVERage?", SCPI_GetListLteTxSeqSegPowerAverage, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:POWEr:MINimum?", SCPI_GetListLteTxSeqSegPowerMinimum, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:POWEr:MAXimum?", SCPI_GetListLteTxSeqSegPowerMaximum, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:POWEr:SDEViation?", SCPI_GetListLteTxSeqSegPowerSdeviation, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:DALLocation?", SCPI_GetListLteTxSeqSegSemaskDallocation, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:ACLR:DALLocation?", SCPI_GetListLteTxSeqSegAclrDallocation, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:DALLocation?", SCPI_GetListLteTxSeqSegModulationDallocation, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:DMODulation?", SCPI_GetListLteTxSeqSegModulationDModu, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMask:DCHType?", SCPI_GetListLteTxSeqSegSemaskDchtype, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:ACLR:DCHType?", SCPI_GetListLteTxSeqSegAclrDchtype, 0, SCPI_QUERY_CMD},
    {"WT:LTE:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:DCHType? ", SCPI_GetListLteTxSeqSegModulationDchtype, 0, SCPI_QUERY_CMD},

    //NR5G相关参数获取
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:CURRent?", SCPI_GetListNr5gTxSeqSegModCurrent, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:AVERage?", SCPI_GetListNr5gTxSeqSegModAverage, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:SDEViation?", SCPI_GetListNr5gTxSeqSegModSdeviation, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:EXTReme?", SCPI_GetListNr5gTxSeqSegModExtreme, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:CURRent?", SCPI_GetListNr5gTxSeqSegIemissionMarginCurrent, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:AVERage?", SCPI_GetListNr5gTxSeqSegIemissionMarginAverage, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:EXTReme?", SCPI_GetListNr5gTxSeqSegIemissionMarginExtreme, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:SDEViation?", SCPI_GetListNr5gTxSeqSegIemissionMarginSdeviation, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:CURRent:RBINdex?", SCPI_GetListNr5gTxSeqSegIemissionMarginCurrentRbindex, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:IEMission:CC#:MARGin:EXTReme:RBINdex?", SCPI_GetListNr5gTxSeqSegIemissionMarginExtremeRbindex, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:ESFLatness:CURRent?", SCPI_GetListNr5gTxSeqSegEsflatnesCurrent, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:ESFLatness:AVERage?", SCPI_GetListNr5gTxSeqSegEsflatnesAverage, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:ESFLatness:EXTReme?", SCPI_GetListNr5gTxSeqSegEsflatnesExtreme, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:ESFLatness:SDEViation?", SCPI_GetListNr5gTxSeqSegEsflatnesSdeviation, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:ESFLatness:CURRent:SCINdex?", SCPI_GetListNr5gTxSeqSegEsflatnesCurrentScindex, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:CURRent?", SCPI_GetListNr5gTxSeqSegSemaskCurrent, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:AVERage?", SCPI_GetListNr5gTxSeqSegSemaskAverage, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:SDEViation?", SCPI_GetListNr5gTxSeqSegSemaskSdeviation, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:EXTReme?", SCPI_GetListNr5gTxSeqSegSemaskExtreme, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin?", SCPI_GetListNr5gTxSeqSegSemaskMargin, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin:CURRent:NEGativ?", SCPI_GetListNr5gTxSeqSegSemaskMarginCurrentNegativ, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin:CURRent:POSitiv?", SCPI_GetListNr5gTxSeqSegSemaskMarginCurrentPositiv, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin:AVERage:NEGativ?", SCPI_GetListNr5gTxSeqSegSemaskMarginAverageNegativ, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin:AVERage:POSitiv?", SCPI_GetListNr5gTxSeqSegSemaskMarginAveragePositiv, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin::MINimum:NEGativ?", SCPI_GetListNr5gTxSeqSegSemaskMarginMinimumNegativ, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:SEMAsk:MARGin::MINimum:POSitiv?", SCPI_GetListNr5gTxSeqSegSemaskMarginMinimumPositiv, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:ACLR:CURRent?", SCPI_GetListNr5gTxSeqSegAclrCurrent, 0, SCPI_QUERY_CMD},
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:ACLR:AVERage?", SCPI_GetListNr5gTxSeqSegAclrAverage, 0, SCPI_QUERY_CMD},

    
    {"WT:NR:SENSe:FETCH:LIST:SEQUence:SEGM#:MODUlation:DMODulation?", SCPI_GetListNr5gTxSeqSegModulationDModu, 0, SCPI_QUERY_CMD},
    
};


static void init_scpi_commands()
{
    scpi_vector_cmd.clear();
    scpi_cmd_insert(scpi_system_cmd);
    scpi_cmd_insert(scpi_wt_query_cmd);
    scpi_cmd_insert(scpi_wt_system_cmd);
    scpi_cmd_insert(scpi_wt_set_vsa_cmd);
    scpi_cmd_insert(scpi_wt_set_vsa_analyze_cmd);
    scpi_cmd_insert(scpi_wt_set_vsa_tb_analyze_cmd);
    scpi_cmd_insert(scpi_wt_set_vsa_11az_analyze_cmd);
    scpi_cmd_insert(scpi_wt_set_vsa_11ah_analyze_cmd);
    scpi_cmd_insert(scpi_wt_set_vsa_11ba_analyze_cmd);
    scpi_cmd_insert(scpi_wt_get_vsa_cmd);
    scpi_cmd_insert(scpi_wt_vsa_arb_result_cmd);
    scpi_cmd_insert(scpi_wt_get_11agb_datainfo_cmd);
    scpi_cmd_insert(scpi_wt_get_11n_datainfo_cmd);
    scpi_cmd_insert(scpi_wt_get_11ac_datainfo_cmd);
    scpi_cmd_insert(scpi_wt_get_11ax_datainfo_cmd);
    scpi_cmd_insert(scpi_wt_get_11ax_tf_cmd);
    scpi_cmd_insert(scpi_wt_get_bt_result_cmd);
    scpi_cmd_insert(scpi_wt_get_zigbee_result_cmd);
    scpi_cmd_insert(scpi_wt_set_vsg_cmd);
    scpi_cmd_insert(scpi_wt_get_vsg_cmd);
    scpi_cmd_insert(scpi_wt_get_ofdma_result_cmd);
    scpi_cmd_insert(scpi_wt_get_wifi_psdu_cmd);
    scpi_cmd_insert(scpi_wt_wifi_cmimo_cmd);
    scpi_cmd_insert(scpi_wt_get_wifi_common_result_cmd);
    scpi_cmd_insert(scpi_common_cmd);
    scpi_cmd_insert(scpi_wt_vsa_11b_graphic_cmd);
    scpi_cmd_insert(scpi_wt_vsa_11be_result_cmd);
    scpi_cmd_insert(scpi_wt_vsa_11ba_result_cmd);
    scpi_cmd_insert(scpi_wt_vsa_11az_result_cmd);
    scpi_cmd_insert(scpi_wt_vsa_11ah_result_cmd);
    scpi_cmd_insert(scpi_wt_common_graphic_cmd);
    scpi_cmd_insert(scpi_wt_wavegenerator_cmd);
    scpi_cmd_insert(scpi_wt_wavegenerator_11ac_mumimo_cmd);
    scpi_cmd_insert(scpi_wt_wavegenerator_11be_cmd);
    scpi_cmd_insert(scpi_wt_wavegenerator_11be_tb_cmd);
    scpi_cmd_insert(scpi_wt_wavegenerator_11ba_cmd);
    scpi_cmd_insert(scpi_wt_wavegenerator_11az_cmd);
    scpi_cmd_insert(scpi_wt_wavegenerator_11ah_cmd);
    scpi_cmd_insert(scpi_wt_wavegenerator_channel_model);
    scpi_cmd_insert(scpi_wt_wavegenerator_CBF_cmd);
    scpi_cmd_insert(scpi_wt_vsa_data_info_plus_cmd);
    scpi_cmd_insert(scpi_wt_vsg_analyze_param_cmd);
    scpi_cmd_insert(scpi_wt_vsg_result_cmd);
    scpi_cmd_insert(scpi_wt_digtal_iq_cmd);
    scpi_cmd_insert(scpi_wt_wifi_ibf_cmd);
    scpi_cmd_insert(scpi_wt_8080_2port_cmd);
    scpi_cmd_insert(scpi_wt_wifi_tf_tb_pair_cmd);
    scpi_cmd_insert(scpi_wt_calibration_cmd);
    scpi_cmd_insert(scpi_wt_pac_cmd);
    scpi_cmd_insert(scpi_wt_admin_tool_cmd);
    scpi_cmd_insert(scpi_wt_monitor_cmd);
    scpi_cmd_insert(scpi_wt_vsg_devm_cmd);
    scpi_cmd_insert(scpi_wt_hw_config_cmd);
    scpi_cmd_insert(scpi_wt_vsa_average_cmd);
    scpi_cmd_insert(scpi_wt_vsa_special_api_cmd);
    scpi_cmd_insert(scpi_wt_vsa_sle_result_cmd);
    scpi_cmd_insert(scpi_wt_authentication_cmd);

    // 蜂窝SCPI
    SCPI_Insert_Extend_3GPP(scpi_vector_cmd);

    scpi_cmd_insert(scpi_wt_vsa_wisun_result_cmd);
    scpi_cmd_insert(scpi_wt_vsa_zwave_result_cmd);
    scpi_cmd_insert(scpi_wt_listmod_cmd);
    scpi_cmd_insert(scpi_cmd_ends);
}

static bool scpi_once_init = (init_scpi_commands(), true);

static scpi_interface_t scpi_interface = {
    SCPI_Error,
    SCPI_Write,
    SCPI_Control,
    SCPI_Flush,
    SCPI_Reset,
};

static void ResetContextBuffer(scpi_t *context, unique_ptr<char[]> &buffer, int bufLen = SCPI_INPUT_BUFFER_LENGTH)
{
    //初始化ScpiContext
    buffer.reset(new (std::nothrow) char[bufLen]);
    context->buffer.length = bufLen;
    context->buffer.position = 0;
    context->buffer.data = buffer.get();
    memset(context->buffer.data, 0, bufLen);
}

void ScpiUtils::InitScpiContext(scpi_t *context)
{
    memset(context, 0, sizeof(scpi_t));
    context->cmdlist = &scpi_vector_cmd[0];
    context->pinterface = &scpi_interface;
    context->registers = m_scpi_regs;
    context->units = scpi_units_def;
    context->error_queue = (scpi_error_queue_t)m_scpi_error_queue;
    ResetContextBuffer(context, m_scpi_input_buffer);

    SCPI_Init(context);
    m_UserParam.Reset(); //初始化相关指针内容
    m_UserParam.ClientFd = m_Fd;
    m_UserParam.JsonRoot = &m_JsonRoot;
    context->user_context = &m_UserParam;
    context->cmd_ack = m_UserParam.m_ScpiResponse;
}

ScpiUtils::ScpiUtils(const int Fd, Json::Value &ConfJson, int Port)
    : m_Fd(Fd), m_JsonRoot(ConfJson), m_Port(Port)
{
    //初始化Scpi_Context
    memset(m_scpi_regs, 0, sizeof(m_scpi_regs));
    InitScpiContext(&m_ScpiContext);
}

ScpiUtils::~ScpiUtils()
{
    //析构时，删除对应连接的监听对象
    MonitorMgr::Instance().DelMonitor(m_Fd);
}

int ScpiUtils::Connect()
{
    int Ret = WT_OK;
    if (m_Port == ScpiConfig::Instance().NormalLinkPort)
    {
        Ret = m_UserParam.Connect(LINK_TYPE_NORMAL);
    }
    else if (m_Port == ScpiConfig::Instance().ManagerLinkPort)
    {
        Ret = m_UserParam.Connect(LINK_TYPE_MANAGER);
    }
    else if (m_Port == ScpiConfig::Instance().QueryLinkPort)
    {
        m_UserParam.TesterLinkType = LINK_TYPE_QUERY;
    }
    else if (m_Port == ScpiConfig::Instance().InnerLinkPort)
    {
        m_UserParam.TesterLinkType = LINK_TYPE_INVALID;
    }

    if (WT_ERR_CODE_OK == Ret)
    {
        char IP[16] = {0};
        int Port = 0;
        GetSockPeerInfo(m_UserParam.ClientFd, IP, Port);
        gettimeofday(&m_UserParam.m_ConnetTestTime, NULL);
        Ret = ScpiConnInfo::Instance().AddLink(IP, Port, m_UserParam.TesterLinkType, m_UserParam.ConnID);
    }
    return Ret;
}

int ScpiUtils::CheckConnectStatus(bool TestConnect)
{
    return m_UserParam.CheckConnectStatus(TestConnect);
}

int ScpiUtils::SCPIParseCmd(const char *Data, int DataLen)
{
    if (DataLen > m_ScpiContext.buffer.length)
    {
        ResetContextBuffer(&m_ScpiContext, m_scpi_input_buffer, DataLen + 1024);
    }
    int iRet = SCPI_Input(&m_ScpiContext, Data, DataLen);
    return iRet;
}

int ScpiUtils::GetMoniPort(int MoniVsg)
{
    return m_UserParam.GetMoniPort(MoniVsg);
}

static int demo_scpi(ScpiUtils *pthis)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "SCPI Interactive demo\r\n");
    char smbuffer[256];
    while (1)
    {
        memset(smbuffer, 0, sizeof(smbuffer));

        if (NULL == fgets(smbuffer, sizeof(smbuffer) - 1, stdin))
        {
            break;
        }
        pthis->SCPIParseCmd(smbuffer, strlen(smbuffer));
    }
    return WT_OK;
}

int ScpiUtils::InteractiveSCPI()
{
    thread t(demo_scpi, this);
    t.detach();

    return WT_OK;
}

void ScpiUtils::ClearTimer(std::string TimerName)
{
    m_UserParam.m_WTTimer.ClearTimer(TimerName);
}
void ScpiUtils::StartTimer(std::string TimerName)
{
    m_UserParam.m_WTTimer.StartTimer(TimerName);
}
void ScpiUtils::StopTimer(std::string TimerName)
{
    m_UserParam.m_WTTimer.StopTimer(TimerName);
}
void ScpiUtils::SetTimerEnable(bool Enable)
{
    m_UserParam.m_WTTimer.SetTimerEnable(Enable);
}
