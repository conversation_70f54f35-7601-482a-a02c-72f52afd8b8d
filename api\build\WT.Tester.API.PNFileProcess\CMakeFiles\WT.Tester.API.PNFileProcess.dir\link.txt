/usr/bin/c++ -fPIC  -std=c++11 -MMD -MP -g  -shared -Wl,-soname,libWT.Tester.API.PNFileProcess.so -o ../lib/libWT.Tester.API.PNFileProcess.so CMakeFiles/WT.Tester.API.PNFileProcess.dir/SignalFromCsv.cpp.o CMakeFiles/WT.Tester.API.PNFileProcess.dir/SignalProcess.cpp.o CMakeFiles/WT.Tester.API.PNFileProcess.dir/SignalInfo.cpp.o CMakeFiles/WT.Tester.API.PNFileProcess.dir/resample.cpp.o CMakeFiles/WT.Tester.API.PNFileProcess.dir/SignalFromCMIMO.cpp.o CMakeFiles/WT.Tester.API.PNFileProcess.dir/SignalFromMatlab.cpp.o CMakeFiles/WT.Tester.API.PNFileProcess.dir/WaveFileCommon.cpp.o   -L/home/<USER>/code/WT328_1/api/build/lib  -Wl,-rpath,/home/<USER>/code/WT328_1/api/build/lib -lpthread -lm -lmat -lmx ../lib/libWT.Tester.API.Common.so ../lib/libWT.Tester.API.MAC.Encryption.so -lpthread -lm 
