/*
 * @Description: listmod相关命令
 * @Autor: 
 * @Date: 20240703
 */

#ifndef SCPI_LISTMOD_H_
#define SCPI_LISTMOD_H_

#include "scpi/scpi.h"

#ifdef __cplusplus
extern "C"
{
#endif

    //listmod使能/去使能
    scpi_result_t SCPI_SetTxListModEnable(scpi_t *context);
    scpi_result_t SCPI_SetTxListModDisable(scpi_t *context);
    scpi_result_t SCPI_SetRxListModEnable(scpi_t *context);
    scpi_result_t SCPI_SetRxListModDisable(scpi_t *context);

    //list seq启动/停止
    scpi_result_t SCPI_SetListTxSeqStart(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqStart(scpi_t *context);
    scpi_result_t SCPI_SetListTxRxSeqStart(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqStop(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqStop(scpi_t *context);
    scpi_result_t SCPI_SetListTxRxSeqStop(scpi_t *context);

    //seq编辑，设置seq场景，0：非组合场景，组合场景下，只能有一个sequence，非组合场景下，最多两个sequence：TX Seq和RX Seq
    scpi_result_t SCPI_SetListSeqMod(scpi_t *context);
    //seq编辑，整个seq trigoffset设置， 只对第一个seg生效，只有TX
    scpi_result_t SCPI_SetListTxSeqTrigerOffset(scpi_t *context);

    //seq编辑，设置seq大小
    scpi_result_t SCPI_SetListTxSeqSize(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqSize(scpi_t *context);
    scpi_result_t SCPI_SetListTxRxSeqSize(scpi_t *context);

    //seq编辑，设置抓取参数
    scpi_result_t SCPI_SetListTxSeqFreq(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqFreqAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqPower(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqPowerAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqRfport(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqRfportAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqSampleRate(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqSampleRateAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqExtGain(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqExtGainAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqTriggerType(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqTriggerLevel(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqTriggerTypeAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqTriggerLevelAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqTriggerGaptime(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqTriggerGaptimeAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqTriggerFrametime(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqTriggerFrametimeAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqTriggerTimeout(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqTriggerTimeoutAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqGenTriggerType(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqGenTriggerTypeAll(scpi_t *context);


    //seq编辑，设置vsg参数
    scpi_result_t SCPI_SetListRxSeqFreq(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqFreqAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqPower(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqPowerAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqRfport(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqRfportAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqSampleRate(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqSampleRateAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqExtGain(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqExtGainAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqWave(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqWaveAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqSync(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqSyncAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqTriggerType(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqTriggerTypeAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqArbRepet(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqArbRepetAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqArbExtend(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqArbExtendAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqRepet(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqEnable(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqCellMod(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqIncre(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqTriggerTimeoutAll(scpi_t *context);

    //seq编辑，设置时间参数
    scpi_result_t SCPI_SetListTxSeqDuration(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqDurationAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqDuration(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqDurationAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqMeaoffset(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqMeaoffsetAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqMeaoffset(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqMeaoffsetAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqMeaDur(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqMeaDurAll(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqRepeat(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqRepeatAll(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqRepeat(scpi_t *context);
    scpi_result_t SCPI_SetListRxSeqRepeatAll(scpi_t *context);

    //seq编辑，设置seq的分析协议
    scpi_result_t SCPI_SetListTxSeqAnalDemod(scpi_t *context);
    scpi_result_t SCPI_SetListTxSeqAnalDemodAll(scpi_t *context);

    //LTE listmod seq配置
    scpi_result_t SCPI_SetListLteTxSeqParam(scpi_t *context);
    scpi_result_t SCPI_SetListLteTxSeqTdd(scpi_t *context);
    scpi_result_t SCPI_SetListLteTxSeqRbAllocation(scpi_t *context);
    scpi_result_t SCPI_SetListLteTxSeqModulation(scpi_t *context);
    scpi_result_t SCPI_SetListLteTxSeqSemask(scpi_t *context);
    scpi_result_t SCPI_SetListLteTxSeqAclr(scpi_t *context);
    scpi_result_t SCPI_SetListLteTxSeqPmonitor(scpi_t *context);
    scpi_result_t SCPI_SetListLteTxSeqPower(scpi_t *context);

    //NR5G listmod seq配置
    scpi_result_t SCPI_SetListNr5gTxSeqRedCap(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqParam(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqFrequency(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqBandwidth(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqPhyCellID(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqDmrsTypeAPos(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqScsSpacing(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqTxBWidthOffset(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqBwpPart(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqBwpPartPuschDmrsTypeA(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqBwpPartPuschDmrsTypeB(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqBwpPartPuschDmrsTypeADftPrecoding(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqBwpPartPusch(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqBwpPartPuschAdditional(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqBwpPartPuschSGeneneration(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqModulation(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqSemask(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqAclr(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqPmonitor(scpi_t *context);
    scpi_result_t SCPI_SetListNr5gTxSeqPower(scpi_t *context);

    scpi_result_t SCPI_GetListNr5gTxSeqBwpPart(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqBwpPartPuschDmrsTypeA(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqBwpPartPuschDmrsTypeB(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqBwpPartPuschDftPrecoding(scpi_t *context);

    //seq删除
    scpi_result_t SCPI_DeleteListTxSeqSeg(scpi_t *context);
    scpi_result_t SCPI_DeleteListTxSeqSegAll(scpi_t *context);
    scpi_result_t SCPI_DeleteListRxSeqSeg(scpi_t *context);
    scpi_result_t SCPI_DeleteListRxSeqSegAll(scpi_t *context);

    //状态查询以及结果获取
    scpi_result_t SCPI_GetListTxSeqState(scpi_t *context);
    scpi_result_t SCPI_GetListTxSeqAllState(scpi_t *context);
    scpi_result_t SCPI_GetListRxSeqState(scpi_t *context);
    scpi_result_t SCPI_GetListRxSeqAllState(scpi_t *context);
    scpi_result_t SCPI_GetListTxRxSeqState(scpi_t *context);
    scpi_result_t SCPI_GetListTxRxSeqAllState(scpi_t *context);
    scpi_result_t SCPI_GetListTxSeqCapState(scpi_t *context);
    scpi_result_t SCPI_GetListTxSeqAllCapState(scpi_t *context);
    scpi_result_t SCPI_GetListRxSeqTransState(scpi_t *context);
    scpi_result_t SCPI_GetListRxSeqAllTransState(scpi_t *context);
    scpi_result_t SCPI_GetListTxSeqAnalyState(scpi_t *context);
    scpi_result_t SCPI_GetListTxSeqAllAnalyState(scpi_t *context);
    scpi_result_t SCPI_GetListTxSeqPowerResult(scpi_t *context);
    scpi_result_t SCPI_GetListTxSeqAllPowerResult(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqAllSegState(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegModCurrent(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegModAverage(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegModSdeviation(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegModExtreme(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginCurrent(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginAverage(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginExtreme(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginSdeviation(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginCurrentRbindex(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegIemissionMarginExtremeRbindex(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegEsflatnesCurrent(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegEsflatnesAverage(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegEsflatnesExtreme(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegEsflatnesSdeviation(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegEsflatnesCurrentScindex(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskCurrent(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskAverage(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskSdeviation(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskExtreme(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskMargin(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginCurrentNegativ(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginCurrentPositiv(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginAverageNegativ(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginAveragePositiv(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginMinimumNegativ(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskMarginMinimumPositiv(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegAclrCurrent(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegAclrAverage(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegPmonitorRms(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegPmonitorPeak(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegPowerCurrent(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegPowerAverage(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegPowerMinimum(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegPowerMaximum(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegPowerSdeviation(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskDallocation(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegAclrDallocation(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegModulationDallocation(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegModulationDModu(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegSemaskDchtype(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegAclrDchtype(scpi_t *context);
    scpi_result_t SCPI_GetListLteTxSeqSegModulationDchtype(scpi_t *context);

    //NR5G相关结果获取
    scpi_result_t SCPI_GetListNr5gTxSeqSegModCurrent(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegModAverage(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegModSdeviation(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegModExtreme(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegModulationDModu(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegIemissionMarginCurrent(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegIemissionMarginAverage(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegIemissionMarginExtreme(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegIemissionMarginSdeviation(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegIemissionMarginCurrentRbindex(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegIemissionMarginExtremeRbindex(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegEsflatnesCurrent(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegEsflatnesAverage(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegEsflatnesExtreme(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegEsflatnesSdeviation(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegEsflatnesCurrentScindex(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegSemaskCurrent(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegSemaskAverage(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegSemaskSdeviation(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegSemaskExtreme(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegSemaskMargin(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegSemaskMarginCurrentNegativ(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegSemaskMarginCurrentPositiv(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegSemaskMarginAverageNegativ(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegSemaskMarginAveragePositiv(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegSemaskMarginMinimumNegativ(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegSemaskMarginMinimumPositiv(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegAclrCurrent(scpi_t *context);
    scpi_result_t SCPI_GetListNr5gTxSeqSegAclrAverage(scpi_t *context);

#ifdef __cplusplus
}
#endif

#endif
