//*****************************************************************************
//  File: analysis.h
//  算法功能封装
//  Data: 2016.8.23
//*****************************************************************************
#ifndef __WT_ANALYSIS_H__
#define __WT_ANALYSIS_H__

#include <string>
#include <array>
#include <vector>
#include <memory>
#include "alg/includeAll.h"
#include "wt-calibration.h"
#include "mempool.h"
#include "sigfile.h"
#include "result.h"
#include "resultdef.h"
#include "license.h"
#include "../../scpi/scpiapiwrapper/scpi_3gpp_base.h"
#define MAX_SEGMENT_CNT 2                   //最大segment段数，8080时最大为2，其他为1

struct VsaParam;
struct DevCalParam;
#define VAL_DAT -999
const double VAL_MIN = -999.99;
//数据信息
struct DataBufInfo
{
    std::unique_ptr<char[]> Buf; //保存采集到的数据所使用的buffer
    int BufLen = 0;              //buffer长度
    int DataLen = 0;             //buffer中实际数据长度

    DataBufInfo() = default;

    DataBufInfo(DataBufInfo &&Data)
    {
        Move(Data);
    }

    DataBufInfo& operator=(DataBufInfo &&Data)
    {
        Move(Data);
        return *this;
    }

    void Clear()
    {
        BufLen = 0;
        DataLen = 0;
        Buf.reset(nullptr);
    }

private:
    //将本对象的数据和buffer转移到目的对象中
    void Move(DataBufInfo &Data)
    {
        Buf.reset(Data.Buf.release());
        BufLen = Data.BufLen;
        DataLen = Data.DataLen;
        Data.DataLen = 0;
        Data.BufLen = 0;
    }
};

//平均类型
enum WT_AVG_TYPE
{
    MULTI_CAP_AVG,   //多次采集平均
    SINGLE_CAP_AVG   //一次采集内的多帧平均
};

//平均类型
enum WT_IQ_IMB_RESET_TYPE
{
    IQ_IMB_NEED_RESET,
    IQ_IMB_ALREAD_RESET,
    IQ_IMB_NOT_NEED_RESET = IQ_IMB_ALREAD_RESET,
};

//平均模式定义
enum WT_AVG_MODE
{
    MULTI_CNT_AVG,   //多次平均
    SLIDE_AVG        //滑动平均
};

enum WT_DATA_TYPE_E
{
    WT_RAW_CAP_DATA,    //原始采集的数据
    WT_COMPESATE_DATA   //补偿过的数据
};

enum WT_ALZ_PARAM_TYPE
{
    WT_ALZ_PARAM_COMMON,
    WT_ALZ_PARAM_FFT,
    WT_ALZ_PARAM_WIFI,
    WT_ALZ_PARAM_BT,
    WT_ALZ_PARAM_ZIGBEE,
    WT_ALZ_PARAM_ZWAVE,
    WT_ALZ_PARAM_GLE,
    WT_ALZ_PARAM_3GPP,
    WT_ALZ_PARAM_WSUN,
};

enum WT_SLE_CTRLINFO_TYPE
{
    WT_SLE_CTRLINFO_TYPE_A1 = 0,
    WT_SLE_CTRLINFO_TYPE_A2 = 1,
    WT_SLE_CTRLINFO_TYPE_A3 = 2,
    WT_SLE_CTRLINFO_TYPE_A4 = 3,
    WT_SLE_CTRLINFO_TYPE_A5 = 4,
    WT_SLE_CTRLINFO_TYPE_A6 = 5,
    WT_SLE_CTRLINFO_TYPE_A7 = 6,
    WT_SLE_CTRLINFO_TYPE_B1 = 7,
    WT_SLE_CTRLINFO_TYPE_B2 = 8,
    WT_SLE_CTRLINFO_TYPE_B3 = 9,
    WT_SLE_CTRLINFO_TYPE_B4 = 10,
    WT_SLE_CTRLINFO_TYPE_B5 = 11,
};

//OFDM分析参数
struct AlzParamWifi
{
    int Demode = WT_DEMOD_UNKNOW;               //分析模式
    int AutoDetect = WT_BW_AUTO_DETECT;         //信号带宽自动检测，详见WT_BANDWIDTH_DETECT_ENUM

    int Method11b = WT_11B_STANDARD_TX_ACC;     //802.11 b EVM方式.  WT_11B_METHOD_ENUM
    int DCRemoval = WT_DC_REMOVAL_OFF;          //802.11 b 直流去除. WT_DC_REMOVAL_ENUM
    int EqTaps = WT_EQ_OFF;                     //802.11 b 均衡类型. WT_EQ_ENUM
    int PhsCorrMode11B = WT_PH_CORR_11b_ON;     //802.11 b 相位跟踪. WT_PH_CORR_11b_ENUM

    int PhsCorrMode = WT_PH_CORR_SYM_BY_SYM;    //802.11 a/b/g/n/ac 相位跟踪. WT_PH_CORR_ENUM
    int ChEstimate = WT_CH_EST_RAW;             //802.11 a/g/n/ac 通道估计. WT_CH_EST_ENUM
    int SynTimeCorr = WT_SYM_TIM_ON;            //802.11 a/g/n/ac 时序跟踪. WT_SYM_TIM_ENUM
    int FreqSyncMode = WT_FREQ_SYNC_AUTO;       //802.11 a/g/n/ac 频率同步. WT_FREQ_SYNC_ENUM
    int AmplTrack = WT_AMPL_TRACK_OFF;          //802.11 a/g/n/ac 幅度跟踪. WT_AMPL_TRACK_ENUM
    int OfdmDemodOn = 1;                        //OFDM Demodulation, 1:on, 0:off

    int MIMOAnalysisMode = 0;                   //MIMO分析模式
    int MimoMaxPowerDiff = 30;                  //MIMO不同signal之间的最大功率差异，差异大于该值较低功率的会被当成干扰信号
    int SpectrumMaskVersion = 0;                //SpecturmMask Version ，0：Ieee2009；1：Ieee2012,只在11n选择两种不同模板时使用~
    int ClockRate = 1;                          //压缩子载波间隔,赋值详见枚举,默认为1

    short NonHTDupBW = 0;                       //分析nonHT Duplicate打孔时需要指定带宽,可取值：0,20,40,80,160,320；默认值为0
    char FullCRCFlag = 0;                       //全译码开关，0：FCS CRC； 1：FEC CRC
    char IQCompensation = 0;                    //针对lp的优化选项，使能开关0：off；1：on；默认值0

    char PreambleAverage = 0;                   //针对lp的优化选项，使能开关0：off；1：on；默认值0
    char EqualizerSmoothing = 0;                //针对ks的优化选项，使能开关0：off；1：on；默认值0
    char LdpcDecodeIterationTimes = 3;          //LDPC译码迭代次数（范围1~10，默认3）
    char SfoCompensation = 0;                   //SFO优化

    char OBWCalcFlag = 1;                       //obw计算方式，0：子载波间隔乘子载波数量；1：功率占比99.5%，默认值为1
    char HardwareDecodeFlag = 0;                //是否开启硬件译码，0-软件译码，1-硬件译码，默认0
    char FrameType11AH = 0;                     //11ah的分析帧类型，0：S1G（默认值），1：S1G_DUP_1M,2:S1G_DUP_2M
    char ICISuppression = 0;

    char SpecialAnalyzeStream = 0;              //当MIMOAnalysisMode为1，single signal analysis时，指定分析指定流ID，范围：0~7
    char ReservedC[2];                          //对齐保留位
    char EnbaleEmbeddedBSSID = 0;               // 0/1
	int EmbeddedBSSID[4] = {0};                 // EmbeddedBSSID[*]: use 0 ~ 15bit，范围0：0xFFFF

    int Reserved[251];

    bool operator==(const AlzParamWifi &a){
    if( this->Demode != a.Demode||
        this->AutoDetect != a.AutoDetect||
        this->Method11b != a.Method11b||
        this->DCRemoval != a.DCRemoval||
        this->EqTaps != a.EqTaps||
        this->PhsCorrMode11B != a.PhsCorrMode11B||
        this->PhsCorrMode != a.PhsCorrMode||
        this->ChEstimate != a.ChEstimate||
        this->SynTimeCorr != a.SynTimeCorr||
        this->FreqSyncMode != a.FreqSyncMode||
        this->AmplTrack != a.AmplTrack||
        this->OfdmDemodOn != a.OfdmDemodOn||
        this->MIMOAnalysisMode != a.MIMOAnalysisMode||
        this->MimoMaxPowerDiff != a.MimoMaxPowerDiff||
        this->SpectrumMaskVersion != a.SpectrumMaskVersion||
        this->ClockRate != a.ClockRate||
        this->NonHTDupBW != a.NonHTDupBW||
        this->FullCRCFlag != a.FullCRCFlag||
        this->IQCompensation != a.IQCompensation||
        this->PreambleAverage != a.PreambleAverage||
        this->EqualizerSmoothing != a.EqualizerSmoothing||
        this->LdpcDecodeIterationTimes != a.LdpcDecodeIterationTimes||
        this->SfoCompensation != a.SfoCompensation||
        this->OBWCalcFlag != a.OBWCalcFlag||
        this->HardwareDecodeFlag != a.HardwareDecodeFlag||
        this->FrameType11AH != a.FrameType11AH||
        this->ICISuppression != a.ICISuppression ||
        this->EnbaleEmbeddedBSSID != a.EnbaleEmbeddedBSSID ||
        this->EmbeddedBSSID[0] != a.EmbeddedBSSID[0] ||
        this->EmbeddedBSSID[1] != a.EmbeddedBSSID[1] ||
        this->EmbeddedBSSID[2] != a.EmbeddedBSSID[2] ||
        this->EmbeddedBSSID[3] != a.EmbeddedBSSID[3] )
        {
            return false;
        }
        else
        {
            return true;
        }
    }
};

//BT分析参数
struct AlzParamBT
{
    int BTDataRate = WT_BT_DATARATE_Auto;   //Bluetooth速率, WT_BT_DATARATE
    int BTPktType = WT_BT_PACKETTYPE_NULL;  //Bluetooth包类型, WT_BT_PACKETTYPE(默认设置为WT_BT_PACKETTYPE_NULL即可)
    int BTBleEnhance = 0;                   //增强模式0:OFF Standard Rate; 1:ON Twice Standard Rate
    char BTBlePDUPktype = 0;                //0:Test; 1:Advertising
    char BTBleSyncMode = 0;                 //0:Preamble; 1:AccessAddress,当且仅当BTBlePDUPktype为1，Advertising时，该值有效
    unsigned char BTBleChannelIndex = 0;    //信道索引，当且仅当BTBlePDUPktype为1，Advertising时，该值有效
    char ACPViewRangeType = 2;              //ACP显示宽度，0：All Channel； 1：1M/2M/3M为±10M，BLE_1M/BLE_2M/BLE_125K/BLE_500K时为±5M； 2：±SamplingRate/2,正负采样率的一半(默认值)
    unsigned int BTBleAccessAddress = 0x8E89BED6;
    unsigned int ACPSweepTimes = 2;         //按SLE计算方式时，ACP扫描计算的次数，默认1，范围：1~10
    char ReservedC[3];                      //保留
    bool operator==(const AlzParamBT &a){
    if( this->BTDataRate != a.BTDataRate ||
        this->BTPktType != a.BTPktType ||
        this->BTBleEnhance != a.BTBleEnhance ||
        this->BTBlePDUPktype != a.BTBlePDUPktype ||
        this->BTBleSyncMode != a.BTBleSyncMode ||
        this->BTBleChannelIndex != a.BTBleChannelIndex ||
        this->BTBleAccessAddress != a.BTBleAccessAddress ||
        this->ACPViewRangeType != a.ACPViewRangeType ||
        this->ACPSweepTimes != a.ACPSweepTimes
        )
        {
            return false;
        }
        else
        {
            return true;
        }
    }
};

//ZigBee分析参数
struct AlzParamZigBee
{
    int Optimize = 0;  //是否开启优化，0 关闭， 1 开启
    bool operator==(const AlzParamZigBee &a){
    if( this->Optimize != a.Optimize)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
};

//通用分析参数
struct AlzParamComm
{
    int IQSwap = WT_IQ_SWAP_DISABLED;             //IQ交换(频谱反转). WT_IQ_SWAP_ENUM
    int IQReversion = WT_IQ_IQReversion_DISABLED; //IQ极性反转
    int ManualPktStart = 0;                       //指定数据包在多少个采样点后为起始
    int FilterPktByTime = 0;                      //短包过滤，低于此时间长度的包会被过滤
    int FilterPktByType = 0;                      //包类型过滤
    double FreqOffset = 0;                        //输入信号频率偏移
    int SpectrumFlag = 0;                         //取值：0:只取有效数据计算 ； 1：包括gap的全部数据计算
    int CcdfFlag = 0;                             //取值：0:power frame 作为参考值； 1：power all作为参考值
    int AvgTimes = 0;                             //算法平均次数(采集次数低于此则不平均)
    int HmatrixEnable = 0;                        //H矩阵使能开关，0：OFF； 1：ON
    int HmatRxAntennaNum = 0;                     //H矩阵 Rx 天线数
    int HmatTxAntennaNum = 0;                     //H矩阵 Tx 天线数
    Complex HMatValue[8][8] = {0};                //H矩阵数据，Complex，包含实部虚部
    int Reserved[5];                              //保留
    bool operator==(const AlzParamComm &a){
    if( this->IQSwap != a.IQSwap||
        this->IQReversion != a.IQReversion||
        this->ManualPktStart != a.ManualPktStart||
        this->FilterPktByTime != a.FilterPktByTime||
        this->FilterPktByType != a.FilterPktByType||
        Basefun::CompareDouble(this->FreqOffset, a.FreqOffset)||
        this->SpectrumFlag != a.SpectrumFlag||
        this->CcdfFlag != a.CcdfFlag||
        this->AvgTimes != a.AvgTimes ||
        this->HmatrixEnable != a.HmatrixEnable ||
        this->HmatRxAntennaNum != a.HmatRxAntennaNum ||
        this->HmatTxAntennaNum != a.HmatTxAntennaNum)
        {
            return false;
        }
        else
        {
            //加H matrix的判断，判断太多，加在这里
            if(this->HmatrixEnable == 1)
            {
                for(int i = 0; i < 8; i++)
                {
                    for(int j = 0; j < 8; j++)
                    {
                        if(this->HMatValue[i][j][0] != a.HMatValue[i][j][0] || this->HMatValue[i][j][1] != a.HMatValue[i][j][1])
                        {
                            return false;
                        }
                    }
                }
            }

            return true;
        }
    }
};

struct AlzParamFFT
{
    //FFT分析参数
    int WindowType = 4;             //WT_WINDOW_TYPE_Hann
    double Rbw = 100000;            //resolution bandwidth, 100K
    double Reserved[10];            //保留
    bool operator==(const AlzParamFFT &a){
    if( this->WindowType != a.WindowType||
        Basefun::CompareDouble(this->Rbw, a.Rbw))
        {
            return false;
        }
        else
        {
            return true;
        }
    }
};

//Zwave的分析参数
struct AlzParamZwave
{
    int ZwaveRate = 0;              //速率设置，取值1=R1，2=R2，3=R3
    int Reserved[32] = {0};
    bool operator==(const AlzParamZwave &a){
    if( this->ZwaveRate != a.ZwaveRate)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
};

struct AlzParamSparkLink
{
    int FrmType = 0;                                   /* Frame Type: 0-4 (auto, type1, type2, type3, type4) */
    int Bandwidth = 0;                                 /* Bandwidth: 0(auto); 1(1M); 2(2M); 3(4M) */
    int CtrlInfoType = 0;                              /* Control Information Type, reference Alg_GleCtrlInfoType */
    int PayloadCrcType = 1;                            /* Payload CRC Type: 1(CRC24); 2(CRC32) */
    unsigned int PayloadCrcSeed = 0x555555;                   /* Payload CRC Seed */
    int SlotIndex = 0;                                 /* Slot Index */
    int PilotDens = 4;                                 /* Pilot Density: 4, 8, 16 */
    int BoardIndex = 1;
    int PolarEncodePathNum = 1;
    int PID = 0;
    int Scramble = 1;
    int ChannelType = 1;
    int FreqRange = 0;
    int MSeqNo = 0;
    int SyncSource = 0;
    int SyncSeq[64] = {0};
    int Payload_Analyze_Mode = 0;                          //分析模式 取值：1 User Defined（默认值）；0 Standard
    int MCS = 0;                                           //调制编码方式索引 取值范围0-11,默认值为0
    int Raised_Root_Cosine_Filter = 0;                     //根升余弦滤波器 取值：0:OFF；1:ON（默认值）
    int Reserved[57];
    bool operator==(const AlzParamSparkLink &a)
    {
        if (this->FrmType != a.FrmType ||
            this->Bandwidth != a.Bandwidth ||
            this->CtrlInfoType != a.CtrlInfoType ||
            this->PayloadCrcType != a.PayloadCrcType ||
            this->PayloadCrcSeed != a.PayloadCrcSeed ||
            this->SlotIndex != a.SlotIndex ||
            this->PilotDens != a.PilotDens ||
            this->BoardIndex != a.BoardIndex ||
            this->PolarEncodePathNum != a.PolarEncodePathNum ||
            this->PID != a.PID ||
            this->Scramble != a.Scramble ||
            this->ChannelType != a.ChannelType ||
            this->FreqRange != a.FreqRange ||
            this->MSeqNo != a.MSeqNo ||
            this->SyncSource != a.SyncSource ||
            this->Payload_Analyze_Mode != a.Payload_Analyze_Mode ||
            this->MCS != a.MCS ||
            this->Raised_Root_Cosine_Filter != a.Raised_Root_Cosine_Filter)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
};


// 3GPP
struct AlzParam3GPP
{
    int analyzeGroup;
    int DcFreqCompensate;
    int Standard;
    int SpectrumRBW;
    int PkgAlzOffset;
    int MeasPowerGraph;
    int MeasSpectrum;
    int MeasCCDF;
    int ErrorCode;
    int rf_band[ALG_3GPP_MAX_STREAM];
    int rf_channel[ALG_3GPP_MAX_STREAM];
    int TriggerType; /* Free Run; External; Signal */
    double PreTime;
    int Reserved1[16];
    union {
        Alg_3GPP_AlzIn4g LTE;
        Alg_3GPP_AlzIn5g NR;
        Alg_3GPP_AlzInNBIOT NBIOT;
        Alg_3GPP_AlzInWCDMA WCDMA;
        Alg_3GPP_AlzInGSM GSM;
        Alg_4G_ListInType LTELIST;
        Alg_5G_ListInType NR5GLIST;

        char MaxReserved[1024 * 500]; // 500K
    };

    bool operator==(const AlzParam3GPP &a)
    {
        if (this->analyzeGroup != a.analyzeGroup ||
            this->DcFreqCompensate != a.DcFreqCompensate ||
            this->Standard != a.Standard ||
            this->SpectrumRBW != a.SpectrumRBW ||
            this->MeasPowerGraph != a.MeasPowerGraph ||
            this->MeasSpectrum != a.MeasSpectrum ||
            this->MeasCCDF != a.MeasCCDF ||
            this->rf_band[0] != a.rf_band[0] ||
            this->rf_band[1] != a.rf_band[1] ||
            this->rf_channel[0] != a.rf_channel[0] ||
            this->rf_channel[1] != a.rf_channel[1] ||
            this->TriggerType != a.TriggerType ||
            Basefun::CompareDouble(this->PreTime, a.PreTime))
        {
            return false;
        }
        else
        {
            switch(this->Standard)
            {
                case ALG_3GPP_STD_NB_IOT:
                    if(this->NBIOT.LinkDirect != a.NBIOT.LinkDirect)//等待补全
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                default:;                
            }
            return false;
        }
    }
};

struct AlzParamWiSun
{
    int Mr_OFDM_Option = 0;
    int Phy_OFDM_Interleaving = 0;
    int PhsCorrMode;
    int ChEstimate;
    int SynTimeCorr;
    int FreqSyncMode;
    int AmplTrack;
    int SfoCompensation;
    int ClockRate;
    int IQCompensation;
    int FreqBand;
    int Demode;
    int DataRate;
    int AcpCalMode;
    double  ModulationIndex;
    int ChannelSpacing;
    int Reserved[25];
};

//VSA分析参数
struct VsaAlzParam
{
    int Demode = WT_DEMOD_UNKNOW;
    AlzParamComm CommParam;
    AlzParamFFT  FFTParam;
    AlzParamWifi WifiParam;
    AlzParamBT   BTParam;
    AlzParamZigBee ZigBeeParam;
    AlzParamZwave ZwaveParam;
    AlzParamSparkLink AnalyzeParamSparkLink;
    AlzParam3GPP Alz3GPPParam;
    AlzParamWiSun AnalyzeParamWiSun;
};

//11ax Trigger Base分析参数
struct AlzParamAxTriggerBase
{
    int TBFlag = 0;                        //是否必须按TriggerBase模式来解
    int UserNum = 0;                       //总User数
    int UserID = 1;                        //当前分析User
    int GILTFSize = 0;                     //帧的GILTF:GILTFSize0, 1:GILTFSize1, 2:GILTFSize2
    int NumLTF = 1;                        //帧的取值1,2,4,6,8
    int LDPCSym = 0;                       //取值0,1
    int PEDisamb = 0;                      //取值0,1
    int AFactor = 1;                       //取值1,2,3,4
    int STBC =0;                           //STBC值
    int Doppler = 0;
    int Midamble_Periodicity = 0;

    int Stream[BE_RU_COUNT] = {0};      //取值1~8
    int MCS[BE_RU_COUNT] = {0};        //取值0~11
    int Segment[BE_RU_COUNT] = {0};    //取值0,1
    int RUIndex[BE_RU_COUNT] = {0};    //
    int Conding[BE_RU_COUNT] = {0};    //取值0:BCC、1:LDPC
    int DCM[BE_RU_COUNT] = {0};        //取值
    int AID[BE_RU_COUNT] = {0};
    int TBMUMIMOFlag = 0;                   //TB MU-MIMO时为1，否则为0
    int NSSStart[BE_RU_COUNT] = {0};         //取值1-8，常规TB都为1
    int Reserved[107]={0};
};

//11az analyze param
struct AlzParam11az
{
    /*Secure mode 0: OFF(默认值), 1: ON*/
    u32 SecureMode;
    /*Frequency domian transmitter window 0: OFF(默认值), 1: ON*/
    u32 TxWinFlg;
    /*User number*/
    u32 UserNum;
    Set11AzUser User[ALG_11AZ_MAX_USER_NUM];
    int Reserved[256];
};

//帧过滤，纯加载文件分析不做支持，帧过滤要实际环回
enum WT_COMPARE_TYPE
{
    GE,             //GREATER_THAN_OR_EQUAL大于等于 >=
    LE,             //LESS_THAN_OR_EQUAL小于等于 <=
    GT,             //GREATER_THAN大于 >
    LT,             //LESS_THAN小于<
    EQ,             //EQUAL等于 ==
    NE,             //NOT_EQUAL不等于!=
    MAX_COMPARE_TYPE
};

enum WT_FILTER_TYPE
{
    FILT_BY_PSDULENGTH,         //根据Psdu长度过滤
    FILT_BY_DATARATE,           //根据速率过滤
    FILT_BY_POWER,              //根据功率过滤（有帧功率时取帧功率，没有帧功率则取power all）
    MAX_FILTER_TYPE
};

struct ResultFilter
{
    int IsEnable = 0;                           //是否使能过滤功能
    int FilterType = FILT_BY_PSDULENGTH;        //过滤类型见枚举WT_FILTER_TYPE
    int CompareType = GE;                       //比较类型见枚举WT_COMPARE_TYPE
    int FiltPsduLength;                         //指定的Psdu长度
    int FiltMcs = -1;                           //指定的mcs模式
    double FiltDataRate;                        //指定的速率
    double FiltPower;                           //指定的Power值
    double Reserved[20] = {0};                  //扩展保留位
};

//附加扩展的分析参数
struct ExtralVsaAlzParam
{
    int Demode = WT_DEMOD_UNKNOW;
    int Type = -1;
    AlzParamAxTriggerBase AxTrigBaseParam;
    TriggerFrameSetting AxTrigFrameParam;
    AlzParam11az AzAlzParam;
};


enum WT_IQ_IMB_PARAM_TYPE
{
    WT_IQ_IMB_PARAM_80M,
    WT_IQ_IMB_PARAM_160M,
    WT_IQ_IMB_PARAM_320M,
};

//校准返回的IQ IMB数据
struct CalIQImbData
{
    int CurIQParamType = WT_IQ_IMB_PARAM_80M;
    Rx_Iq_Imb_Parm rx_iq_imb_parm;
    Rx_Iq_Imb_Parm rx_iq_imb_parm_160m;
    Rx_Iq_Imb_Parm rx_iq_imb_parm_320m;
    Rx_Iq_Image_Parm rx_iq_image_parm;
    Rx_Iq_Image_Parm rx_iq_image_parm_back;
};

struct ExtendEVMStu
{
    int IterativeEVM = 0;
    int SncEVM = 0;
    int CcEVM = 0;
};

enum IterativeEVM
{
    Iterative_EVM_OFF,
    Iterative_EVM_ON,
};

enum SncEVM
{
    SNC_EVM_OFF,
    SNC_EVM_ON,
};

enum CcEVM
{
    CC_EVM_OFF,
    CC_EVM_ON,
};

class Composite8080Result
{
public:
    Composite8080Result()
    {
        memset(m_Result, 0, sizeof(m_Result));
    }

    ~Composite8080Result();

    void Reset();

    Merge80And80Rslt m_Result[MAX_NUM_OF_CHNNEL];
};

class Analysis
{
public:
    Analysis();
    ~Analysis();

    //查找最合适的IQ补偿参数，测试用
    int FindBestIQParam(const std::string &File, int Demode, int Freq);

    //计算IQ不平衡参数
    int CalcIQImblance(int Segment, double &Ampl, double &Phase, double &TimeSkew);

    //设置全局固定IQ不平衡参数
    void SetStaticIQParam(int Segment, double Ampl, double Phase, double TimeSkew);

    //清除固定IQ不平衡参数
    void ClrStaticIQParam(int Segment);

    //*****************************************************************************
    // 设置CMIMO参考文件
    // 参数[IN]: File : 参考文件路径
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetCmimoRefFile(const std::string &File);

    //*****************************************************************************
    // 配置VSA分析参数
    // 参数[IN]: Type : 分析参数类型，见WT_ALZ_PARAM_TYPE
    //           Param  : 分析参数
    //             Len  : 参数长度
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SetAlzParam(int Type, void *Param, int Len);

    //*****************************************************************************
    // 判断下发的VSA分析参数是否改变了
    // 参数[IN]: Type : 分析参数类型，见WT_ALZ_PARAM_TYPE
    //           Param  : 分析参数
    //             Len  : 参数长度
    // 返回值: true/false
    //*****************************************************************************
    int IsAlzParamChanged(int Demode, void *Param, int Len);

    //*****************************************************************************
    // 获取分析参数
    // 参数[OUT]: Param : 分析参数
    //             Len  : 参数长度
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetAlzParam(const void **Param, int &Len);

    //*****************************************************************************
    // 加载信号文件数据
    // 参数[IN]: File : 文件路径
    // 返回值: 成功或错误码
    //*****************************************************************************
    int LoadSigFile(const std::string &File, const ExtendEVMStu &ExtendEvm);

    //*****************************************************************************
    // 获取vsg 多路信号的power值，和多路power中的最大值
    // 参数[IN]: FileName: vsg信号文件
    // 参数[OUT]: MaxPower: 多路功率中最大的功率
    //            ChPower: 对应每一路的功率，80+80的时候每一路有两个通道的数据
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetMimoFileMaxPower(const std::string &FileName, double &MaxPower, double *ChPower);

    //清除所有的分析数据，包括原始采集数据和结果数据。每次设置新的分析数据前需要调用此接口
    void Clear(void);

    //*****************************************************************************
    // 设置算法所要分析的数据, MIMO时需要按照Chain顺序设置
    // 参数[IN]: Data : 数据地址
    //         Segment: 80+80时选择是哪一个频段，取值为0，1
    //         Param  : VSA参数
    //        CalParam: 补偿参数
    // 返回值: 无
    //*****************************************************************************
    void SetData(const DataBufInfo &DataBuf, int Segment, const VsaParam &Param, const Rx_Parm &CalParam, const ExtendEVMStu &ExtendEvm, const std::vector<DataBufInfo> &MultiData = std::vector<DataBufInfo>(), int Spec500MFlag = 0, int Capture500cnt = 0, int Offset500M = 0);

    //*****************************************************************************
    // 分析帧功率
    // 参数[OUT]:   Exist : 是否存在帧
    //              Power : 帧功率，没有帧时表示power all
    //         PeakPower  : peak功率
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AlzPower(int Segment, bool &Exist, double &Power, double &PeakPower, double &MinPower, int Demode = WT_DEMOD_UNKNOW);

    //*****************************************************************************
    // 分析最小平均功率
    // 参数[OUT]:MinPower : 最小平均功率
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AlzMinAvgPower(int Segment, double &MinPower);

    //*****************************************************************************
    // 分析多帧数据并计算平均，调用此接口前需要先调用SetData来设置数据和参数。此接口会分析出所有的结果
    // 参数[IN]: FrameId : 从哪一帧开始分析
    //          FrameCnt : 分析多少帧数据
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AlzData(int FrameId, int FrameCnt);

    //*****************************************************************************
    // 分析单帧数据，调用此接口前需要先调用SetData来设置数据和参数。此接口会分析出所有的结果
    // 参数[IN]: FrameId : 分析第几帧的数据  Filter：是否进行帧过滤
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AlzFrameData(int FrameId, int IsFilter = false);

    //*****************************************************************************
    // 根据过滤条件，过滤指定的结果！调用此接口前需要先调用SetData来设置数据和参数。此接口会分析出所有的结果
    // 返回值: 成功或错误码
    //*****************************************************************************
    int FrameFilter();

    //*****************************************************************************
    // 如结果OBW大于阈值，则更新IMB数据并重新分析
    // 返回值: 成功或错误码
    //*****************************************************************************
    int UpdateImbAndReAnalyze();

    //*****************************************************************************
    // 或者实际使用的IQ IMB补偿数据
    // 参数[IN]: Segment:80+80时选择是哪一个频段，取值为0，1
    //           Chain :MIMO时第几个信号流。
    // 参数[OUT]: CalParam :校准数据对象
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetIQImbParam(int Segment, int Chain, Rx_Parm &CalParam);

    //*****************************************************************************
    // 分析单帧数据，并对结果做平均处理。调用此接口前需要先调用SetData来设置数据和参数。
    // 参数[IN]: FrameId : 分析第几帧的数据
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AlzAvgData(int FrameId);

    //*****************************************************************************
    // 剥离当前的数据结果
    // 参数[IN]: FrameId : 分析第几帧的数据
    // 返回值: 成功或错误码
    //*****************************************************************************
    int StripAvgData(int FrameId);

    //*****************************************************************************
    // 清除平均结果和计数，开始一次新的平均计算时需要先调用此接口
    //*****************************************************************************
    void ClearAvgData(void)
    {
        m_AvgCnt = 0;
        LastDataRate = UNVALID_DOUBLE_VAL;
        if (m_AlgAvg[0] != nullptr)
        {
            m_AlgAvg[0]->SetLastResultDemo(UNVALID_INT_VAL);
        }
        for (int j = 0; j < MAX_NUM_OF_CHNNEL; j++)
        {
            if (m_AlzParam.Demode == WT_DEMOD_BT)
            {
                BTMultiAlgResult[j].clear();
                AlgResult::Instance().m_BTRawBuf.clear();
                AlgResult::Instance().m_BTRawBufF2.clear();

            }
            else if (m_AlzParam.Demode == WT_DEMOD_GLE)
            {
                SLEMultiAlgResult[j].clear();
            }
            else
            {
                MultiAlgResult[j].clear();
            }
        }
        for(int i = 0; i < ALG_3GPP_MAX_STREAM; i++)
        {
            m_Alg3GPPMultiAlgResult[i].clear();
        }
    }

    //*****************************************************************************
    // 获取当前已平均的次数
    // 参数[IN]: 无
    // 参数[OUT] Cnt : 当前已平均的次数
    // 返回值: 无
    //*****************************************************************************
    void GetCurAvgCnt(int &Cnt) { Cnt = m_AvgCnt; };

    //*****************************************************************************
    // 获取VSA结果数据
    // 参数[IN]: Type : 需要获取的结果的标识码，见WT_defines.h
    //          Stream: 流ID，为0表示整体结果，否则获取指定流的结果
    //         Segment: 频段，80+80模式下选择哪个段的数据
    // 参数[OUT]: DataBuf : 数据指针
    //          DataSize : 数据大小
    //           DataType: 数据类型
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetVsaResult(const std::string &Type, int Stream, int Segment, void **DataBuf, int &DataSize, int &DataType);
    int GetVsaResult3GPP(const std::string &Type, int Stream, int Segment, void **DataBuf, int &DataSize, int &DataType);

    int Get11axUserVsaResult(int UserID, const std::string &Type, int Stream, int Segment, void **DataBuf, int &DataSize, int &DataType);

    //*****************************************************************************
    // 获取当前分析结果中的通用数据
    // 参数[IN]: Segment : 80+80时获取那个频段结果
    //           Stream : 指定流
    // 参数[OUT]: Result: 结果
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetCommResult(int Segment, int Stream, const VsaCommResult **Result);

    int GetSleCommResult(int Segment, int Stream, VsaSleCommResult2 **Result);

    int GetBTCommResult(int Segment, int Stream, VsaBTCommResult2 **Result);

    //*****************************************************************************
    // 获取平均结果
    // 参数[IN]: Segment : 80+80时获取那个频段结果
    //           Stream : 指定流
    // 参数[OUT]: AvgResult: 平均结果
    //           MaxResult: 最大结果
    //           MinResult: 最小结果
    // 返回值: 成功或错误码
    //*****************************************************************************
    int GetAvgResult(int Segment, int Stream, const VsaCommResult **AvgResult,
                     const VsaCommResult **MaxResult, const VsaCommResult **MinResult);

    int GetSleAvgResult(int Segment, int Stream, const VsaSleCommResult **AvgSleResult,
                     const VsaSleCommResult **MaxSleResult, const VsaSleCommResult **MinSleResult);

    int GetBTAvgResult(int Segment, int Stream, const VsaBTCommResult **AvgBTResult,
                     const VsaBTCommResult **MaxBTResult, const VsaBTCommResult **MinBTResult);
    int Get3GPPAvgResult(int Segment, int Stream, const Vsa3GPPCommResult **Avg3GPPResult,
                           const Vsa3GPPCommResult **Max3GPPResult, const Vsa3GPPCommResult **Min3GPPResult);
    int GetAvgResultComposite(int Segment, const VsaBaseResult **AvgResultComposite);
    //*****************************************************************************
    // 释放获取结果过程中申请的内存
    // 参数[IN]: 无
    // 返回值: 无
    //*****************************************************************************
    void FreeVsaResult(void);

    //*****************************************************************************
    // 将信号保存到文件中
    // 参数[IN]: DataType: 数据类型, 见WT_DATA_TYPE_E
    //            File : 文件名
    // 返回值: 成功或错误码
    //*****************************************************************************
    int SaveSignal(int DataType, const std::string &File);

    //获取当前分析的数据的segment数量
    int GetSegmentNum(void) { return m_RxInData[0].aStChIn[0].vht80_80MEnable ? 2 : 1; }

    //滑动平均时，是否清空数据标识
    bool GetSlipClrFlag(void){return ClrFlag;}

    //Beamforming 分析
    int AlzBeamformingCalChEstDUTTx(int Demode);
    int BeamformingCalChEstDUTRx(void *Data, int Len);
    int AlzBeamformingResult(void **RstBuf, int &Len);
    int AlzBeamformingVerification(double &RstDiffPower);
    int AlzBeamformingCalChProfile(int Demode, void *RstBuf, int &Len);
    int AlzBeamformingCalChAmplitudeAngleBCM(void *RstBuf, int &Len);

    //per 分析
    int AlzPer(int &FrameResult);

    //协议配置扩展附加的分析参数
    int SetExtralAlzParam(int Demode, int Type, void *Param, int Len);

    //配置Alg的AnalyseGroup
    int SetAlzGroup(std::vector<std::string> &AlzTypes);

    //*****************************************************************************
    // ResetAnalyzeGroup重置算法分析为全部内容的分析
    // 参数[IN]: 无
    // 返回值: 无
    //*****************************************************************************
    void ResetAnalyzeGroup();

    // 返回当期算法分析参数的Demode
    int GetAlzParamDemode()
    {
        return m_AlzParam.Demode;
    }

    int GetAlzParamNrAlzBand()
    {
        return m_AlzParam.Alz3GPPParam.NR.UL.Cell[0].ChannelBW;
    }

    //通过协议下发的配置Filter内容
    int SetResultFilterSetting(void *Param, int Len);

    //程序崩溃时保存给算法的配置和数据（崩溃时，会有保留，主要为崩溃时使用，这些文件会在重新开机（重新run）时被删除)
    // 返回值: 无
    void SaveStackDataFile(void);

    //*****************************************************************************
    //程序崩溃时保存给算法的采集数据
    // 参数[IN]: 将要保存的文件名
    // 返回值: 无
    //*****************************************************************************
    void SaveStackData(const char *FileName);
    void SaveStack3GppData(const char *FileName);

    //检查指定是否支持指定协议的license
    bool CheckDemodLic(int Demode);

    //mimo，分析时，检查从机是否有对应业务有效的license
    int CheckSlaveDemodLic(std::vector<LicItemInfo> &SlaveLicInfo);

    //配置算法分析时的analyzeMode，表明是否是第一次分析，还是已经多次分析,默认0表示做第一次分析，1表示当前数据已经分析过一次以上
    //目前实现只有采集的数据才会区分第一次还是多次分析；平均，agc，分析文件这些都永远只做第一次分析！
    void SetAnalyzeMode(int AnalyzeMode = 0);

    //设置IQ重新分析标志，//0表示未重新分析过IQ参数，1表示分析过或不需重新分析IQ参数
    void SetIQImbReset(int IQImbReset = 0);

    void CopySIFS(const std::vector<double> &TBTSIFS) { m_TBTSIFS = TBTSIFS; }
    int SetVsaFlatnessCal(int Enable);
    int SetVsaIQImbCal(int Enable);
    void SetVsaDomianIQForceEnable(int Enable) { m_ForceTimeDomainIq = Enable; }
    int GetVsaDomianIQForceEnable() { return m_ForceTimeDomainIq; }
    int GetVsaFlatnessCal() { return m_AlzFlatnessEnable;};
    int GetVsaIQImbCal() { return m_AlzIQImbEnable;};
    int SetDuplexNoiseFlag(int Flag);
    int GetDuplexNoiseFlag() { return m_AlzDuplexNoiseFlag;};

    int GetAnalyzeErrResult(void){ return m_AlzResult; };

    //3GPP_START/////////////////////////////////////////////////////////////////////////////////
    //设置分析数据
    void SetData3GPP(const DataBufInfo &DataBuf, const VsaParam &Param, const Rx_Parm &CalParam);
    void SetData3GPPList(const DataBufInfo &DataBuf, const VsaParam &Param, const Rx_Parm &CalParam);
    //5G宽频时设置原始数据
    void Set3gppNrWideBandData(const std::vector<DataBufInfo> &ReSampleData, const std::vector<DataBufInfo> &RawSampleData,
        const VsaParam &Param, const Rx_Parm &CalParam, int FreqOffset);
    //分析单帧数据
    int AlzFrameData3GPP(int FrameId);
    int AlzFrameData3GPPList();
    //设置算法分析参数
    void SetAlgVsaParam3GPP(Alg_3GPP_VsaInInfo *pInData, const AlzParam3GPP &Alz3GPPParam);
    void SetAlgVsaParam3GPPList(Alg_3GPP_ListInType *pInData, const AlzParam3GPP &Alz3GPPParam);
    //申请算法分析所需内存
    int AllocBuf3GPP();
    //结果平均
    //*****************************************************************************
    // 分析并计算平均，调用此接口前需要先调用SetData来设置数据和参数。此接口会分析出所有的结果
    // 参数[IN]: FrameId : 从哪一帧开始分析
    //          FrameCnt : 分析多少帧数据
    //          AvgMode : 平均模式
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AlzAvgData3GPP(int FrameId, int FrameCnt, int AvgMode);
    // 配置算法分析平均次数
    void Set3GPPAvgParam(int AvgNum)
    {
        m_StatisticCnt = AvgNum;
    }
    int Get3GPPAvgParam()
    {
        return m_StatisticCnt;
    }

    void SetListMod(bool Enable) { m_ListEnable = Enable;}
//3GPP_END/////////////////////////////////////////////////////////////////////////////////
private:
    //*****************************************************************************
    //程序崩溃时保存给算法的配置参数
    // 参数[IN]: 将要保存的文件名
    // 返回值: 无
    //*****************************************************************************
    void SaveStackAlzParam(char *FileName);

    //检查指定是否支持指定协议的license
    //bool CheckDemodLic(int Demode);

    //设置算法分析参数
    void SetAlgVsaParam(RX_InDat *pInData, const VsaAlzParam &AlzParam);

    //设置算法分析参数中的扩展的分析参数，配合设置算法分析参数时一起使用
    void SetAlgExtralParam(RX_InDat *pInData, ExtralVsaAlzParam &Param);

    void SetAlgMIMOAnalysisMode();

    //初始化算法输入结构体
    void InitInData(void);

    //检查算法计算出的demode
    void CheckAlgDemode(StOutInfo *pRxOut);

    //检查mac lic有效性,用于控制psdubit，psdu decode，psdu decompose，msdu info视图结果的显示
    int CheckMacLic(StOutInfo *pRxOut);

    //获取bt5.1结果项时，判断5.1lic是否存在，不存在返回无效结果
    //[in]:结果项字符串
    int CheckBT51Lic(const std::string ResultType);

    //判断数字模式的lic
    int CheckDigModeLic(StOutInfo *pRxOut);
    //*****************************************************************************
    // 申请算法分析所用的内存
    // 参数[IN]:Segment: 哪一段的算法
    //          Chain : 需要申请内存的chain
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AllocBuf(int Segment, int Chain);

    // 帧结果过滤，分析算法结果符合指定要求才返回正确
    // 参数[IN]:pRxOut: 算法分析返回的输出结果
    // 返回值: 成功或错误码
    int FiltFrameResult(int Stream, int Seg);

    bool IsTFFrame(StOutInfo *pRxOut);
    bool IsMuMimo(StOutInfo *pRxOut);

    void SetWifiFlatnessDataFitting(StOutInfo *pRxOut, const std::string &Type);

    //开启平均是，重新计算平均后的频谱数据和模板之间的margin值
    void CalculateAvgSpectMargin(int demod, int Stream, VsaCommResult *AvgResult);
    void ShowAlzParam();
    void Set3gppRfInfo(const DataBufInfo &DataBuf, const VsaParam &Param, const Rx_Parm &CalParam, int FreqOffset, Alg_3GPP_RFInInfo *pRFInInfo);

    //*****************************************************************************
    // 剥离当前的数据结果
    // 参数[IN]: FrameCnt:平均次数
    // 返回值: 成功或错误码
    //*****************************************************************************
    int StripAvgData3GPP(int FrameCnt);
    //*****************************************************************************
    // 平均的数据结果
    // 参数[IN]: FrameCnt:平均次数，AvgMode:平均类型
    // 返回值: 成功或错误码
    //*****************************************************************************
    int AlzAvgData3GPP(int FrameCnt, int AvgMode);

    bool CheckIfNeedGetResultFrom8080CombRst(const std::string &Type, StOutInfo *pRxOut);
    void ResetAlgMemory(void);
public:
    int m_Demod;
private:
    bool m_AlzAll = false;              //是否已分析过所有数据
    bool m_Comb8080To160 = false;       //是否将8080合并为160
    int  m_FrameId = 0;                 //当前分析的帧ID
    int m_AlzResult = WT_OK;

    TriggerFrameSetting m_TriggerFrame;              //给算法的tf分析配置
    VsaAlzParam m_AlzParam;             //算法分析参数

    ExtralVsaAlzParam m_ExtralAlzParam;    //附加扩展的分析参数
    int m_AnalyseGroup = WT_GROUP_ALL;              //分析组类型

    RxInfoMultIn m_RxInData[MAX_SEGMENT_CNT];         //算法VSA输入
    StOutInfo *m_RxOutData[MAX_SEGMENT_CNT];          //算法VSA输出


    Alg_3GPP_VsaInInfo m_Alg_3GPP_VsaInInfo; //算法VSA输入
    Alg_3GPP_VsaOutInfo m_Alg_3GPP_VsaOutInfo; //算法VSA输出
    Alg_3GPP_ListInType m_Alg_3GPP_List_VsaInInfo; //lismod算法vsa输入
    Alg_3GPP_ListOutType m_Alg_3GPP_List_VsaOutInfo; //listmod算法vsa输出
    int m_StatisticCnt = 0;
    std::deque<Vsa3GPPCommResult> m_Alg3GPPMultiAlgResult[ALG_3GPP_MAX_STREAM]; //算法多帧结果

    std::unique_ptr<Composite8080Result> m_8080Rslt;     //8080合并结果

    std::array<std::unique_ptr<void, FreePoolMem>, MAX_NUM_OF_CHNNEL> m_AlzBuf[MAX_SEGMENT_CNT];  //算法分析数据所用的buffer
    std::array<int, MAX_NUM_OF_CHNNEL>  m_BufLen[MAX_SEGMENT_CNT];     //算法buffer长度

    std::unique_ptr<AlgAvgResult> m_AlgAvg[3];  //平均结果信息
    VsaBaseResult AvgRstComposite;              //组合的平均结果
    int m_AvgCnt = 0;                           //已平均的次数

    std::string m_RefFile;              //CMIMO参考文件
    std::unique_ptr<ReadFile> m_RefData;//CMIMO参考文件数据内容

    std::unique_ptr<SigFile> m_SigFile;   //所分析的信号文件
    std::unique_ptr<stSpectrumOffset[]> m_SpectOffset;  //频谱补偿数据暂存

    std::unique_ptr<void, FreePoolMem> m_ResultBuf;  //保存结果用的buffer
    const int m_ResultBufLen = 74 << 20;  //结果buffer长度，74M暂定
    int m_BufPos = 0;                     //结果buffer已使用的长度
    bool m_ListEnable = false;  //listmod使能标志

    struct StaticIQParam
    {
        bool Valid = false;    //是否使用
        double IQAmpl;
        double IQPhase;
        double TimeSkew;
    };

    StaticIQParam m_StaticIQParam[MAX_SEGMENT_CNT];    //静态IQ补偿参数
    bool ClrFlag = false;

    WT_Beamforming m_BeamfomingAlzParam;   //Beamforming 分析配置参数

    std::vector<VsaCommResult> MultiAlgResult[MAX_NUM_OF_CHNNEL];       //多次平均模式下保存各路多次分析的结果，主要用于计算最大最小
    std::vector<VsaSleCommResult> SLEMultiAlgResult[MAX_NUM_OF_CHNNEL];
    std::vector<VsaBTCommResult> BTMultiAlgResult[MAX_NUM_OF_CHNNEL];
    double LastDataRate = UNVALID_DOUBLE_VAL;                           //保存上次结果的DataInfo信息，用于平均时判断是否清空平均数据

    int m_SLEFrameType = UNVALID_INT_VAL;     // SLE
    int m_SLEBandWidth = UNVALID_INT_VAL;     // SLE
    int m_SLEPayLoadLength = UNVALID_INT_VAL; // SLE
    int m_SLECtrlInfoType = UNVALID_INT_VAL;  // SLE
    int m_SLEMCS = UNVALID_INT_VAL;           // SLE
    char m_SLEPayLoadBin[256];          // SLE

    int m_BTPayLoadSize = UNVALID_INT_VAL;   //BT
    int m_BTPacketType = UNVALID_INT_VAL;    //BT
    int m_BTPattern = UNVALID_INT_VAL;       //BT
    int m_BTDataRate = UNVALID_INT_VAL;      //BT

    int m_3GPPAvgLinkDirect = UNVALID_INT_VAL;
    int m_3GPPAvgChanType = UNVALID_INT_VAL;

    ResultFilter m_ResultFilters;                                         //结果过滤器
    int m_AnalyzeMode = 0; //0表示做第一次分析，1表示数据已经分析过一次
    bool m_FlatnessFitting = false;

    int m_AlzFlatnessEnable = true;
    int m_AlzIQImbEnable = true;
    int m_ForceTimeDomainIq = false;
    int m_AlzDuplexNoiseFlag = false;   // 双工泄露泄露补偿开关
    int m_IsIQImbReset = 0; //0表示未重新分析过IQ参数，1表示分析过或不需重新分析IQ参数,WT_IQ_IMB_RESET_TYPE
    std::array<CalIQImbData, MAX_NUM_OF_CHNNEL>  m_CalIQImbData[MAX_SEGMENT_CNT];
    std::vector<double> m_TBTSIFS;     //TBT SIFS

    int m_HistoryMIMOAnalysisMode = 0;
    int m_RealChannelCount[2] = {0}; // 实际流数量，配合MIMOAnalysisMode为1 single sagnal模式时使用
    int m_AlzInDataCh0Bak = false;
    RX_InDat m_AlzInDataCh0[2]; // 配合MIMOAnalysisMode为1,保存第一流实际数据，方便切换分析
};

#define MIMO_MAX_DIFF_POWER 30.0    //mimo 文件多路间的最大差值 ,主要为了区分噪底，Todo:值大小待定
#endif
