//*****************************************************************************
//  File: license.cpp
//  license文件解析，提供特定license是否存在的查询接口,注意：License期限计算以0点~24点计算，小于1天仍显示1天，升级当天至凌晨0点的时间不计入计算中
//  Data: 2016.7.15
//*****************************************************************************

#include <iostream>
#include <fstream>
#include <sys/types.h>
#include <string.h>
#include <memory>
#include <dirent.h>
#include <cstring>
#include <math.h>
#include <algorithm>
#include <stdio.h>
#include <stdlib.h>
#include <semaphore.h>
#include <errno.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <thread>

#include "license.h"
#include "errorlib.h"
#include "wterror.h"
#include "secure.h"
#include "basefun.h"
#include "wtlog.h"
#include "conf.h"
#include "wtev++.h"
#include "devlib/rf.h"
#include "devlib/devlib.h"
#include "wtCryptlib.h"


using namespace std;
static const long  DAY_SECONDS = 24 * 60 * 60;          //一天的秒数
static const int LIC_SHORT_LENGTH = 480; //92;                 //license文件最短长度
static const int SRV_TIMER_INTERVAL = 10;


#define UNLIMITED 0   //表示license是永久有效的
#define LIMITED   1   //表示license是有期限的
#define LIC_PRINTF_ON 0
//方便内部测试使用接口，后续需删除

// 加密解密的字数(双字数?)
#define SECURE_COUNT(type) (sizeof(type) / 4)

int GenerateUpdateFileTest();
int InitTest();

const u32  License::m_LicPasswd[4] =
{
    0x59997510,
    0x4c96a250,
    0x090b6752,
    0x92541453
};

// base基础license
const std::vector<int> BaseLics448 = {WTL_VSG_UNIT_4, WTL_VSA_UNIT_4, WTL_LINK_8, WTL_RF_PORT_8, WTL_BAND_2_4G, WTL_GPRF, WTL_WIFI_SISO, WTL_11B, WTL_11G,
                                        WTL_11N, WTL_CMIMO, WTL_SWITCHED_MIMO, WTL_BAND_5G, WTL_11A, WTL_IBF, WTL_11AC, WTL_160M, WTL_11AX, WTL_BAND_6G, WTL_11BE};

// base基础license
const std::vector<int> BaseLics428 = {WTL_VSG_UNIT_1, WTL_VSA_UNIT_1, WTL_LINK_4, WTL_RF_PORT_4, WTL_BAND_2_4G, WTL_GPRF, WTL_WIFI_SISO, WTL_11B, WTL_11G,
                                        WTL_11N, WTL_CMIMO, WTL_SWITCHED_MIMO, WTL_IBF, WTL_BAND_5G, WTL_11A, WTL_11AC, WTL_160M, WTL_11AX, WTL_BAND_6G};
// base基础license
const std::vector<int> BaseLics418 = {WTL_VSG_UNIT_1, WTL_VSA_UNIT_1, WTL_GPRF, WTL_LINK_4, WTL_RF_PORT_4};

// lic依赖关系字典,考虑到所有lic都依赖base，判断依赖一开始就判断了base的，所以不再映射依赖
const std::map<int, std::vector<int>> LicRelationshipMap448 = {
    {WTL_WIFI_MAS_MIMO, {WTL_WIFI_MIMO}},
    {WTL_MUMIMO, {WTL_WIFI_MIMO}},
    {WTL_MAS_MUMIMO, {WTL_MUMIMO, WTL_WIFI_MAS_MIMO}},

    {WTL_MAC_11AX, {WTL_MAC_11AC}},
    {WTL_MAC_11BE, {WTL_MAC_11AX}},

    {WTL_MAC_INTER_AC, {WTL_MAC_11AC}},
    {WTL_MAC_INTER_AX, {WTL_MAC_11AX, WTL_MAC_INTER_AC}},
    {WTL_MAC_INTER_BE, {WTL_MAC_11BE, WTL_MAC_INTER_AX}},
    {WTL_MAC_ENCRYPTION, {WTL_MAC_11AC}},

    {WTL_DIQ_AX, {WTL_DIQ_AC}},
    {WTL_DIQ_BE, {WTL_DIQ_AX}},

    {WTL_BT5_1, {WTL_BT}},
    {WTL_CHANNEL_MATRIX, {WTL_WIFI_MIMO},},
    {WTL_DIQ_MULTI_CELL, {WTL_DIQ_AC},},
    {WTL_BT_HDR, {WTL_BT},},
};

// lic依赖关系字典,考虑到所有lic都依赖base，判断依赖一开始就判断了base的，所以不再映射依赖
const std::map<int, std::vector<int>> LicRelationshipMap428 = {
    {WTL_320M, {WTL_11BE}},
    {WTL_MULIT_SEG, {WTL_RF_PORT_8}},
    {WTL_WIFI_MIMO, {WTL_RF_PORT_8}},
    {WTL_MUMIMO, {WTL_WIFI_MIMO}},
    
    {WTL_BT5_1, {WTL_BT}},
    {WTL_CHANNEL_MATRIX, {WTL_WIFI_MIMO},},
    {WTL_DIQ_MULTI_CELL, {WTL_DIQ_AC},},
    {WTL_BT_HDR, {WTL_BT},},
};

// lic依赖关系字典,考虑到所有lic都依赖base，判断依赖一开始就判断了base的，所以不再映射依赖
const std::map<int, std::vector<int>> LicRelationshipMap418 = {
    {WTL_11A, {WTL_BAND_2_4G}},
    {WTL_11AC, {WTL_BAND_5G}},
    {WTL_BAND_5G, {WTL_BAND_2_4G}},
    {WTL_11AX, {WTL_11AC}},
    {WTL_WIFI6E, {WTL_11AX}},
    {WTL_11BE, {WTL_11AX}},
    {WTL_11P, {WTL_BAND_5G}},

    {WTL_BT5_1, {WTL_BT}},
    {WTL_BT_HDR, {WTL_BT},},
};

//c++11中局部静态变量多线程初始化是安全的
License &License::Instance(void)
{
    static License Object;
    return Object;
}

// license条目名称信息初始化
void License::LicItemNameInfoInit(void)
{
    // License名称
    // 与 WT_LIC_RESOURCE_E中定义的顺序一致
    m_ResourceLicNameVec.emplace(WT_RF_PORT_NUM, std::string("HW License RF Port Num"));
    m_ResourceLicNameVec.emplace(WT_ETH_PORT_NUM, std::string("HW License Eth Port Num"));
    m_ResourceLicNameVec.emplace(WT_LINK_NUM, std::string("HW License Link Num"));
    m_ResourceLicNameVec.emplace(WT_VSG_UNIT_NUM, std::string("HW License VSG Unit Num"));
    m_ResourceLicNameVec.emplace(WT_VSA_UNIT_NUM, std::string("HW License VSA Unit Num"));
    m_ResourceLicNameVec.emplace(WT_SPEC_RF_PORT9, std::string("HW License HPRF Port Num"));
    m_ResourceLicNameVec.emplace(WT_USE_MODULES_PARALLET, std::string("HW License Parallel Modules"));
    m_ResourceLicNameVec.emplace(WT_DIGITAL_ETH_NUM, std::string("HW License Digital IQ Eth Num"));

    // 与 WT_FREQ_BAND_E 中定义的顺序一致
    m_FreqLicNameVec.emplace(WT_BAND_1G, std::string("HW License 1G "));
    m_FreqLicNameVec.emplace(WT_BAND_2G, std::string("HW License 2G "));
    m_FreqLicNameVec.emplace(WT_BAND_2_4G, std::string("HW License 2.4G"));
    m_FreqLicNameVec.emplace(WT_BAND_3G, std::string("HW License 3G "));
    m_FreqLicNameVec.emplace(WT_BAND_4G, std::string("HW License 4G "));
    m_FreqLicNameVec.emplace(WT_BAND_5G, std::string("HW License 5G "));
    m_FreqLicNameVec.emplace(WT_BAND_6G, std::string("HW License 6G "));
    m_FreqLicNameVec.emplace(WT_BAND_8G, std::string("HW License 8G "));

    // 与 WT_PROT_E 中定义的顺序一致
    m_BusiLicNameVec.emplace(WT_GPRF, std::string("SW License GPRF"));

    m_BusiLicNameVec.emplace(WT_WIFI_SISO, std::string("SW License WIFI SISO"));
    m_BusiLicNameVec.emplace(WT_11A, std::string("SW License 11a"));
    m_BusiLicNameVec.emplace(WT_11B, std::string("SW License 11b"));
    m_BusiLicNameVec.emplace(WT_11G, std::string("SW License 11g"));
    m_BusiLicNameVec.emplace(WT_11N, std::string("SW License 11n"));
    m_BusiLicNameVec.emplace(WT_11P, std::string("SW License 11p"));
    m_BusiLicNameVec.emplace(WT_11AC, std::string("SW License 11ac"));
    m_BusiLicNameVec.emplace(WT_11AX, std::string("SW License 11ax"));
    m_BusiLicNameVec.emplace(WT_11BE, std::string("SW License 11be"));
    m_BusiLicNameVec.emplace(WT_11AH, std::string("SW License 11ah"));
    m_BusiLicNameVec.emplace(WT_11AD, std::string("SW License 11ad"));
    m_BusiLicNameVec.emplace(WT_160M, std::string("SW License 160M"));
    m_BusiLicNameVec.emplace(WT_320M, std::string("SW License 320M"));
    m_BusiLicNameVec.emplace(WT_MULIT_SEG, std::string("SW License Multi-Segment"));
    m_BusiLicNameVec.emplace(WT_CMIMO, std::string("SW License CMIMO"));
    m_BusiLicNameVec.emplace(WT_MAC_AC, std::string("SW License MAC 11ac"));
    m_BusiLicNameVec.emplace(WT_MAC_AX, std::string("SW License MAC 11ax"));
    m_BusiLicNameVec.emplace(WT_MAC_BE, std::string("SW License MAC 11be"));
    m_BusiLicNameVec.emplace(WT_MAC_BN, std::string("SW License MAC 11bn"));

    m_BusiLicNameVec.emplace(WT_WIFI_MIMO, std::string("SW License WIFI MIMO"));
    m_BusiLicNameVec.emplace(WT_MUMIMO, std::string("SW License MU-MIMO"));
    m_BusiLicNameVec.emplace(WT_WIFI_MAS_MIMO, std::string("SW License WIFI MAS MIMO"));
    m_BusiLicNameVec.emplace(WT_MAS_MUMIMO, std::string("SW License MAS MU-MIMO"));
    m_BusiLicNameVec.emplace(WT_SWITCHED_MIMO, std::string("SW License Switched MIMO"));

    m_BusiLicNameVec.emplace(WT_ZIGBEE, std::string("SW License ZigBee"));
    m_BusiLicNameVec.emplace(WT_BT, std::string("SW License BT"));

    m_BusiLicNameVec.emplace(WT_GPS, std::string("SW License GPS"));
    m_BusiLicNameVec.emplace(WT_BEIDOU, std::string("SW License BDS"));
    m_BusiLicNameVec.emplace(WT_GALILEO, std::string("SW License Galileo"));
    m_BusiLicNameVec.emplace(WT_GLONASS, std::string("SW License Glonass"));

    m_BusiLicNameVec.emplace(WT_CELL_2G, std::string("SW License Cellular 2G"));
    m_BusiLicNameVec.emplace(WT_CELL_3G, std::string("SW License Cellular 3G"));
    m_BusiLicNameVec.emplace(WT_CELL_LTE, std::string("SW License Cellular LTE"));
    m_BusiLicNameVec.emplace(WT_CELL_5G, std::string("SW License Cellular 5G"));

    m_BusiLicNameVec.emplace(WT_ZWAVE, std::string("SW License ZWave"));
    m_BusiLicNameVec.emplace(WT_DEVM, std::string("SW License DEVM"));

    m_BusiLicNameVec.emplace(WT_MAC_INTER_AC, std::string("SW License Interactive 11ac"));
    m_BusiLicNameVec.emplace(WT_MAC_INTER_AX, std::string("SW License Interactive 11ax"));
    m_BusiLicNameVec.emplace(WT_MAC_INTER_BE, std::string("SW License Interactive 11be"));
    m_BusiLicNameVec.emplace(WT_MAC_INTER_BN, std::string("SW License Interactive 11bn")); 
    m_BusiLicNameVec.emplace(WT_MAC_ENCRYPTION, std::string("SW License WIFI Encryption"));

    m_BusiLicNameVec.emplace(WT_IBF, std::string("SW License IBF"));
    m_BusiLicNameVec.emplace(WT_SEQUENCE, std::string("SW License Sequence"));
    m_BusiLicNameVec.emplace(WT_DIQ_AC, std::string("SW License Digital IQ 11ac"));
    m_BusiLicNameVec.emplace(WT_DIQ_AX, std::string("SW License Digital IQ 11ax"));
    m_BusiLicNameVec.emplace(WT_DIQ_BE, std::string("SW License Digital IQ 11be"));
    m_BusiLicNameVec.emplace(WT_DIQ_BN, std::string("SW License Digital IQ 11bn"));

    m_BusiLicNameVec.emplace(WT_SIGNALING, std::string("SW License Signaling"));
    m_BusiLicNameVec.emplace(WT_BEAMFORMING, std::string("SW License Beamforming"));
    m_BusiLicNameVec.emplace(WT_PER, std::string("SW License PER"));
    m_BusiLicNameVec.emplace(WT_INTER, std::string("SW License Interactive"));
    m_BusiLicNameVec.emplace(WT_SUB_INSTRUMENT, std::string("SW License Sub Instrument"));
    m_BusiLicNameVec.emplace(WT_AIQ, std::string("SW License Analog IQ"));
    m_BusiLicNameVec.emplace(WT_BT5_1, std::string("SW License BT5.1"));
    m_BusiLicNameVec.emplace(WT_SLE, std::string("SW License SLE"));
    m_BusiLicNameVec.emplace(WT_11BA, std::string("SW License 11ba"));
    m_BusiLicNameVec.emplace(WT_11AZ, std::string("SW License 11az"));
    m_BusiLicNameVec.emplace(WT_PAC, std::string("SW License PAC"));
    m_BusiLicNameVec.emplace(WT_HWO, std::string("SW License Baseband"));

    m_BusiLicNameVec.emplace(WT_WISUN, std::string("SW License WiSun"));
    m_BusiLicNameVec.emplace(WT_LORA, std::string("SW License LoRa"));
    m_BusiLicNameVec.emplace(WT_EVMC, std::string("SW License Evmc"));
    m_BusiLicNameVec.emplace(WT_CHANNEL_MODE, std::string("SW License Channel Mode"));
    m_BusiLicNameVec.emplace(WT_CHANNEL_MATRIX, std::string("SW License Channel Matrix"));
    m_BusiLicNameVec.emplace(WT_DIQ_MULTI_CELL, std::string("SW License Digiital Multi-cell"));
    m_BusiLicNameVec.emplace(WT_BT_HDR, std::string("SW License BT HDR"));
    m_BusiLicNameVec.emplace(WT_WAVE_DECRYPT, std::string("SW License Wave Decrypt"));
    m_BusiLicNameVec.emplace(WT_LTE_IoT, std::string("SW License LTE IoT"));
    m_BusiLicNameVec.emplace(WT_BLE_ADVERTISING, std::string("SW License BLE Advertising"));
    m_BusiLicNameVec.emplace(WT_BROADCAST, std::string("SW License Broadcast"));
    m_BusiLicNameVec.emplace(WT_WIFI6E, std::string("SW License Wifi6e"));
    m_BusiLicNameVec.emplace(WT_SUB_NET, std::string("SW License sub net"));
    m_BusiLicNameVec.emplace(WT_DECT, std::string("SW License DECT"));
    m_BusiLicNameVec.emplace(WT_HIGH_POWER, std::string("SW License High Power"));
    m_BusiLicNameVec.emplace(WT_MAC_ADVANCED, std::string("SW License MAC Adanced"));
    m_BusiLicNameVec.emplace(WT_DPD, std::string("SW License DPD"));
    m_BusiLicNameVec.emplace(WT_11BN, std::string("SW License 11bn"));
    m_BusiLicNameVec.emplace(WT_11BN_ADV, std::string("SW License 11 bn advanced"));
    m_BusiLicNameVec.emplace(WT_MLO_RF, std::string("SW License MLO RF"));
    m_BusiLicNameVec.emplace(WT_CLOCKRATE, std::string("SW License ClockRate"));
    m_BusiLicNameVec.emplace(WT_BT6, std::string("SW License BT 6"));
    m_BusiLicNameVec.emplace(WT_BT_HDT, std::string("SW License BT HDT"));
    m_BusiLicNameVec.emplace(WT_SLP, std::string("SW License SLP"));
    m_BusiLicNameVec.emplace(WT_UWB, std::string("SW License UWB"));
    m_BusiLicNameVec.emplace(WT_NR, std::string("SW License NR"));
    m_BusiLicNameVec.emplace(WT_GNSS_DB, std::string("SW License GNSS DB"));

    // License Technology名称 与 WT_TECHNOLOGY_E中定义的顺序一致
    m_TechnologyNameVec.emplace(LIC_GPRF, std::string("GPRF"));
    m_TechnologyNameVec.emplace(LIC_WIFI_SISO, std::string("WIFI SISO"));
    m_TechnologyNameVec.emplace(LIC_WIFI_MIMO, std::string("WIFI MIMO"));
    m_TechnologyNameVec.emplace(LIC_BLUETOOTH, std::string("Bluetooth"));
    m_TechnologyNameVec.emplace(LIC_INTER, std::string("Interactive"));
    m_TechnologyNameVec.emplace(LIC_DEVM, std::string("DEVM"));
    m_TechnologyNameVec.emplace(LIC_CELLULAR, std::string("Cellular"));
    m_TechnologyNameVec.emplace(LIC_OTHER, std::string("Other"));
    m_TechnologyNameVec.emplace(LIC_MODE, std::string("")); // MODE LIC
}

//构造函数完成License的读取和解析工作
License::License(void)
    : m_LicStatus(WT_OK)
{
    if (WTDeviceInfo::Instance().GetDevType() == MODEL_TYPE_WT448)
    {
        m_BaseLics = BaseLics448;
        m_LicRelationshipMap = LicRelationshipMap448;
    }
    else if(WTDeviceInfo::Instance().GetDevType() == MODEL_TYPE_WT428)
    {
        m_BaseLics = BaseLics428;
        m_LicRelationshipMap = LicRelationshipMap428;
    }
    else
    {
        m_BaseLics = BaseLics418;
        m_LicRelationshipMap = LicRelationshipMap418;
    }

    std::string WorkPath = WTConf::GetDir();

    LICFileName = WorkPath + std::string("/LIC");
    DEVTIMEFileName = WorkPath + std::string("/DEVTIME");
    LicItemNameInfoInit();

    sem_t *sem_test;
    sem_test = sem_open("testlic", O_CREAT, 0644, 1);
    sem_wait(sem_test);
    if (-1 == access(DEV_TIME_PATH, F_OK) && -1 == access(DEV_STATUS_PATH, F_OK))   //不存在DevTime
    {
        //当保存仪器时间的文件和保存仪器是否被篡改的文件都不存在时，创建这两个文件
        std::ofstream Fs;
        int Status = 0;
        Fs.open(DEV_STATUS_PATH, std::fstream::out | std::fstream::trunc);
        if (Fs.is_open())
        {
            Fs.write((char *)&Status,sizeof(int));
            Fs.close();

            time_t TimeTicks = 0;
            Fs.open(DEV_TIME_PATH, std::fstream::out | std::fstream::trunc);
            if (Fs.is_open())
            {
                //加密
                Secure::Encrypt((int *)&TimeTicks, SECURE_COUNT(time_t), m_LicPasswd);
                Fs.write((char *)&TimeTicks,sizeof(time_t));
                Fs.close();
            }
        }
    }
    sem_post(sem_test);
    memset(&m_LicInfo, 0 , sizeof(LicInfo));
    //检查当前系统时间与WT设备时间是否有效
    if (CheckDevTime() == WT_OK)
    {
        m_LicStatus = GetCurDevLicenses();
        if (m_LicStatus == WT_OK)
        {
            LicInfo TmpLicInfo;
            pthread_rwlock_rdlock(&m_rwlock);
            ThransLic(m_EquipDecrptLicVec, TmpLicInfo);
            pthread_rwlock_unlock(&m_rwlock);
            memcpy(&m_LicInfo, &TmpLicInfo, sizeof(m_LicInfo));
        }
    }

    // 内存中lic刷新监控间隔为1 day
    wtev::default_loop Loop;
    m_EvTime.set(Loop);
    m_EvTime.set<License, &License::ReFreshLicTimerCb>(this);
    m_EvTime.start(SRV_TIMER_INTERVAL, SRV_TIMER_INTERVAL);
}

int License::GetLicStatus(void)
{
    ErrorLib::Instance().CheckErrCode(m_LicStatus, m_LicStatus);
    return m_LicStatus;
}

int License::GetVSGUnitNum(int &VSGNum)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    VSGNum = m_HWResNum[WT_VSG_UNIT_NUM];
    return WT_OK;
}

int License::GetVSAUnitNum(int &VSANum)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    VSANum = m_HWResNum[WT_VSA_UNIT_NUM];
    return WT_OK;
}

int License::GetRFPortNum(int &Num)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    Num = m_HWResNum[WT_RF_PORT_NUM];
    return WT_OK;
}

int License::GetEthPortNum(int &Num)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    Num = m_HWResNum[WT_ETH_PORT_NUM];
    return WT_OK;
}

int License::GetLinkNum(int &Num)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    Num = m_HWResNum[WT_LINK_NUM];
    return WT_OK;
}

int License::GetResourceItemsRemainTime(std::map<std::string, int> &RemainTimeMap)
{
    for(auto &Item : m_ResItemExpTable)
    {
        RemainTimeMap.insert(std::make_pair(m_ResourceLicNameVec[Item.first], GetRemainDays(Item.second)));
    }
    return WT_OK;
}

int License::GetFreqItemsRemainTime(std::map<std::string, int> &RemainTimeMap)
{
    for(auto &Item : m_FreqItemExpTable)
    {
        RemainTimeMap.insert(std::make_pair(m_FreqLicNameVec[Item.first], GetRemainDays(Item.second)));
    }
    return WT_OK;
}

int License::GetBusiItemsRemainTime(std::map<std::string, int> &RemainTimeMap)
{
    for(auto &Item : m_BusiItemExpTable)
    {
        RemainTimeMap.insert(std::make_pair(m_BusiLicNameVec[Item.first], GetRemainDays(Item.second)));
    }
    return WT_OK;
}

int License::GetAllValidItemsRemainTime(std::map<std::string, int> &RemainTimeMap)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }
    GetResourceItemsRemainTime(RemainTimeMap);
    GetFreqItemsRemainTime(RemainTimeMap);
    GetBusiItemsRemainTime(RemainTimeMap);
    return WT_OK;
}

int License::GetResourceLicItemsInfo(std::vector<LicItemInfo> &LicItemsInfo)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "########## res lic item count = %d ##########\n", (int)m_ResItemExpTable.size());

    LicItemInfo ValidTimeTemp;
    for(auto &Item : m_ResItemExpTable)
    {
        TIME_TYPE STime;
        TIME_TYPE ETime;

        memset(&ValidTimeTemp, 0, sizeof(LicItemInfo));
        strncpy(ValidTimeTemp.LicTechName, m_TechnologyNameVec[m_LicInfo.HWRes[Item.first].LicenseItem.LicTech].c_str(), sizeof(ValidTimeTemp.LicTechName) - 1);
        // ValidTimeTemp.LicTechValue = m_LicInfo.HWRes[Item.first].LicenseItem.LicTech;
        strncpy(ValidTimeTemp.LicName, m_ResourceLicNameVec[Item.first].c_str(), sizeof(ValidTimeTemp.LicName) - 1);
        strncpy(ValidTimeTemp.Remarks, m_LicInfo.HWRes[Item.first].LicenseItem.Remarks, sizeof(ValidTimeTemp.Remarks) - 1);

        ValidTimeTemp.LicType = m_LicInfo.HWRes[Item.first].LicenseItem.LicType;
        ValidTimeTemp.LicValue = Item.first;
        ValidTimeTemp.ResourceNum = m_HWResNum[Item.first];

        if(Basefun::Seconds2TimeType(m_LicInfo.HWRes[Item.first].StartTimeTicks, &STime) == WT_OK)
        {
            ValidTimeTemp.StartTime = STime;
        }

        if(Basefun::Seconds2TimeType(Item.second, &ETime) == WT_OK)
        {
            ValidTimeTemp.EndTime = ETime;
        }

        LicItemsInfo.push_back(ValidTimeTemp);
    }

    return WT_OK;
}

int License::GetFreqLicItemsInfo(std::vector<LicItemInfo> &LicItemsInfo)
{
    LicItemInfo ValidTimeTemp;
    for(auto &Item : m_FreqItemExpTable)
    {
        TIME_TYPE STime;
        TIME_TYPE ETime;

        memset(&ValidTimeTemp, 0, sizeof(LicItemInfo));
        strncpy(ValidTimeTemp.LicTechName, m_TechnologyNameVec[m_LicInfo.Freq[Item.first].LicenseItem.LicTech].c_str(), sizeof(ValidTimeTemp.LicTechName) - 1);
        // ValidTimeTemp.LicTechValue = m_LicInfo.Freq[Item.first].LicenseItem.LicTech;
        strncpy(ValidTimeTemp.LicName, m_FreqLicNameVec[Item.first].c_str(), sizeof(ValidTimeTemp.LicName) - 1);
        strncpy(ValidTimeTemp.Remarks, m_LicInfo.Freq[Item.first].LicenseItem.Remarks, sizeof(ValidTimeTemp.Remarks) - 1);

        ValidTimeTemp.LicType = m_LicInfo.Freq[Item.first].LicenseItem.LicType;
        ValidTimeTemp.LicValue = Item.first;
        ValidTimeTemp.ResourceNum = 0;

        if(Basefun::Seconds2TimeType(m_LicInfo.Freq[Item.first].StartTimeTicks, &STime) == WT_OK)
        {
            ValidTimeTemp.StartTime = STime;
        }

        if(Basefun::Seconds2TimeType(Item.second, &ETime) == WT_OK)
        {
            ValidTimeTemp.EndTime = ETime;
        }

        LicItemsInfo.push_back(ValidTimeTemp);
    }

    return WT_OK;
}

int License::GetBusiLicItemsInfo(std::vector<LicItemInfo> &LicItemsInfo)
{
    LicItemInfo ValidTimeTemp;
    for(auto &Item : m_BusiItemExpTable)
    {
        TIME_TYPE STime;
        TIME_TYPE ETime;

        memset(&ValidTimeTemp, 0, sizeof(LicItemInfo));
        strncpy(ValidTimeTemp.LicTechName, m_TechnologyNameVec[m_LicInfo.Business[Item.first].LicenseItem.LicTech].c_str(), sizeof(ValidTimeTemp.LicTechName) - 1);
        // ValidTimeTemp.LicTechValue = m_LicInfo.Business[Item.first].LicenseItem.LicTech;
        strncpy(ValidTimeTemp.LicName, m_BusiLicNameVec[Item.first].c_str(), sizeof(ValidTimeTemp.LicName) - 1);
        strncpy(ValidTimeTemp.Remarks, m_LicInfo.Business[Item.first].LicenseItem.Remarks, sizeof(ValidTimeTemp.Remarks) - 1);

        ValidTimeTemp.LicType = m_LicInfo.Business[Item.first].LicenseItem.LicType;
        ValidTimeTemp.LicValue = Item.first;
        ValidTimeTemp.ResourceNum = 0;

        if(Basefun::Seconds2TimeType(m_LicInfo.Business[Item.first].StartTimeTicks, &STime) == WT_OK)
        {
            ValidTimeTemp.StartTime = STime;
        }

        if(Basefun::Seconds2TimeType(Item.second, &ETime) == WT_OK)
        {
            ValidTimeTemp.EndTime = ETime;
        }

        LicItemsInfo.push_back(ValidTimeTemp);
    }

    return WT_OK;
}

int License::GetAllLicItemsInfo(std::vector<LicItemInfo> &LicItemsInfo)
{
    WTLog::Instance().WriteLog(LOG_DEBUG, "####Get m_LicStatus = 0x%x######\n", m_LicStatus);
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    int Ret = WT_OK;
    pthread_rwlock_rdlock(&m_rwlock);
    if (!m_EquipDecrptLicVec.empty())
    {
        Ret = GetSrcLicenseInfos(m_EquipDecrptLicVec, LicItemsInfo);
    }
    pthread_rwlock_unlock(&m_rwlock);
    return Ret;
}

int License::CheckLicense(void)
{
    if (m_LicStatus != WT_OK)
    {
        WTLog::Instance().WriteLog(LOG_DEBUG, "m_LicStatus error\n");
        return m_LicStatus;
    }

    WTLog::Instance().WriteLog(LOG_DEBUG, "cur dev sn is [%s], lic file sn is [%s]\n", WTDeviceInfo::Instance().GetDeviceDetailedInfo().SN, m_LicFileSN.c_str());
    if(CheckLicSNCode(m_LicFileSN.c_str(), m_LicFileSN.c_str()) != WT_OK)
    {
        char ErrStr[100] = {0};
        sprintf(ErrStr, "SN does not match, cur dev sn:%s, lic sn:%s!", WTDeviceInfo::Instance().GetDeviceDetailedInfo().SN,  m_LicFileSN.c_str());
        WTLog::Instance().LOGERR(WT_LIC_FILE_ILLEGAL, ErrStr);
        return WT_LIC_FILE_ILLEGAL;
    }

    return WT_OK;
}

int License::CheckVSGUnit(int Id)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    return ((Id < 0) || (Id >= m_HWResNum[WT_VSG_UNIT_NUM])) ? WT_LIC_NOT_EXIST : WT_OK;
}

int License::CheckVSAUnit(int Id)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    return ((Id < 0) || (Id >= m_HWResNum[WT_VSA_UNIT_NUM])) ? WT_LIC_NOT_EXIST : WT_OK;
}

int License::CheckRFPort(int Port)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    //规避做法，1-4端口base都会有，不再检测,规避当5-8失效时，1-4也无法使用的问题
    if(Port >= WT_RF_1 && Port <= WT_RF_4)
    {
        return WT_OK;
    }
    return ((Port < 0) || (Port > m_HWResNum[WT_RF_PORT_NUM])) ? WT_LIC_NOT_EXIST : WT_OK;  // off:0 ,1-8对应实际的端口
}

int License::CheckEthPort(int Port)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    return ((Port < 0) || (Port >= m_HWResNum[WT_ETH_PORT_NUM])) ? WT_LIC_NOT_EXIST : WT_OK;
}

// 检查频段LicItem是否有效。
int License::CheckFreqBandLicItem(const std::vector<WT_FREQ_BAND_E> &FreqBandVertor)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    time_t NowTimeTicks;
    time(&NowTimeTicks);
    int LicExist = false;
    for (auto FreqBandId : FreqBandVertor)
    {
        auto iter = m_FreqItemExpTable.find(FreqBandId);
        if (FreqBandId == WT_MAX_BAND || iter == m_FreqItemExpTable.end())
        {
            continue;
        }
        else if (NowTimeTicks < iter->second)
        {
            return WT_OK;
        }
        else
        {
            LicExist = true;
        }
    }

    // FreqBandVertor不存在LIC或LIC过期
    return LicExist ? WT_LIC_TIME_EXCEED : WT_LIC_NOT_EXIST;
}

// 检查频率是否有效
int License::CheckFreq(double Freq)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

// 2.4G lic同时许可 SUB1G SUB2G
#define LIC_2_4G_DEBUG 0

    //当频率为两LIC区间的分界值时，任一侧频段的LIC合法，就认为频率可用。
    std::vector<WT_FREQ_BAND_E> FreqBandVertor;
    if (Basefun::CompareDoubleAccuracy1K(Freq, RF_1G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_1G_MAX) <= 0)
    {
        FreqBandVertor.push_back(WT_BAND_1G);
#if LIC_2_4G_DEBUG
        FreqBandVertor.push_back(WT_BAND_2_4G);
#endif
        //频率为1000M时，当WT_BAND_1G或WT_BAND_2G的LIC合法，都认为频率可用。
        if (Basefun::CompareDoubleAccuracy1K(Freq, RF_2G_MIN) == 0)
        {
            FreqBandVertor.push_back(WT_BAND_2G);
        }
    }
    else if (Basefun::CompareDoubleAccuracy1K(Freq, RF_2G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_2G_MAX) <= 0)
    {
        FreqBandVertor.push_back(WT_BAND_2G);
#if LIC_2_4G_DEBUG
        FreqBandVertor.push_back(WT_BAND_2_4G);
#endif
        if (Basefun::CompareDoubleAccuracy1K(Freq, RF_2_4G_MIN) == 0)
        {
            FreqBandVertor.push_back(WT_BAND_2_4G);
        }
    }
    else if (Basefun::CompareDoubleAccuracy1K(Freq, RF_2_4G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_2_4G_MAX) <= 0)
    {
        FreqBandVertor.push_back(WT_BAND_2_4G);
        if (Basefun::CompareDoubleAccuracy1K(Freq, RF_3G_MIN) == 0)
        {
            FreqBandVertor.push_back(WT_BAND_3G);
        }
    }
    else if (Basefun::CompareDoubleAccuracy1K(Freq, RF_3G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_3G_MAX) <= 0)
    {
        FreqBandVertor.push_back(WT_BAND_3G);
        if (Basefun::CompareDoubleAccuracy1K(Freq, RF_4G_MIN) == 0)
        {
            FreqBandVertor.push_back(WT_BAND_4G);
        }
    }
    else if (Basefun::CompareDoubleAccuracy1K(Freq, RF_4G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_4G_MAX) <= 0)
    {
        FreqBandVertor.push_back(WT_BAND_4G);
        if (Basefun::CompareDoubleAccuracy1K(Freq, RF_5G_MIN) == 0)
        {
            FreqBandVertor.push_back(WT_BAND_5G);
        }
    }
    else if (Basefun::CompareDoubleAccuracy1K(Freq, RF_5G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_5G_MAX) <= 0)
    {
        FreqBandVertor.push_back(WT_BAND_5G);
        if (Basefun::CompareDoubleAccuracy1K(Freq, RF_6G_MIN) == 0)
        {
            FreqBandVertor.push_back(WT_BAND_6G);
        }
    }
    else if (Basefun::CompareDoubleAccuracy1K(Freq, RF_6G_MIN) >= 0 && Basefun::CompareDoubleAccuracy1K(Freq, RF_6G_MAX) <= 0)
    {
        FreqBandVertor.push_back(WT_BAND_6G);
    }
    else
    {
        if (Freq == 0) // 支持频率为0, 80+80作校准用
        {
            return WT_OK;
        }
        else
        {
            return WT_LIC_NOT_EXIST;
        }
    }
    return CheckFreqBandLicItem(FreqBandVertor);
}

//检查业务协议LicItem是否有效
int License::CheckBusinessLicItem(WT_PROT_E LicItemId)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    if (WTDeviceInfo::Instance().GetDevType() == MODEL_TYPE_WT428)
    {
        if (LicItemId == WT_MAC_INTER_AC ||
            LicItemId == WT_MAC_INTER_AX ||
            LicItemId == WT_MAC_INTER_BE)
        {
            LicItemId = WT_INTER;
        }
    }

    auto iter = m_BusiItemExpTable.find(LicItemId);
    if (iter == m_BusiItemExpTable.end())
    {
        return WT_LIC_NOT_EXIST;
    }

    time_t NowTimeTicks;
    time(&NowTimeTicks);
    if (NowTimeTicks < iter->second)
    {
        return WT_OK;
    }
    else
    {
        return WT_LIC_TIME_EXCEED;
    }
}

//检查硬件资源LicItem是否有效
int License::CheckResourceLicItem(WT_LIC_RESOURCE_E Lictype)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }
    auto iter = m_ResItemExpTable.find(Lictype);
    if (iter == m_ResItemExpTable.end())
    {
        return WT_LIC_NOT_EXIST;
    }

    time_t NowTimeTicks;
    time(&NowTimeTicks);
    if (NowTimeTicks < iter->second)
    {
        return WT_OK;
    }
    else
    {
        return WT_LIC_TIME_EXCEED;
    }
}

int License::CheckSlaveBusinessLicItem(std::vector<int> &LicVec, std::vector<LicItemInfo> &SlaveLicInfo)
{
    int Ret = WT_OK;
    // WTLog::Instance().WriteLog(LOG_DEBUG, "sizeof slave lic = %d\n",(int)SlaveLicInfo.size());
    for (auto &DemoLic : LicVec)
    {
        if (WTDeviceInfo::Instance().GetDevType() == MODEL_TYPE_WT428)
        {
            if (DemoLic == WT_MAC_INTER_AC ||
                DemoLic == WT_MAC_INTER_AX ||
                DemoLic == WT_MAC_INTER_BE)
            {
                DemoLic = WT_INTER;
            }
        }

        int Find = false;
        for (auto &LicItem : SlaveLicInfo)
        {
            if (LicItem.LicType == WT_PROT_TYPE && LicItem.LicValue == DemoLic)
            {
                Find = true;
                //比较当前时间
                time_t NowTimeTicks;
                time(&NowTimeTicks);
                if (Basefun::TimeType2Seconds(&LicItem.EndTime) < NowTimeTicks)
                {
                    Ret = WT_LIC_TIME_EXCEED;
                    WTLog::Instance().LOGERR(WT_LIC_TIME_EXCEED, "Salve demo license out of date.");
                }
                break;
            }
        }
        if (!Find)
        {
            Ret = WT_LIC_NOT_EXIST;
            WTLog::Instance().LOGERR(WT_LIC_NOT_EXIST, "Slave demo license not exist.");
            break;
        }
    }
    return Ret;
}

// 获取license文件内容信息
int License::GetLicInfo(LicInfo &Lic)
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }

    Lic = m_LicInfo;
    return WT_OK;
}

// 生成license文件，并写回文件
int License::GenerateLicFile(LicInfo &Lic)
{
    LicInfo LicBufTemp;

    memset(&LicBufTemp, 0, sizeof(LicInfo));
    memcpy(&LicBufTemp, &Lic, sizeof(LicInfo));

    // 加密
    Secure::Encrypt((int *)&LicBufTemp, SECURE_COUNT(LicInfo), m_LicPasswd);
    if(Basefun::WriteFile(LICFileName.c_str(), (u8 *)&LicBufTemp, sizeof(LicInfo)) < 0)
    {
        WTLog::Instance().LOGERR(WT_LIC_GENERATE_FAILED, "GenerateLicenseFile failed!");
        return WT_LIC_GENERATE_FAILED;
    }

    return WT_OK;
}



// 解析license文件内容
int License::ParseLicContent(u8 *FileBuf)
{
    time_t EndTimeTick = 0;

    // 解密
    Secure::Decrypt((int *)FileBuf, SECURE_COUNT(LicInfo), m_LicPasswd);
    memcpy((u8 *)&m_LicInfo, FileBuf, sizeof(LicInfo));

    // 初始化SN码和特征码
    m_LicFileSN = std::string(m_LicInfo.SN);                    // license文件对应的SN码
    m_LicFileCODE = std::string(m_LicInfo.CODE);                // license文件对应的特征码

    // 初始化支持的硬件资源
    for(int i = 0; i < WT_MAX_RES; i++)
    {
        if(m_LicInfo.HWRes[i].Available == true)
        {
            // 获取到期的秒数Tick
            EndTimeTick = m_LicInfo.HWRes[i].StartTimeTicks + m_LicInfo.HWRes[i].LicenseItem.Days * DAY_SECONDS;
            GetLicEndTime(EndTimeTick);
            m_ResItemExpTable.insert(std::make_pair(m_LicInfo.HWRes[i].LicenseItem.LicValue, EndTimeTick));

            // 获取资源数
            m_HWResNum[i] = m_LicInfo.HWRes[i].LicenseItem.ResourceNum;
        }
    }

    // 初始化支持的频段
    for(int i = 0; i < WT_MAX_BAND; i++)
    {
        if(m_LicInfo.Freq[i].Available == true)
        {
            // 获取到期的秒数Tick
            EndTimeTick = m_LicInfo.Freq[i].StartTimeTicks + m_LicInfo.Freq[i].LicenseItem.Days * DAY_SECONDS;
            GetLicEndTime(EndTimeTick);
            m_FreqItemExpTable.insert(std::make_pair(m_LicInfo.Freq[i].LicenseItem.LicValue, EndTimeTick));
        }
    }

    // 初始化支持的业务协议
    for(int i = 0; i < WT_MAX_PROT; i++)
    {
        if(m_LicInfo.Business[i].Available == true)
        {
            // 获取到期的秒数Tick
            EndTimeTick = m_LicInfo.Business[i].StartTimeTicks + m_LicInfo.Business[i].LicenseItem.Days * DAY_SECONDS;
            GetLicEndTime(EndTimeTick);
            m_BusiItemExpTable.insert(std::make_pair(m_LicInfo.Business[i].LicenseItem.LicValue, EndTimeTick));
        }
    }

    return WT_OK;
}

// 结束时间要转为结束当天的23时59分59秒才结束
int License::GetLicEndTime(time_t &EndTimeTicks)
{
    time_t TmpTime;
    struct tm *TM;

    TmpTime = EndTimeTicks;
    TM = localtime(&TmpTime);
    if(TM == NULL)
    {
        return WT_ERROR;
    }

    TM->tm_sec = 59;
    TM->tm_min = 59;
    TM->tm_hour = 23;

    EndTimeTicks = mktime(TM);
    return WT_OK;
}

// 将license文件内容m_LicInfo中的表项清为0，并置为无效
int License::ClearLicInfoItem(WT_LIC_TYPE_E LicType, int LicValue)
{
    switch (LicType)
    {
    case WT_HW_TYPE:
        if (LicValue >= WT_MAX_RES)
        {
            return WT_LIC_VALUE_INVALID;
        }

        // license文件中的硬件可用资源数置为0，并置为无效
        m_LicInfo.HWRes[LicValue].LicenseItem.ResourceNum = 0;
        m_LicInfo.HWRes[LicValue].Available = false;
        break;

    case WT_FREQ_BAND_TYPE:
        if(LicValue >= WT_MAX_BAND)
        {
            return WT_LIC_VALUE_INVALID;
        }

        m_LicInfo.Freq[LicValue].Available = false;
        break;

    case WT_PROT_TYPE:
        if (LicValue >= WT_MAX_PROT)
        {
            return WT_LIC_VALUE_INVALID;
        }

        m_LicInfo.Business[LicValue].Available = false;
        break;

    default:
        return WT_LIC_TYPE_INVALID;
        break;
    }

    return WT_OK;
}

// 将当前硬件可用资源数清为0
int License::ClearAvailResourceNum(WT_LIC_RESOURCE_E LicValue)
{
    if(LicValue < WT_MAX_RES)
    {
        // 支持的硬件资源数量置为0
        m_HWResNum[LicValue] = 0;
    }
    else
    {
        WTLog::Instance().LOGERR(WT_LIC_VALUE_INVALID, "Lic value is invalid!");
        return WT_LIC_VALUE_INVALID;
    }

    return WT_OK;
}

// 注：此函数需创建独立线程进行实时维护并检查系统当前所有Lic的有效性, 若存在失效表项，需写回license文件
// 检查license时效，若过时将可用标志置为假,并返回WT_LIC_TIME_EXCEED
int License::CheckLicExpTime(bool WriteEnable)
{
    int ret = WT_OK;

    time_t NowTimeTicks;
    time(&NowTimeTicks);

    // 检查频段时效性
    for (auto Item = m_FreqItemExpTable.begin(); Item != m_FreqItemExpTable.end();)
    {
        if (NowTimeTicks > Item->second)
        {
            // 删除license文件内容中和m_FreqItemExpTable中对应的表项
            ClearLicInfoItem(WT_FREQ_BAND_TYPE, Item->first);
            Item = m_FreqItemExpTable.erase(Item);
            ret = WT_LIC_TIME_EXCEED;
        }
        else
        {
            ++Item;
        }
    }

    // 检查业务时效性
    for (auto Item = m_BusiItemExpTable.begin(); Item != m_BusiItemExpTable.end();)
    {
        if(NowTimeTicks > Item->second)
        {
            // 删除license文件内容中和m_BusiItemExpTable中对应的表项
            ClearLicInfoItem(WT_PROT_TYPE, Item->first);
            Item = m_BusiItemExpTable.erase(Item);
            ret = WT_LIC_TIME_EXCEED;
        }
        else
        {
            ++Item;
        }
    }

    // 检查硬件资源时效性
    for (auto Item = m_ResItemExpTable.begin(); Item != m_ResItemExpTable.end();)
    {
        if (NowTimeTicks > Item->second)
        {
            // 删除license文件内容中和m_BusiItemExpTable中对应的表项
            ClearLicInfoItem(WT_HW_TYPE, Item->first);
            Item = m_ResItemExpTable.erase(Item);
            // 当前可用资源数置为0
            ClearAvailResourceNum((WT_LIC_RESOURCE_E)Item->first);
            ret = WT_LIC_TIME_EXCEED;
        }
        else
        {
            ++Item;
        }
    }

    // LicInfo写回文件
    if(ret != WT_OK)
    {
        if(WriteEnable == true)
        {
            GenerateLicFile(m_LicInfo);
        }
    }

    return ret;
}

// 此函数需创建独立线程进行实时维护并检查当前系统时间是否被更改
// 检查当前系统时间与WT设备时间是否有效
int License::CheckDevTime(bool WriteEnable)
{
    sem_t *sem_test;
    sem_test = sem_open("testlic", O_CREAT, 0644, 1);
    sem_wait(sem_test);

    do
    {
        int Status = WT_OK;
        if (Basefun::ReadFile(DEV_STATUS_PATH, (u8 *)&Status, sizeof(int)) < 0)
        {
            WTLog::Instance().LOGERR(WT_DEV_FILE_ERROR, "Read DEV_Status File failed!");
            m_LicStatus = WT_DEV_FILE_ERROR;
            break;
        }

        if (Status != WT_OK)
        {
            m_LicStatus = Status;
            break;
        }

        if(-1 == access(DEV_TIME_PATH, F_OK))
        {
            WTLog::Instance().LOGERR(WT_DEV_FILE_ERROR, "DEV_TIME File not exit!");
            m_LicStatus = WT_DEV_FILE_ERROR;
            break;
        }

        int DevTimeFromFileFlag = 0;
        DevTime = 0;
        if (WriteEnable == true)
        {
            // 读取设备时间文件内容
            if(Basefun::ReadFile(DEV_TIME_PATH, (u8 *)&DevTime, sizeof(time_t)) < 0)
            {
                WTLog::Instance().LOGERR(WT_DEV_FILE_ERROR, "Read DEV_TIME File failed!");
                m_LicStatus = WT_DEV_FILE_ERROR;
                break;
            }
            DevTimeFromFileFlag = 1;
            // 解密
            Secure::Decrypt((int *)&DevTime, SECURE_COUNT(time_t), m_LicPasswd);
        }

        time_t NowTimeTicks = 0;
        time(&NowTimeTicks);

        // 比较系统时间与设备时间, 允许10min的误差
        //WTLog::Instance().WriteLog(LOG_DEBUG, "####check DevTime=%lf - NowTimeTicks=%lf####\n", (double)DevTime, (double)NowTimeTicks);
        if ((DevTime - NowTimeTicks) > 10*60) //10分误差
        {
            char ErrorStr[256] = {0};
            sprintf(ErrorStr, "DEV_TIME is invalid! Fileflag=%d,DevTime:%ld, nowTime:%ld.--%ld.",DevTimeFromFileFlag,(long int)DevTime, (long int)NowTimeTicks,(long int)(DevTime - NowTimeTicks));
            WTLog::Instance().LOGERR(WT_DEV_TIME_INVALID, ErrorStr);

            DevLib::Instance().SetErrorLed(LED_STATUS_ON);

            m_LicStatus = WT_DEV_TIME_INVALID;

            if(Basefun::WriteFile(DEV_STATUS_PATH, (u8 *)&m_LicStatus, sizeof(int)) < 0)
            {
                WTLog::Instance().LOGERR(WT_DEV_FILE_ERROR, "Write DEV_STATUS File failed!");
                m_LicStatus = WT_DEV_FILE_ERROR;
                break;
            }
        }
        else
        {
            DevTime = NowTimeTicks;

            if(WriteEnable == true)
            {
                // 加密
                Secure::Encrypt((int *)&NowTimeTicks, SECURE_COUNT(time_t), m_LicPasswd);

                // 将当前系统时间写回设备时间文件
                if(Basefun::WriteFile(DEV_TIME_PATH, (u8 *)&NowTimeTicks, sizeof(time_t)) < 0)
                {
                    WTLog::Instance().LOGERR(WT_DEV_FILE_ERROR, "Write DEV_TIME File failed!");
                    m_LicStatus = WT_DEV_FILE_ERROR;
                    break;
                }
            }
        }
    } while(0);

    sem_post(sem_test);
    return m_LicStatus;
}

// 检查license升级文件的SN码与特征码是否与设备的SN码与特征码匹配
int License::CheckLicSNCode(const char *SN, const char *Code)
{
#if LIC_PRINTF_ON
    WTLog::Instance().WriteLog(LOG_DEBUG, "check SN = %s\n",SN);
#endif
    return WTDeviceInfo::Instance().DeviceSnVerify(SN, Code);
}

int License::CheckRelyBusiLicItem(PCLicItem &LicItemTemp, time_t NowTimeTicks, WT_PROT_E ReliedLic, WT_TECHNOLOGY_E ReliedLicTech)
{
    if (m_LicInfo.Business[ReliedLic].Available == true)
    {
        // 若存在被依赖表项，获取剩余天数并比较大小，若依赖表项剩余天数小于新表项的剩余天数则更新
        auto Iter = m_BusiItemExpTable.find(ReliedLic);
        if(Iter == m_BusiItemExpTable.end())
        {
            return WT_LIC_NOT_EXIST;
        }

        int RemainDays = GetRemainDays(Iter->second);
        if (RemainDays < LicItemTemp.Days)
        {
            m_LicInfo.Business[ReliedLic].LicenseItem.Days = LicItemTemp.Days;
            m_LicInfo.Business[ReliedLic].StartTimeTicks = NowTimeTicks;    // 被依赖表项重新计时
        }
    }
    else
    {
        PCLicItem LicItemReliedTemp;
        memset(&LicItemReliedTemp, 0, sizeof(PCLicItem));

        // 若不存在,则插入被依赖的新表项
        m_LicInfo.Business[ReliedLic].Available = true;
        m_LicInfo.Business[ReliedLic].StartTimeTicks = NowTimeTicks;        // 开始计时

        LicItemReliedTemp.LicTech = ReliedLicTech;
        LicItemReliedTemp.LicType = WT_PROT_TYPE;
        LicItemReliedTemp.LicValue = ReliedLic;
        LicItemReliedTemp.ResourceNum = 0;
        LicItemReliedTemp.Days = LicItemTemp.Days;

        memcpy(&m_LicInfo.Business[ReliedLic].LicenseItem, &LicItemReliedTemp, sizeof(PCLicItem));
    }

    return WT_OK;
}

int License::ProcessRelyBusiLicItem(PCLicItem &LicItemTemp, time_t NowTimeTicks)
{
//    if(LicItemTemp.LicValue == WT_BLE)
//    {
//        CheckRelyBusiLicItem(LicItemTemp, NowTimeTicks, WT_BT, LIC_BLUETOOTH);
//    }

    if(LicItemTemp.LicValue == WT_11AC)
    {
        CheckRelyBusiLicItem(LicItemTemp, NowTimeTicks, WT_11A, LIC_WIFI_SISO);
        CheckRelyBusiLicItem(LicItemTemp, NowTimeTicks, WT_11B, LIC_WIFI_SISO);
        CheckRelyBusiLicItem(LicItemTemp, NowTimeTicks, WT_11G, LIC_WIFI_SISO);
        CheckRelyBusiLicItem(LicItemTemp, NowTimeTicks, WT_11N, LIC_WIFI_SISO);
    }

//    if(LicItemTemp.LicValue == WT_11AC_80_80)
//    {
//        CheckRelyBusiLicItem(LicItemTemp, NowTimeTicks, WT_11AC, LIC_WIFI_SISO);
//        ProcessRelyBusiLicItem(m_LicInfo.Business[WT_11AC].LicenseItem, NowTimeTicks);
//    }

    return WT_OK;
}

// 向m_LicInfo文件中插入LicItem表项
int License::InsertLicItem2LicInfo(PCLicItem &LicItemTemp)
{
    time_t NowTimeTicks;
    LicItem *pInsertLicItem;
    LicItemExpiryTable::iterator Iter;
    int RemainDays;

    time(&NowTimeTicks);

    switch(LicItemTemp.LicType)
    {
    case WT_HW_TYPE:
    {
        if(LicItemTemp.LicValue < WT_MAX_RES && LicItemTemp.LicValue >= 0)
        {
            pInsertLicItem = &m_LicInfo.HWRes[LicItemTemp.LicValue];
        }
        else
        {
            WTLog::Instance().LOGERR(WT_LIC_VALUE_INVALID, "Lic freq value is invalid!");
            return WT_LIC_VALUE_INVALID;
        }
        break;
    }
    case WT_FREQ_BAND_TYPE:
    {
        if(LicItemTemp.LicValue < WT_MAX_BAND && LicItemTemp.LicValue >= 0)
        {
            pInsertLicItem = &m_LicInfo.Freq[LicItemTemp.LicValue];
        }
        else
        {
            WTLog::Instance().LOGERR(WT_LIC_VALUE_INVALID, "Lic freq value is invalid!");
            return WT_LIC_VALUE_INVALID;
        }
        break;
    }
    case WT_PROT_TYPE:
    {
        if(LicItemTemp.LicValue < WT_MAX_PROT && LicItemTemp.LicValue >= 0)
        {
            pInsertLicItem = &m_LicInfo.Business[LicItemTemp.LicValue];

            //处理业务licensed的依赖关系
            ProcessRelyBusiLicItem(LicItemTemp, NowTimeTicks);
        }
        else
        {
            WTLog::Instance().LOGERR(WT_LIC_VALUE_INVALID, "Lic business value is invalid!");
            return WT_LIC_VALUE_INVALID;
        }
        break;
    }
    default:
    {
        WTLog::Instance().LOGERR(WT_LIC_TYPE_INVALID, "Lic type is invalid!");
        return WT_LIC_TYPE_INVALID;
    }
    }


    if (pInsertLicItem->Available == true)
    {
        // 若表项已存在
        if (LicItemTemp.LicType == WT_HW_TYPE)
        {
            Iter = m_ResItemExpTable.find(LicItemTemp.LicValue);
        }
        else if (LicItemTemp.LicType == WT_PROT_TYPE)
        {
            Iter = m_BusiItemExpTable.find(LicItemTemp.LicValue);
        }
        else
        {
            Iter = m_FreqItemExpTable.find(LicItemTemp.LicValue);
        }

        RemainDays = GetRemainDays(Iter->second);

        // 判断新插入的表项的剩余天数是否大于旧表项的剩余天数，若是则更新
        if (RemainDays <= LicItemTemp.Days)
        {
            pInsertLicItem->LicenseItem.Days = LicItemTemp.Days;
            pInsertLicItem->StartTimeTicks = NowTimeTicks;      //旧表项重新计时
            pInsertLicItem->LicenseItem.ResourceNum = LicItemTemp.ResourceNum;
        }
    }
    else
    {
        // 若不存在,则插入新表项
        memcpy(&pInsertLicItem->LicenseItem, &LicItemTemp, sizeof(PCLicItem));
        pInsertLicItem->Available = true;
        pInsertLicItem->StartTimeTicks = NowTimeTicks;      //开始计时
        pInsertLicItem->LicenseItem.ResourceNum = LicItemTemp.ResourceNum;
    }

    return WT_OK;
}

// 升级时直接替换设备原有的license文件，升级后需要重启设备才能生效
int License::UpdateLicFile(const u8 *LicFile, int Length)
{
    int Ret = WT_OK;

    if(Length < LIC_DATA_LENGTH_MINIMUM)
    {
        WTLog::Instance().LOGERR(WT_LIC_FILE_ILLEGAL, "License File Length too short!");
        Ret = WT_LIC_FILE_ILLEGAL;
    }
    else
    {
        char LineBuf[LINE_BUFF_LEN] = {0};
        //char *pBuf = (char *)malloc(Length);
        std::unique_ptr<char[]> pBuf;
        pBuf.reset(new(std::nothrow) char[Length]);

        if (pBuf == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc buffer failed when update lic~");
            return WT_ALLOC_FAILED;
        }
        else
        {
            char *pLicContent = (char *)pBuf.get();
            char *pLine = (char *)pBuf.get();
            char *TextBuf = NULL;

            int LicItemNum = 0;
            int LicBufLength = 0;
            int CRC = 0;
            int CheckSum = 0;

            TIME_TYPE LicGenerateTime;

            memset(&LicGenerateTime, 0, sizeof(TIME_TYPE));

            memcpy((char *)pBuf.get(), LicFile, Length);

            //忽略文件开头“//”或“##”后的信息描述
            if ((strncmp(pLine, "##", 2) == 0) || (strncmp(pLine, "//", 2) == 0))
            {
                while(Basefun::GetLine(LineBuf, LINE_BUFF_LEN, &pLine) == 0)
                {
                    if ((strncmp(LineBuf, "##", 2) == 0) || (strncmp(LineBuf, "//", 2) == 0))
                    {
                        pLicContent = pLine;
                        continue;
                    }
                    break;
                }
            }

            do
            {
                //验证license文件的数据完整性
                LicBufLength = *(int *)&pLicContent[0];
                CRC =  *(int *)&pLicContent[4];
                CheckSum =  *(int *)&pLicContent[8];
                TextBuf = &pLicContent[12];

                if(LicBufLength <= 80 || LicBufLength > LICENSE_BUF_SIZE)
                {
                    WTLog::Instance().LOGERR(WT_LIC_FILE_ILLEGAL, "License File LicBuf Length is invalid!");
                    return WT_LIC_FILE_ILLEGAL;
                }

                //CRC验证
                if(Secure::GetCRC16((u8 *)TextBuf, LicBufLength) != CRC)
                {
                    WTLog::Instance().LOGERR(WT_LICENSE_FILE_CRC_ERROR, "License File CRC is invalid!");
                    Ret = WT_LICENSE_FILE_CRC_ERROR;
                    break;
                }
                //校验和验证
                if(Secure::GetChecksum((u8 *)TextBuf, LicBufLength) != CheckSum)
                {
                    WTLog::Instance().LOGERR(WT_LICENSE_FILE_CHECKSUM_ERROR, "License File Checksum is invalid!");
                    Ret = WT_LICENSE_FILE_CHECKSUM_ERROR;
                    break;
                }

                //解密数据
                Secure::Decrypt((int *)TextBuf, LicBufLength / 4, m_LicPasswd);

                //获取Lic表项数量
                LicItemNum = *(int *)TextBuf;

                //判断SN码和特征码
                Ret = CheckLicSNCode(&TextBuf[4], &TextBuf[36]);
                //if(Ret == WT_SN_NOT_MATCH || Ret == WT_CODE_NOT_MATCH)
                if(Ret != WT_OK)
                {
                    WTLog::Instance().LOGERR(WT_LICENSE_UPGRADE_ERROR, "License SN or CODE not match!");
                    Ret = WT_LICENSE_UPGRADE_ERROR;
                    break;
                }

                strncpy((char *)&m_LicInfo.SN, &TextBuf[4], 32 - 1);
                strncpy((char *)&m_LicInfo.CODE, &TextBuf[36], 32 - 1);

                memcpy(&LicGenerateTime, (TIME_TYPE *)&TextBuf[68], sizeof(TIME_TYPE));
                time_t LicFileTimeTicks = Basefun::TimeType2Seconds(&LicGenerateTime);
                //判断是否为较新的license升级文件
                if(m_LicInfo.LicFileModifyTimeTicks > LicFileTimeTicks)
                {
                    WTLog::Instance().LOGERR(WT_LICENSE_IS_OLD, "Old License can not upgrade!");
                    Ret = WT_LICENSE_IS_OLD;
                    break;
                }
                else if(m_LicInfo.LicFileModifyTimeTicks == LicFileTimeTicks)
                {
                    WTLog::Instance().LOGERR(WT_LICENSE_REPEAT, "Update License is same with the one which Device use now!");
                    Ret = WT_LICENSE_REPEAT;
                    break;
                }
                //将设备license文件更新时间设为该license文件的生成时
                m_LicInfo.LicFileModifyTimeTicks = LicFileTimeTicks;

                PCLicItem LicItemTemp;

                //处理license表项,插入到LicInfo文件内容中
                for(int i = 0; i < LicItemNum; i++)
                {
                    memset(&LicItemTemp, 0, sizeof(PCLicItem));
                    memcpy(&LicItemTemp, &TextBuf[80 + i * sizeof(PCLicItem)], sizeof(PCLicItem));
                    if(LicItemTemp.Days > 0)    //当有效天数大于零时才允许升级
                    {
                        InsertLicItem2LicInfo(LicItemTemp);
                    }
                }

                //写回文件
                Ret = GenerateLicFile(m_LicInfo);

            }
            while(0);

            pLicContent = NULL;
            pLine = NULL;
            TextBuf = NULL;
        }
    }

    return Ret;
}

int License::GetRemainDays(time_t EndTimeTicks)
{
    time_t NowTimeTicks;
    time(&NowTimeTicks);

    time_t TimeTicks = EndTimeTicks - NowTimeTicks;
    int RemainDays = TimeTicks / DAY_SECONDS;

    // License期限计算以0点~24点计算，小于1天仍显示1天，升级当天至凌晨0点的时间不计入计算中
    if((RemainDays == 0) && (0 < TimeTicks) )
    {
        RemainDays += 1;
    }

    return RemainDays;
}

//new function
int License::ParseLicensePackage(const u8 *LicPackFileContent, int Length)
{
    int Ret = WT_OK;

    if(Length < LIC_SHORT_LENGTH)
    {
        WTLog::Instance().LOGERR(WT_LIC_FILE_ILLEGAL, "License File Length too short!");
        Ret = WT_LIC_FILE_ILLEGAL;
    }
    else
    {
        std::unique_ptr<char[]> pBuf;
        pBuf.reset(new(std::nothrow) char[Length]);

        if (pBuf == nullptr)
        {
            WTLog::Instance().LOGERR(WT_ALLOC_FAILED, "alloc buffer failed when update lic~");
            Ret = WT_ALLOC_FAILED;
        }
        else
        {
            int LicItemNum = 0;
            int LicBufLength = 0;
            int CRC = 0;
            int CheckSum = 0;

            memcpy((char *)pBuf.get(), LicPackFileContent, Length);
            char *pLicContent = (char *)pBuf.get();

            LicItemNum = *(int *)&pLicContent[12];

            if(LicItemNum > 0)//  去掉LicItemNum < 64，最大lic数量限制，后续lic还是会不断增加~
            {
                CRC =  *(int *)&pLicContent[0];
                CheckSum =  *(int *)&pLicContent[4];
                char *pLicContentData = NULL;
                pLicContentData = &pLicContent[480];//lic具体文件内容的开始位置

                //获取lic具体文件内容数据长度
                for(int i = 0; i < LicItemNum; i++)
                {
                    LicBufLength += *(int *)&pLicContent[16+4*i];
                }
#if LIC_PRINTF_ON
                WTLog::Instance().WriteLog(LOG_DEBUG, "crc = %d, CRC=%d, Len=%d\n",Secure::GetCRC16((u8 *)pLicContentData, LicBufLength), CRC, LicBufLength);
#endif
                if(Secure::GetCRC16((u8 *)pLicContentData, LicBufLength) != CRC)//CRC验证
                {

                    WTLog::Instance().LOGERR(WT_LICENSE_FILE_CRC_ERROR, "License File CRC is invalid!");
                    Ret = WT_LICENSE_FILE_CRC_ERROR;
                }
                else if(Secure::GetChecksum((u8 *)pLicContentData, LicBufLength) != CheckSum)//校验和验证
                {
                    WTLog::Instance().LOGERR(WT_LICENSE_FILE_CHECKSUM_ERROR, "License File Checksum is invalid!");
                    Ret = WT_LICENSE_FILE_CHECKSUM_ERROR;
                }
                else if( WTDeviceInfo::Instance().GetDevType() != *(int *)&pLicContent[8]) //仪器类型验证匹配
                {
                    char Tmp[128] = {0};
                    sprintf(Tmp, "License File device type is not match!DevType()=%d,license type=%d",WTDeviceInfo::Instance().GetDevType() , *(int *)&pLicContent[8]);
                    WTLog::Instance().LOGERR(WT_LICENSE_DEV_TYPE_ERROR, Tmp);
                    Ret = WT_LICENSE_DEV_TYPE_ERROR;
                }
                else//解析出具体的lic文件集，并做升级判断
                {
                    int Offset = 0;
                    char *pLicEncryptData = NULL;
                    pLicEncryptData = &pLicContent[480];
                    m_DetailLicFiles.clear();
                    m_LicDataInfoVec.clear();
                    for(int i = 0 ; i < LicItemNum; i++)
                    {
                        LicEncptDataInfo UpdateFile;
                        UpdateFile.LicDataLen = *(int *)&pLicContent[16+4*i];
                        memcpy(UpdateFile.LicData, pLicEncryptData+Offset,UpdateFile.LicDataLen);
                        Offset += UpdateFile.LicDataLen;
                        m_LicDataInfoVec.push_back(UpdateFile);
                        DevLicFileInfo DetailLic;
#if LIC_PRINTF_ON
    struct timeval tv;
    gettimeofday(&tv, NULL);
    int start = tv.tv_sec * 1000000 + tv.tv_usec;
#endif
                        Ret = DecryptSingleLicFile(UpdateFile.LicData,(char *)&DetailLic);
#if LIC_PRINTF_ON
    gettimeofday(&tv, NULL);
    int End = tv.tv_sec * 1000000 + tv.tv_usec;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "ParseLicensePackage-DecryptSingleLicFile use time us: " << (End - start) << endl;
#endif
                        if(WT_OK == Ret)
                        {
                            //判断是否是正确的lic类型
//                            if(DetailLic.LicType > WTL_MAX_LIC) //DetailLic.LicType < 0 ||
//                            {
//                                char Str[128] = {0};
//                                sprintf(Str, "licnese%d files License type error~", DetailLic.LicType);
//                                WTLog::Instance().LOGERR(WT_LIC_TYPE_INVALID, Str);
//                                Ret = WT_LIC_TYPE_INVALID;
//                                break;
//                            }
                            //解密成功判断LIC的sn是否与仪器的中的相对应
                            if((Ret = CheckLicSNCode((char*)DetailLic.SN,(char*)DetailLic.SN)) != WT_OK)
                            {
                                WTLog::Instance().LOGERR(WT_SN_NOT_MATCH, "Lic sn not match~");
                                Ret = WT_SN_NOT_MATCH;
                                m_DetailLicFiles.clear();
                                break;
                            }
                            //升级包中的lic结束时间与当前系统时间比较，判断是否lic过期;
                            if(DetailLic.LicLimitFlag == LIMITED) //lic是要有期限的
                            {
                                TIME_TYPE LicStartTime;
                                TIME_TYPE LicEndTime;
                                InitDateTime(LicStartTime, DetailLic.StartYear, DetailLic.StartMonth, DetailLic.StartDay, 0);
                                InitDateTime(LicEndTime, DetailLic.EndYear, DetailLic.EndMonth, DetailLic.EndDay, 1);

                                time_t Timep;
                                time(&Timep);
                                if(Basefun::TimeType2Seconds(&LicStartTime) >= Timep) //如果lic的开始时间比当前系统时间后，则不升级,TODO 换成仪器保密正确的时间，防止系统时间篡改
                                {
                                    WTLog::Instance().LOGERR(WT_LIC_TIME_EXCEED, "License start time is later then cur time~");
                                    Ret = WT_LIC_TIME_EXCEED;
                                    m_DetailLicFiles.clear();
                                    break;
                                }
                                if(Basefun::TimeType2Seconds(&LicEndTime) <= Timep)//如果lic结束时间比当前系统时间前，也不能升级
                                {
                                    WTLog::Instance().LOGERR(WT_LIC_TIME_EXCEED, "License is overdue");
                                    Ret = WT_LIC_TIME_EXCEED;
                                    m_DetailLicFiles.clear();
                                    break;
                                }
                            }
                            m_DetailLicFiles.push_back(DetailLic);
                        }
                        else
                        {
                            WTLog::Instance().LOGERR(WT_LICENSE_FILE_CRC_ERROR, "License File CRC2 is invalid!");
                            Ret = WT_LICENSE_FILE_CRC_ERROR;
                            m_DetailLicFiles.clear();
                            break;
                        }
                    }
#if LIC_PRINTF_ON
                    WTLog::Instance().WriteLog(LOG_DEBUG, "The package contains [%d] EncptLicFiles \n", (int)m_LicDataInfoVec.size());
#endif
                }
            }
            else
            {
                WTLog::Instance().LOGERR(WT_LICENSE_NUM_ERROR, "lic number error~");
                Ret = WT_LICENSE_NUM_ERROR;
            }
        }
    }
    return Ret;
}

void License::Hex2Str(u8 *Dest, u8 *Src, int Len)
{
    int Count = Len / 2;
    int i;

    for (i = 0; i<Count; i++)
    {
        if (*Src >= 'A')
            Dest[i] = (*Src - 'A' + 10) * 16;
        else
            Dest[i] = (*Src - '0') * 16;
        Src++;
        if (*Src >= 'A')
            Dest[i] += (*Src - 'A' + 10);
        else
            Dest[i] += (*Src - '0');
        Src++;
    }
}

int License::DecryptSingleLicFile(const char *FileBuf, char *LicBuf)
{
    int Ret = WT_OK;
    u8 RSAPlainHex[LINE_BUFF_LEN] = {0};//RSA解密后的16进制字符串
    u8 RSAPlainText[LINE_BUFF_LEN] = {0};//RSA解密后的16进制字符转二进制后的内容（AES加密内容+AES 密钥）
    u8 AESPlainText[LINE_BUFF_LEN] = {0};//进行AES二次解密之后的原文，即为64字节license的格式定义
    u8 AESEncrypted[LINE_BUFF_LEN] = {0};//RSA解密后，去掉AES key部分的内容
    u8 AESKey[AES_KEY_LENGTH] = {0};     //AES解密key
    u32 RSAPlainLen = sizeof(RSAPlainText);
    u32 AESPlainLen = sizeof(AESPlainText);

    //第一步，对lic密文进行RSA解密
    Ret = RsaDecrypt((char *)PublicKey.c_str(), (char *)FileBuf, RSAPlainHex, &RSAPlainLen);
    if(WT_OK != Ret)
    {
        WTLog::Instance().LOGERR(Ret, "RsaDecrypt fail~");
        return Ret;
    }
#if LIC_PRINTF_ON
    WTLog::Instance().WriteLog(LOG_DEBUG, "RSAPlainLen=%d, Text=%s\n", RSAPlainLen,RSAPlainHex);
#endif
    Hex2Str(RSAPlainText, RSAPlainHex, RSAPlainLen);    //RSA解密后为16进制字符串，需转换成二进制后再进行AES解密
#if LIC_PRINTF_ON
    WTLog::Instance().WriteLog(LOG_DEBUG, "after Hex2Str = %s\n",RSAPlainText);
#endif
    RSAPlainLen /= 2;

    char* pBuf = NULL;
    pBuf = (char*)&RSAPlainText[0];
    memcpy(AESKey, pBuf+(RSAPlainLen-AES_KEY_LENGTH), AES_KEY_LENGTH);
    memcpy(AESEncrypted, pBuf, RSAPlainLen - AES_KEY_LENGTH);

    Ret = AesDecrypt(AESKey, sizeof(AESKey), AESEncrypted, (RSAPlainLen-AES_KEY_LENGTH), AESPlainText, &AESPlainLen);
    if(WT_OK != Ret)
    {
        WTLog::Instance().LOGERR(Ret, " AesDecrypt fail~");
        return Ret;
    }
#if LIC_PRINTF_ON
    WTLog::Instance().WriteLog(LOG_DEBUG, "AESPlainLen=%d\n",AESPlainLen);
#endif
    memcpy(LicBuf, AESPlainText, LICENSE_STRING_SIZE);
    return Ret;
}

int License::GetPackageLicenseInfos(std::vector<LicItemInfo> &LicItemsInfo)
{
    return GetSrcLicenseInfos(m_DetailLicFiles, LicItemsInfo);
}

int License::GetSrcLicenseInfos(std::vector<DevLicFileInfo> LicFile, std::vector<LicItemInfo> &LicItemsInfo)
{
    int Ret = WT_OK;
    if(LicFile.empty())
    {
        Ret = WT_LICENSE_NUM_ERROR;
        WTLog::Instance().LOGERR(Ret, " Get package licnese infos empty~");
    }
    else
    {
        TIME_TYPE NowTime;
        TIME_TYPE EndTime;
        Basefun::GetSysTime(&NowTime);
        memcpy(&EndTime, &NowTime, sizeof(TIME_TYPE));
        EndTime.year = NowTime.year+100;
        EndTime.hour = 23;
        EndTime.min = 59;
        EndTime.sec = 59;
        for(int i = 0; i < LicFile.size(); i++)
        {
            int First4Bit = 0;
            int Mid6Bit = 0;
            int Last6Bit = 0;

            SplitUpdateLicTypetoDetail(LicFile[i].LicType, First4Bit, Mid6Bit, Last6Bit);

            LicItemInfo LicInfo;
            memset(&LicInfo, 0, sizeof(LicItemInfo));

            if(First4Bit == WT_HW_TYPE)
            {
                strncpy(LicInfo.LicTechName, m_TechnologyNameVec[LIC_MAX_TECH].c_str(), sizeof(LicInfo.LicTechName) - 1);
                strncpy(LicInfo.LicName, m_ResourceLicNameVec[Mid6Bit].c_str(), sizeof(LicInfo.LicName) - 1);
                LicInfo.LicType = WT_HW_TYPE;
                LicInfo.LicValue = Mid6Bit;
                LicInfo.ResourceNum = Last6Bit;
            }
            else if (First4Bit == WT_FREQ_BAND_TYPE)
            {
                strncpy(LicInfo.LicTechName, m_TechnologyNameVec[LIC_MAX_TECH].c_str(), sizeof(LicInfo.LicTechName) - 1);
                strncpy(LicInfo.LicName, m_FreqLicNameVec[Last6Bit].c_str(), sizeof(LicInfo.LicName) - 1);
                LicInfo.LicType = WT_FREQ_BAND_TYPE;
                LicInfo.LicValue = Last6Bit;
                LicInfo.ResourceNum = 0;
            }
            else
            {
                strncpy(LicInfo.LicTechName, m_TechnologyNameVec[Mid6Bit].c_str(), sizeof(LicInfo.LicTechName) - 1);
                strncpy(LicInfo.LicName, m_BusiLicNameVec[Last6Bit].c_str(), sizeof(LicInfo.LicName) - 1);
                LicInfo.LicType = WT_PROT_TYPE;
                LicInfo.LicValue = Last6Bit;
                LicInfo.ResourceNum = 0;
            }

            if(LicFile[i].LicLimitFlag == UNLIMITED)
            {
                memcpy(&LicInfo.StartTime, &NowTime, sizeof(TIME_TYPE));
                memcpy(&LicInfo.EndTime, &EndTime, sizeof(TIME_TYPE));
            }
            else
            {
                InitDateTime(LicInfo.StartTime, LicFile[i].StartYear, LicFile[i].StartMonth, LicFile[i].StartDay, 0);
                InitDateTime(LicInfo.EndTime, LicFile[i].EndYear, LicFile[i].EndMonth, LicFile[i].EndDay, 1);
            }
#if LIC_PRINTF_ON
            WTLog::Instance().WriteLog(LOG_DEBUG, "package lic lictype=%d, tech=%s, licvalue=%d,name=%s,resource=%d,start:%d-%d-%d %d:%d:%d,end:%d-%d-%d %d:%d:%d\n",\
                    LicInfo.LicType,LicInfo.LicTechName,LicInfo.LicValue,LicInfo.LicName,LicInfo.ResourceNum,
                    LicInfo.StartTime.year,LicInfo.StartTime.mon,LicInfo.StartTime.mday,LicInfo.StartTime.hour,LicInfo.StartTime.min,LicInfo.StartTime.sec,
                    LicInfo.EndTime.year,LicInfo.EndTime.mon,LicInfo.EndTime.mday,LicInfo.EndTime.hour,LicInfo.EndTime.min,LicInfo.EndTime.sec);
#endif
            bool AddFlag = true;
            if(First4Bit == WT_HW_TYPE)
            {
                for(auto &LicItem : LicItemsInfo)
                {
                    if(LicInfo.LicType == LicItem.LicType && LicInfo.LicValue == LicItem.LicValue)
                    {
                        AddFlag = false;
                        if(LicInfo.ResourceNum > LicItem.ResourceNum)
                        {
                            memcpy(&LicItem, &LicInfo, sizeof(LicItemInfo));//硬件资源lic信息，只修改数量
                        }
                    }
                }
            }
            if(AddFlag)
            {
                LicItemsInfo.push_back(LicInfo);
            }
        }
    }
    return Ret;
}

int License::UpdateLicensePackage(char *pErrorInfo, int &Len)
{
    int Ret = WT_OK;

    if(m_DetailLicFiles.size() != 0)
    {
        string ErrorInfo = "";
        if(WT_OK == (Ret = CheckLicRelationshipValid_V2(ErrorInfo)))
        {
            //拿升级包中的lic文件覆盖仪器中的lic，没有的就新建~
            TIME_TYPE NowTime;
            Basefun::GetSysTime(&NowTime);

            for(int i = 0; i< m_DetailLicFiles.size(); i++)
            {
                //最后判断，时间是否比仪器当前的有效时间短，短的话不覆盖，继续下一个文件升级
                int iRet = WT_OK;

                pthread_rwlock_rdlock(&m_rwlock);
                for (auto &Item : m_EquipDecrptLicVec)
                {
                    if (Item.LicType == m_DetailLicFiles[i].LicType)
                    {
                        if (Item.LicLimitFlag != UNLIMITED && m_DetailLicFiles[i].LicLimitFlag != UNLIMITED)
                        {
                            TIME_TYPE LicEndTime, DevLicEndTime;
                            InitDateTime(LicEndTime, m_DetailLicFiles[i].EndYear, m_DetailLicFiles[i].EndMonth, m_DetailLicFiles[i].EndDay, 1);
                            InitDateTime(DevLicEndTime, Item.EndYear, Item.EndMonth, Item.EndDay, 1);
                            if (Basefun::TimeType2Seconds(&LicEndTime) < Basefun::TimeType2Seconds(&DevLicEndTime))
                            {
                                iRet = WT_LICENSE_TOO_SHORT;
                                char TmpStr[128] = {0};
                                sprintf(TmpStr, "Update lic %d validity period is less than the one in device.\r\n", Item.LicType);
                                WTLog::Instance().LOGERR(iRet, TmpStr);
                                break;
                            }
                        }
                        else if (Item.LicLimitFlag == UNLIMITED && m_DetailLicFiles[i].LicLimitFlag != UNLIMITED)
                        {
                            iRet = WT_LICENSE_TOO_SHORT;
                            char TmpStr[128] = {0};
                            sprintf(TmpStr, "Update lic %d validity period is less than the one in device.\r\n", Item.LicType);
                            WTLog::Instance().LOGERR(iRet, TmpStr);
                            break;
                        }
                    }
                }
                pthread_rwlock_unlock(&m_rwlock);

                if(iRet != WT_OK)
                {
                    continue;   //继续下一个license的判断
                }

                char FileName[128] = "";
                sprintf(FileName,"/lic/license%d.lic",m_DetailLicFiles[i].LicType);
                string FinalFile = WTConf::GetDir() + FileName;
                ofstream OutFile(FinalFile, fstream::out | fstream::binary | fstream::trunc); //| ios::noreplace
                OutFile.write(m_LicDataInfoVec[i].LicData, m_LicDataInfoVec[i].LicDataLen );
                OutFile.close();
#if LIC_PRINTF_ON
                WTLog::Instance().WriteLog(LOG_DEBUG, "Update lic %s, update time = %d-%d-%d %d:%d:%d\n",FileName,NowTime.year,NowTime.mon,NowTime.mday,NowTime.hour,NowTime.min,NowTime.sec);
                if(m_DetailLicFiles[i].LicLimitFlag == LIMITED)
                {
                    WTLog::Instance().WriteLog(LOG_DEBUG, "limit start date=%d-%d-%d,end date=%d-%d-%d\n",m_DetailLicFiles[i].StartYear,m_DetailLicFiles[i].StartMonth,m_DetailLicFiles[i].StartDay,m_DetailLicFiles[i].EndYear,m_DetailLicFiles[i].EndMonth,m_DetailLicFiles[i].EndDay);
                }
#endif
            }
        }
        string LastSrc = "\r\n";
        if((ErrorInfo.length() >= LastSrc.length()) && LastSrc.compare(ErrorInfo.substr(ErrorInfo.length()-LastSrc.length(), LastSrc.length())) == 0 ) //去除错误信息最后的\r\n,配合tool
        {
            WTLog::Instance().WriteLog(LOG_DEBUG, "substr = %s ,compare rst =%d\n",ErrorInfo.substr(ErrorInfo.length()-LastSrc.length(), LastSrc.length()).c_str(), LastSrc.compare(ErrorInfo.substr(ErrorInfo.length()-LastSrc.length(), LastSrc.length())));
            Len = ErrorInfo.length()-LastSrc.length();
        }
        else
        {
            Len = ErrorInfo.length();
        }

        memcpy(pErrorInfo,(char *)ErrorInfo.c_str(), Len);
#if LIC_PRINTF_ON
        WTLog::Instance().WriteLog(LOG_DEBUG, "####Errorinfo = %s, Len = %d####\n",ErrorInfo.c_str(), Len);
#endif
    }
    else
    {
        WTLog::Instance().LOGERR(WT_LICENSE_NUM_ERROR, "lic number error~");
        Ret = WT_LICENSE_NUM_ERROR;
    }
    return Ret;
}

int License::CheckLicRelationshipValid_V2(std::string &ErrorInfo)
{
#if 0
    struct timeval tv;
    gettimeofday(&tv, NULL);
    int start = tv.tv_sec * 1000000 + tv.tv_usec;
#endif
    ErrorInfo.clear();

    char TmpStr[128] = {0};
    int Ret = WT_OK;

    //第一步先将仪器现有的lic和将要升级的lic整合成一个新的license序列，一长一短只取有效时间较长者，不重复的序列
    vector<DevLicFileInfo> CombineLicVec;
    pthread_rwlock_rdlock(&m_rwlock);
    CombineLicVec = m_EquipDecrptLicVec;
    pthread_rwlock_unlock(&m_rwlock);

    for(auto &LicItem : m_DetailLicFiles)
    {
        bool Exit = false;
        for(auto &ComLic : CombineLicVec)
        {
            if(ComLic.LicType == LicItem.LicType)
            {
                Exit = true;
                if(LicItem.LicLimitFlag == UNLIMITED || ComLic.LicLimitFlag == UNLIMITED)
                {
                    ComLic.LicLimitFlag = UNLIMITED;
                }
                else
                {
                    TIME_TYPE LicEndTime;
                    TIME_TYPE ComEndTime;
                    InitDateTime(LicEndTime, LicItem.EndYear, LicItem.EndMonth, LicItem.EndDay, 1);
                    InitDateTime(ComEndTime, ComLic.EndYear, ComLic.EndMonth, ComLic.EndDay, 1);
                    if(Basefun::TimeType2Seconds(&LicEndTime) > Basefun::TimeType2Seconds(&ComEndTime)) //解析lic升级包时，已经把开始时间大于当前系统时间，或者过期license给去掉了,如果只是检测仪器内容的license会怎样
                    {
                        ComLic.EndYear = LicItem.EndYear;
                        ComLic.EndMonth = LicItem.EndMonth;
                        ComLic.EndDay = LicItem.EndDay;
                    }
                }
            }
        }
        if(!Exit)
        {
            CombineLicVec.push_back(LicItem);
        }
    }

//    WTLog::Instance().WriteLog(LOG_DEBUG, "==========================update file===========================\n");
//    for(auto &LicItem : m_DetailLicFiles)
//    {
//        WTLog::Instance().WriteLog(LOG_DEBUG, "update type = %d,(%d-%d-%d)\n",LicItem.LicType, LicItem.EndYear, LicItem.EndMonth, LicItem.EndDay);
//    }
//    WTLog::Instance().WriteLog(LOG_DEBUG, "==========================device===========================\n");
//    for(auto &LicItem : m_EquipDecrptLicVec)
//    {
//        WTLog::Instance().WriteLog(LOG_DEBUG, "device type = %d,(%d-%d-%d)\n",LicItem.LicType, LicItem.EndYear, LicItem.EndMonth, LicItem.EndDay);
//    }
//
//    for(auto &ComLic : CombineLicVec)
//    {
//        WTLog::Instance().WriteLog(LOG_DEBUG, "ComLic type = %d,(%d-%d-%d)\n",ComLic.LicType, ComLic.EndYear, ComLic.EndMonth, ComLic.EndDay);
//    }

    //第二步判断假设可升级组合后comlic中base license是否齐全，bass lic必须齐全且有效时间必须一致
    int LimitFlag = -1;
    int EndYear = -1;
    int EndMonth = -1;
    int EndDay = -1;

    for(auto &Lic : m_BaseLics)     //base lic结合
    {
        bool Exit = false;

        for(auto &ComLic : CombineLicVec)
        {
            if(ComLic.LicType == Lic)   //赋值第一个base lic的时间
            {
                Exit = true;
                if(LimitFlag == -1)
                {
                    LimitFlag = ComLic.LicLimitFlag;
                    EndYear = ComLic.EndYear;
                    EndMonth = ComLic.EndMonth;
                    EndDay = ComLic.EndDay;
                }
                else
                {
                    //判断base lic的有效时间是否都是一致的
                    if(LimitFlag != ComLic.LicLimitFlag)
                    {
                        Ret = WT_LICENSE_NO_BASE_LIC;
                        ErrorInfo.append("Base license is invalid,notice:base lic must be the same valid period.\r\n");
                        WTLog::Instance().LOGERR(Ret, ErrorInfo);
                        return Ret;
                    }
                    else if(LimitFlag == ComLic.LicLimitFlag && LimitFlag == LIMITED)
                    {
                        if(EndYear != ComLic.EndYear || EndMonth != ComLic.EndMonth || EndDay != ComLic.EndDay)
                        {
                            Ret = WT_LICENSE_NO_BASE_LIC;
                            ErrorInfo.append("Base license is invalid,notice:base lic must be the same valid period.\r\n");
                            WTLog::Instance().LOGERR(Ret, ErrorInfo);
                            return Ret;
                        }
                    }
                }
            }
        }
        if(!Exit)   //base lic 不齐全
        {
            Ret = WT_LICENSE_NO_BASE_LIC;
            ErrorInfo.append("Base license is not complete.\r\n");
            WTLog::Instance().LOGERR(Ret, ErrorInfo);
            return Ret;
        }
    }

    //第三步：判断除base license外的license依赖关系是否合法
    for(auto &ComLic : CombineLicVec)
    {
        if (find(m_BaseLics.begin(), m_BaseLics.end(), ComLic.LicType) == m_BaseLics.end()) //不是base lic
        {
            //1、所有的license都依赖于base，所以所有的license都判断是否在base的有效期限范围内
            if(ComLic.LicLimitFlag == UNLIMITED && LimitFlag != UNLIMITED)
            {
                Ret = WT_LICENSE_RELAY_TIME_ERR;
                memset(TmpStr, 0, sizeof(TmpStr));
                sprintf(TmpStr,"%s's validity period must be less than or equal to base license's.\r\n",GetLicNameByUpdateLicType(ComLic.LicType).c_str());
                ErrorInfo.append(TmpStr);
            }
            else if(ComLic.LicLimitFlag == LIMITED && LimitFlag == LIMITED)
            {
                TIME_TYPE BaseEndTime;
                TIME_TYPE ComEndTime;
                InitDateTime(BaseEndTime, EndYear, EndMonth, EndDay, 1);
                InitDateTime(ComEndTime, ComLic.EndYear, ComLic.EndMonth, ComLic.EndDay, 1);
                if(Basefun::TimeType2Seconds(&ComEndTime) > Basefun::TimeType2Seconds(&BaseEndTime))
                {
                    Ret = WT_LICENSE_RELAY_TIME_ERR;
                    memset(TmpStr, 0, sizeof(TmpStr));
                    sprintf(TmpStr,"%s's validity period must be less than or equal to base license's.\r\n",GetLicNameByUpdateLicType(ComLic.LicType).c_str());
                    ErrorInfo.append(TmpStr);
                }
            }

            //2、按lic依赖字典在组合后的lic列中查找对应依赖项，并判断与依赖license项的关系是否合法
            {
                auto iter = m_LicRelationshipMap.find(ComLic.LicType);
                //WTLog::Instance().WriteLog(LOG_DEBUG, "releay size=%d,type=%d\n",(int)((iter->second).size()),ComLic.LicType);
                if (iter != m_LicRelationshipMap.end() && (iter->second).size() != 0) //从映射表中找到了要判断的lic，且依赖项不为空
                {
                    for(int i = 0 ; i < iter->second.size(); i++)
                    {
                        int RelayLicExit = false;
                        for(auto &RelayLic : CombineLicVec)
                        {
                            //WTLog::Instance().WriteLog(LOG_DEBUG, "second = %d, relaytype=%d\n",iter->second[i],RelayLic.LicType);
                            if(iter->second[i] == RelayLic.LicType)
                            {
                                RelayLicExit = true;
                                //如果依赖项期限是无限的，被依赖项期限是有限的则依赖不成立，eg：11ax依赖于11ac，11ax无限的，11ac有限
                                if(ComLic.LicLimitFlag == UNLIMITED && RelayLic.LicLimitFlag != UNLIMITED)
                                {
                                    Ret = WT_LICENSE_RELAY_TIME_ERR;
                                    memset(TmpStr, 0, sizeof(TmpStr));
                                    sprintf(TmpStr,"%s's validity period must be less than or equal to %s's.\r\n",
                                            GetLicNameByUpdateLicType(ComLic.LicType).c_str(), GetLicNameByUpdateLicType(RelayLic.LicType).c_str());
                                    ErrorInfo.append(TmpStr);
                                }
                                else if(ComLic.LicLimitFlag == LIMITED && RelayLic.LicLimitFlag == LIMITED) //都是有期限时，有效时长要小于被依赖项
                                {
                                    TIME_TYPE ComEndTime,RelayEndTime;
                                    InitDateTime(ComEndTime, ComLic.EndYear, ComLic.EndMonth, ComLic.EndDay, 1);
                                    InitDateTime(RelayEndTime, RelayLic.EndYear, RelayLic.EndMonth, RelayLic.EndDay, 1);
                                    if(Basefun::TimeType2Seconds(&ComEndTime) > Basefun::TimeType2Seconds(&RelayEndTime))
                                    {
                                        Ret = WT_LICENSE_RELAY_TIME_ERR;
                                        memset(TmpStr, 0, sizeof(TmpStr));
                                        sprintf(TmpStr,"%s's validity period must be less than or equal to %s's.\r\n",
                                                GetLicNameByUpdateLicType(ComLic.LicType).c_str(), GetLicNameByUpdateLicType(RelayLic.LicType).c_str());
                                        ErrorInfo.append(TmpStr);
                                    }
                                }
                            }
                        }
                        if(!RelayLicExit)   //没有找到依赖项
                        {
                            Ret = WT_LICENSE_RELATIONSHIP_ERROR;
                            sprintf(TmpStr,"%s depends on %s, please add %s first.\r\n",
                                    GetLicNameByUpdateLicType(ComLic.LicType).c_str(),
                                    GetLicNameByUpdateLicType(iter->second[i]).c_str(),
                                    GetLicNameByUpdateLicType(iter->second[i]).c_str());
                            ErrorInfo.append(TmpStr);
                        }
                    }
                }
            }
        }
    }
#if 0
    gettimeofday(&tv, NULL);
    int End = tv.tv_sec * 1000000 + tv.tv_usec;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "Check Relationship use time us: " << (End - start) << endl;
#endif
    return Ret;
}

std::string License::GetLicNameByUpdateLicType(int UpdateLicType)
{
    int First4Bit = 0;
    int Mid6Bit = 0;
    int Last6Bit = 0;
    string Str = "";

    SplitUpdateLicTypetoDetail(UpdateLicType, First4Bit, Mid6Bit, Last6Bit);

    if(First4Bit == WT_HW_TYPE)
    {
        if(WT_RF_PORT_NUM <= Mid6Bit && Mid6Bit < WT_MAX_RES)
        {
            Str = m_ResourceLicNameVec[Mid6Bit];
        }
    }
    else if(First4Bit == WT_FREQ_BAND_TYPE)
    {
        if(WT_BAND_1G <= Last6Bit && Last6Bit < WT_MAX_BAND)
        {
            Str = m_FreqLicNameVec[Last6Bit];
        }
    }
    else
    {
        if(WT_GPRF <= Last6Bit && Last6Bit < WT_MAX_PROT)
        {
            Str = m_BusiLicNameVec[Last6Bit];
        }
    }
    return Str;
}

int License::GetCurDevLicenses()
{
    if (m_LicStatus != WT_OK)
    {
        return m_LicStatus;
    }
    //遍历lic文件夹下的lic文件
    int Ret = WT_OK;
    std::vector<DevLicFileInfo> LicInfoTemp;
    do
    {
        DIR *Dir = NULL; // 路径句柄
        struct dirent *File = NULL;
        string FilePath = WTConf::GetDir() + "/lic/";
        Dir = opendir(FilePath.c_str());
        if (Dir == NULL)
        {
            Ret = WT_LIC_FILE_NOT_EXIST;
            WTLog::Instance().LOGERR(Ret, " lic path not exit~");
            break;
        }
        //遍历FilePath目录下的所有文件
        while ((File = readdir(Dir)) != NULL)
        {
            // 排除当前目录和上级目录
            if (!strncmp(File->d_name, ".", 1))
            {
                continue;
            }
            else if (strstr(File->d_name, ".lic") != NULL) //遍历.lic文件获取内容
            {
                string FileName = FilePath + File->d_name;
                LicEncptDataInfo EncptInfo;
                EncptInfo.LicDataLen = Basefun::ReadFile(FileName.c_str(), (u8 *)EncptInfo.LicData, sizeof(EncptInfo.LicData));

                if (0 >= EncptInfo.LicDataLen)
                {
                    continue;
                }
                //解密lic文件获取原始lic信息
                DevLicFileInfo LicInfo;
#if LIC_PRINTF_ON
                struct timeval tv;
                gettimeofday(&tv, NULL);
                int start = tv.tv_sec * 1000000 + tv.tv_usec;
#endif
                // Ret = DecryptSingleLicFile(EncptInfo.LicData, (char *)&LicInfo);
#if LIC_PRINTF_ON
                gettimeofday(&tv, NULL);
                int End = tv.tv_sec * 1000000 + tv.tv_usec;
                WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "GetCurDevLicenses-DecryptSingleLicFile use time us: " << (End - start) << endl;
#endif
                if (WT_OK == DecryptSingleLicFile(EncptInfo.LicData, (char *)&LicInfo))
                {
                    //判断是否是正确的lic类型
                    //                    if(LicInfo.LicType > WTL_MAX_LIC)//LicInfo.LicType < 0 ||
                    //                    {
                    //                        char Str[128] = {0};
                    //                        sprintf(Str, "licnese%d files License type error~", LicInfo.LicType);
                    //                        WTLog::Instance().LOGERR(WT_LIC_TYPE_INVALID, Str);
                    //                        Ret = WT_LIC_TYPE_INVALID;
                    //                    }
                    //解密成功判断LIC的sn是否与仪器的中的相对应
                    //                    //if(strcmp((char*)LicInfo.SN, WTDeviceInfo::Instance().GetDeviceDetailedInfo().SN) != 0)

                    // lic未定义则跳过
                    int First4Bit = 0;
                    int Mid6Bit = 0;
                    int Last6Bit = 0;
                    SplitUpdateLicTypetoDetail(LicInfo.LicType, First4Bit, Mid6Bit, Last6Bit);
                    if(First4Bit == WT_HW_TYPE)
                    {
                        if(m_ResourceLicNameVec.find(Mid6Bit) == m_ResourceLicNameVec.end())
                        {
                            continue;
                        }
                    }
                    else if (First4Bit == WT_FREQ_BAND_TYPE)
                    {
                        if(m_FreqLicNameVec.find(Last6Bit) == m_FreqLicNameVec.end())
                        {
                            continue;
                        }
                    }
                    else
                    {
                        if(m_TechnologyNameVec.find(Mid6Bit) == m_TechnologyNameVec.end() && m_BusiLicNameVec.find(Last6Bit) == m_BusiLicNameVec.end())
                        {
                            continue;
                        }
                    }

                    if (CheckLicSNCode((char *)LicInfo.SN, (char *)LicInfo.SN) != WT_OK)
                    {
                        char Str[128] = {0};
                        sprintf(Str, "CurDev SN:%s,lic SN:%s,Lic sn not match~", WTDeviceInfo::Instance().GetDeviceDetailedInfo().SN, LicInfo.SN);
                        WTLog::Instance().LOGERR(WT_SN_NOT_MATCH, Str);
                        Ret = WT_SN_NOT_MATCH;
                    }
                    else if (LicInfo.LicLimitFlag == LIMITED) //判断lic是否过期;lic是要有期限的
                    {
                        TIME_TYPE LicEndTime;
                        InitDateTime(LicEndTime, LicInfo.EndYear, LicInfo.EndMonth, LicInfo.EndDay, 1);
                        time_t Timep;
                        time(&Timep);

                        if (Basefun::TimeType2Seconds(&LicEndTime) <= Timep) // lic结束时间比系统时间小，过期
                        {
                            char TmpStr[128] = {0};
                            sprintf(TmpStr, "%s,License is overdue,valid(%d-%d-%d to %d-%d-%d).deleted!", File->d_name,
                                    LicInfo.StartYear, LicInfo.StartMonth, LicInfo.StartDay, LicInfo.EndYear, LicInfo.EndMonth, LicInfo.EndDay);
                            WTLog::Instance().LOGERR(WT_LIC_TIME_EXCEED, TmpStr);
                            remove(FileName.c_str());
                            Ret = WT_LIC_TIME_EXCEED;
                            // m_DetailLicFiles.clear();
                        }
                        else
                        {
                            LicInfoTemp.push_back(LicInfo);
                        }
                    }
                    else
                    {
                        LicInfoTemp.push_back(LicInfo);
                    }
                }
            }
        }
        closedir(Dir);
        if (LicInfoTemp.size() == 0)
        {
            WTLog::Instance().LOGERR(Ret, " Get Lic size is empty~");
            //遍历LIC时，只需排除错误的LIC文件，筛选并解析正确LIC文件。所以不返回错误
            return WT_OK;
        }
    } while (0);
    pthread_rwlock_wrlock(&m_rwlock);
    if (!LicInfoTemp.empty())
    {
        LicInfoTemp.swap(m_EquipDecrptLicVec);
    }
    gettimeofday(&m_CheckTime, NULL);
    pthread_rwlock_unlock(&m_rwlock);
    //遍历LIC时，只需排除错误的LIC文件，筛选并解析正确LIC文件。所以不返回错误
    return WT_OK;
}

//求两个日期相差的天数~
int DayDiff(int StartYear, int StartMonth, int StartDay, int EndYear, int EndMonth, int EndDay)
{
    int Y1, M1, D1;
    int Y2, M2, D2;

    M1 = (StartMonth + 9) % 12;
    Y1 = StartYear - M1/10;
    D1 = 365*Y1 + Y1/4 - Y1/100 + Y1/400 + (M1*306 + 5)/10 + (StartDay - 1);

    M2 = (EndMonth + 9) % 12;
    Y2 = EndYear - M2/10;
    D2 = 365*Y2 + Y2/4 - Y2/100 + Y2/400 + (M2*306 + 5)/10 + (EndDay - 1);

    return (D2 - D1);
}

int License::ThransLic(std::vector<DevLicFileInfo> LicInfos, LicInfo &Info)
{
    int Ret = WT_OK;
#if LIC_PRINTF_ON
    struct timeval tv;
    gettimeofday(&tv, NULL);
    int start = tv.tv_sec * 1000000 + tv.tv_usec;
#endif
    if(LicInfos.size() > 0)
    {
        memset(&Info, 0 , sizeof(Info));
        strcpy(Info.SN, (char *)(LicInfos[0].SN));
        strcpy(Info.CODE, (char *)(LicInfos[0].SN));

        m_LicFileSN = std::string((char *)(LicInfos[0].SN));
        m_LicFileCODE = std::string((char *)(LicInfos[0].SN));
        time(&Info.LicFileModifyTimeTicks);
        m_ResItemExpTable.clear();
        m_FreqItemExpTable.clear();
        m_BusiItemExpTable.clear();
        memset(m_HWResNum, 0, sizeof(m_HWResNum));

        TIME_TYPE NowTime;
        Basefun::GetSysTime(&NowTime);
        for(int i = 0; i < LicInfos.size(); i++)
        {
            int First4Bit = 0;
            int Mid6Bit = 0;
            int Last6Bit = 0;

            SplitUpdateLicTypetoDetail(LicInfos[i].LicType, First4Bit, Mid6Bit, Last6Bit);
            int Days = 0;
            TIME_TYPE StartTime, EndTime;
            time_t StartTimeTicks, EndTimeTicks;

            if(LicInfos[i].LicLimitFlag == UNLIMITED)
            {
                memcpy(&StartTime, &NowTime, sizeof(TIME_TYPE));
                InitDateTime(EndTime, StartTime.year+100, StartTime.mon, StartTime.mday, 1);
                Days = PERMANENT_VALIDITY_LIC;
            }
            else
            {
                InitDateTime(StartTime, LicInfos[i].StartYear, LicInfos[i].StartMonth, LicInfos[i].StartDay, 0);
                InitDateTime(EndTime, LicInfos[i].EndYear, LicInfos[i].EndMonth, LicInfos[i].EndDay, 1);
                Days = DayDiff(LicInfos[i].StartYear, LicInfos[i].StartMonth, LicInfos[i].StartDay, LicInfos[i].EndYear, LicInfos[i].EndMonth, LicInfos[i].EndDay);
            }
            StartTimeTicks = Basefun::TimeType2Seconds(&StartTime);
            EndTimeTicks = Basefun::TimeType2Seconds(&EndTime);

            if(First4Bit == WT_HW_TYPE)
            {
                Info.HWRes[Mid6Bit].Available = true;
                Info.HWRes[Mid6Bit].StartTimeTicks = StartTimeTicks;
                Info.HWRes[Mid6Bit].LicenseItem.LicTech = LIC_MAX_TECH;
                Info.HWRes[Mid6Bit].LicenseItem.LicType = WT_HW_TYPE;
                Info.HWRes[Mid6Bit].LicenseItem.LicValue = Mid6Bit;
                if(Last6Bit > Info.HWRes[Mid6Bit].LicenseItem.ResourceNum)
                    Info.HWRes[Mid6Bit].LicenseItem.ResourceNum = Last6Bit;
                Info.HWRes[Mid6Bit].LicenseItem.Days = Days;

                auto iter = m_ResItemExpTable.find(Mid6Bit);
                if (iter == m_ResItemExpTable.end())
                {
                    m_ResItemExpTable.insert(std::make_pair(Mid6Bit, EndTimeTicks));
                }

                //获取资源数
                m_HWResNum[Mid6Bit] = Info.HWRes[Mid6Bit].LicenseItem.ResourceNum;
            }
            else if(First4Bit == WT_FREQ_BAND_TYPE)
            {
                Info.Freq[Last6Bit].Available = true;
                Info.Freq[Last6Bit].StartTimeTicks = StartTimeTicks;
                Info.Freq[Last6Bit].LicenseItem.LicTech = LIC_MAX_TECH;
                Info.Freq[Last6Bit].LicenseItem.LicType = WT_FREQ_BAND_TYPE;
                Info.Freq[Last6Bit].LicenseItem.LicValue = Last6Bit;
                Info.Freq[Last6Bit].LicenseItem.ResourceNum = 0;
                Info.Freq[Last6Bit].LicenseItem.Days = Days;
                m_FreqItemExpTable.insert(std::make_pair(Last6Bit, EndTimeTicks));
            }
            else if(First4Bit == WT_PROT_TYPE)
            {
                Info.Business[Last6Bit].Available = true;
                Info.Business[Last6Bit].StartTimeTicks = StartTimeTicks;
                Info.Business[Last6Bit].LicenseItem.ResourceNum = 0;
                Info.Business[Last6Bit].LicenseItem.Days = Days;
                Info.Business[Last6Bit].LicenseItem.LicType = WT_PROT_TYPE;
                Info.Business[Last6Bit].LicenseItem.LicValue = Last6Bit;
                m_BusiItemExpTable.insert(std::make_pair(Last6Bit, EndTimeTicks));
            }
            else
            {
                continue;
            }
        }
    }
#if LIC_PRINTF_ON
    gettimeofday(&tv, NULL);
    int End = tv.tv_sec * 1000000 + tv.tv_usec;
    WTLog::Instance().GettmpLogStream(LOG_DEBUG) << "#####ThransLic use time us: " << (End - start) << endl;
#endif
    return Ret;
}

void License::InitDateTime(TIME_TYPE &TMDate, int Year, int Month, int Day, int Flag)
{
    TMDate.year = Year;
    TMDate.mon = Month;
    TMDate.mday = Day;
    if(Flag == 1)   //表示结束时间
    {
        TMDate.hour = 23;
        TMDate.min = 59;
        TMDate.sec = 59;
    }
    else           //否则表示为开始时间
    {
        TMDate.hour = 0;
        TMDate.min = 0;
        TMDate.sec = 0;
    }
}

void License::SplitUpdateLicTypetoDetail(short UpdateLicType, int &TechType, int &LicType, int &Value)
{
    TechType = (UpdateLicType >> LIC_BIT_HIGHT) & 0xF; //short Bit12_15
    LicType = (UpdateLicType >> LIC_BIT_MID) & 0x1F;   //short Bit7_11
    Value = (UpdateLicType >> LIC_BIT_LOW) & 0x7F;     //short Bit0_6
#if LIC_PRINTF_ON
    WTLog::Instance().WriteLog(LOG_DEBUG, "First=%d,Mid=%d,last=%d\n", TechType, LicType, Value);
#endif
}

//耗时较长，有时导致manager不能及时收到心跳，不在定时器中断里执行具体动作，改为新建线程执行。
void License::ReFreshLicTimerCb(wtev::timer &watcher, int revents)
{
    (void)watcher;
    (void)revents;
    std::thread(&License::ReFreshLicTimer, this).detach();
}

void License::ReFreshLicTimer()
{
    auto CheckAndSetTime = [&](struct timeval CheckTime) -> bool
    {
        struct timeval NowTime;
        gettimeofday(&NowTime, NULL);
        if ((NowTime.tv_sec - CheckTime.tv_sec > SRV_TIMER_INTERVAL + 1) ||
            (NowTime.tv_sec - CheckTime.tv_sec == SRV_TIMER_INTERVAL && NowTime.tv_usec > CheckTime.tv_usec))
        {
            return true;
        }
        else
        {
            return false;
        }
    };

    //获取当前仪器中lic项原始内容
    std::unique_lock<std::mutex> lck(m_Mutex, std::defer_lock);
    if (lck.try_lock())
    {
        if (CheckAndSetTime(m_CheckTime))
        {
            m_LicStatus = GetCurDevLicenses();
            CheckLicExpTime(false);
        }
    }
}
