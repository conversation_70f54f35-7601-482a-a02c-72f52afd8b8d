/***************************************************************************************************
 * This confidential and proprietary software may be only used as authorized by a licensing 
 * agreement from iTest Technology Co., Ltd.
 * (C) COPYRIGHT 2020 iTest Technology Co., Ltd. ALL RIGHTS RESERVED
 *--------------------------------------------------------------------------------------------------
 * Filename             : algvsg_3gpp_main.h
 * Author               : Linden
 * Data                 : 2022-08
 * Description          :       
 * Modification History :
 * Data            By          Version         Change Description
 *--------------------------------------------------------------------------------------------------
 * Reference to the source file.
 **************************************************************************************************/

#ifndef ALGVSG_3GPP_MAIN_H_
#define ALGVSG_3GPP_MAIN_H_

/***************************************************************************************************
 *                                          include
 **************************************************************************************************/
#include "algvsg_3gpp_export.h"

/***************************************************************************************************
 *                                Macro or Debug Configuration
 **************************************************************************************************/


/***************************************************************************************************
 *                                      Enum Definition
 **************************************************************************************************/


/***************************************************************************************************
 *                                    Struct Definition
 **************************************************************************************************/


/***************************************************************************************************
 *                                  Extern Variable Statement
 **************************************************************************************************/


/***************************************************************************************************
 *                                  Extern Function Statement
 **************************************************************************************************/

/***************************************************************************************************
 *                                  Export Variable Statement
 **************************************************************************************************/

/***************************************************************************************************
 *                                  Export Function Statement
 **************************************************************************************************/
ALGVSG_3GPP_API int Callback_3GPP_VsgLibInit(void);

/* Release algorith lib dynamic allocation memory */
ALGVSG_3GPP_API void Callback_3GPP_VsgLibDeinit(void);

ALGVSG_3GPP_API char *Callback_3GPP_VsgDllVersion(void);

/* 1. Initialize VSA input parameter.
 * 2. Following parameter shall be set by FW firstly,
 *      pInInfo->CommonParam.standard
 *      5G      : pInInfo->NR.LinkDirect;
 *      4G      : pInInfo->LTE.LinkDirect;
 *      NBIOT   : pInInfo->NBIOT.LinkDirect;
 *      WCDMA   : pInInfo->WCDMA.LinkDirect;
 *      GSM     : NA;
 *      Sidelink: TODO.
 * 3. Following parameter shall be initialized:
 *      pInInfo->CommonParam.subType
 *      pInInfo->CommonParam.FreqErr
 *      pInInfo->CommonParam.IQImbalanceAmp
 *      pInInfo->CommonParam.IQImbalancePhase
 *      pInInfo->CommonParam.DCOffset_I
 *      pInInfo->CommonParam.DCOffset_Q
 *      pInInfo->CommonParam.ClockErr
 *      pInInfo->CommonParam.Snr
 *      pInInfo->CommonParam.Gap
 *      pInInfo->CommonParam.FlatFactor
 *      pInInfo->CommonParam.PhaseNoiseFlg
 *      pInInfo->CommonParam.PhaseNoiseFactor[0-5]
 *      pInInfo->CommonParam.samplingRate
 *      pInInfo->LTE/NR/NBIOT/WCDMA/GSM/SL
 */
ALGVSG_3GPP_API void Callback_3GPP_VsgParamInit(Alg_3GPP_WaveGenType *pInInfo);

ALGVSG_3GPP_API int Callback_3GPP_VsgMain(
    Alg_3GPP_WaveGenType *input, Complex **outdat,
    int *outlen, void **pOutExtData);

#endif /* ALGVSG_3GPP_MAIN_H_ */
