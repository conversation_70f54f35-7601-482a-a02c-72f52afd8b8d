//*****************************************************************************
//  File: wtypes.h
//  WT300常见数据类型定义
//  Data: 2016.7.16
//*****************************************************************************
#ifndef __WT_TYPES_H__
#define __WT_TYPES_H__

//License Technology类型
enum WT_TECHNOLOGY_E
{
    LIC_GPRF,
    LIC_WIFI_SISO,
    LIC_WIFI_MIMO,
    LIC_BLUETOOTH,
    LIC_INTER,   //交互模式，自动添加VSA-VSG以及VSG-VSA; 取消原TB、TF模式的Technology
    LIC_DEVM,
    LIC_CELLULAR,
    LIC_OTHER,
    LIC_MODE,    //软件模式license(协议类型)
    LIC_MAX_TECH //无效，仅作结尾标识用
};

//License类型
enum WT_LIC_TYPE_E
{
    WT_HW_TYPE,             //硬件资源类型
    WT_FREQ_BAND_TYPE,      //频段类型
    WT_PROT_TYPE,           //业务协议类型
};

// license许可的硬件资源类型定义
enum WT_LIC_RESOURCE_E
{
    WT_RF_PORT_NUM,          // RF端口数量类型(普通端口)
    WT_ETH_PORT_NUM,         // 网口数类型
    WT_LINK_NUM,             // 连接数类型
    WT_VSG_UNIT_NUM,         // VSG硬件单元数量类型
    WT_VSA_UNIT_NUM,         // VSA硬件单元数量类型
    WT_SPEC_RF_PORT9,        // RF高功率port9，目前仅328有
    WT_USE_MODULES_PARALLET, //模块允许并行使用的license,318使用
    WT_DIGITAL_ETH_NUM,      //数字IQ网口数量
    WT_MAX_RES               // 无效，仅作结尾标识用
};

//频段类型定义
enum WT_FREQ_BAND_E
{
    WT_BAND_1G,         // 400M-1G
    WT_BAND_2G,         // 1G-2.4G
    WT_BAND_2_4G,       // 2.4G-2.5G
    WT_BAND_3G,         // 2.5G-3.8G
    WT_BAND_4G,         // 3.8G-4.9G
    WT_BAND_5G,         // 4.9G-6G
    WT_BAND_6G,         // 6G-7.3G
    WT_BAND_8G,         // 7.3G-9G
    WT_MAX_BAND         //无效，仅作结尾标识用
};

// 业务协议定义
enum WT_PROT_E
{
    // GPRF
    WT_GPRF,

    // WIFI SISO
    WT_WIFI_SISO,
    WT_11A,
    WT_11B,
    WT_11G,
    WT_11N,
    WT_11P,
    WT_11AC,
    WT_11AX,
    WT_11BE,
    WT_11AH,
    WT_11AD,
    WT_160M,
    WT_320M,      //连续320M
    WT_MULIT_SEG, // 80+80、160+160、320+320等多单元频点组合测试
    WT_CMIMO,
    WT_MAC_AC, // 11a/b/g/n/ac等基础业务的MAC生成、解析控制开关；
               // MAC的基础开关（无该Licence时，UI不显示相关操作，仪器也不响应相关命令）,11a/b/g/n可以不进行细分
    WT_MAC_AX, // 11ax MAC生成解析；依赖于WT_MAC
               // Generator：在11ax业务下，根据是否存在该Licence决定是否处理进一步生成事宜
               // VSA：在非Auto Detect模式下，如果不存在该Licence，不处理11ax、11be的MAC分析及显示操作，在Auto Dect下，则不进行显示
    WT_MAC_BE, // 11be MAC生成解析；依赖于WT_MAC_ax
               //类似WT_MAC_ax

    // WIFI MIMO
    WT_WIFI_MIMO,     //直接到8x8 MIMO
    WT_MUMIMO,        //直接到8x8 MU-MIMO
    WT_WIFI_MAS_MIMO, // 8x8以上MIMO，(不包含8流)依赖于WT_WIFI_MIMO,eg(12*12),MAS(massive)
    WT_MAS_MUMIMO,    // 8x8以上MU-MIMO，(不包含8流)依赖于WT_MUMIMO，WT_WIFI_MAS_MIMO,eg(12*12),MAS(massive)
    WT_SWITCHED_MIMO,

    // ZigBee
    WT_ZIGBEE, //建议删除

    // Bluetooth
    WT_BT,

    // Navigation					//建议删除定位相关Lic
    WT_GPS,
    WT_BEIDOU,
    WT_GALILEO,
    WT_GLONASS,

    // Cellular
    WT_CELL_2G,
    WT_CELL_3G,
    WT_CELL_LTE,
    WT_CELL_5G,

    // Z-mave
    WT_ZWAVE,

    // DEVM
    WT_DEVM,

    // Interactive
    WT_MAC_INTER_AC,   // 11a/b/g/n/ac等基础业务的交互测试控制开关，依赖于WT_MAC_ac
    WT_MAC_INTER_AX,   // 11ax的交互测试控制开关，依赖于WT_MAC_ax、WT_MAC_Inter_ac
    WT_MAC_INTER_BE,   // 11be的交互测试控制开关，依赖于WT_MAC_be、WT_MAC_Inter_ax
    WT_MAC_ENCRYPTION, //加解密处理，依赖于WT_MAC_ac

    // Other 与其他业务组合，不单独成某一Technology
    WT_IBF,      // Implicit Beamforming，隐式Beamforming
    WT_SEQUENCE, // List mode， Sequence控制开关
    WT_DIQ_AC,   //数字IQ基础Licence，使能数字IQ的通讯能力，可以与MIMO、MAC组合拓宽测试范围;通过网口传输基带数据，不支持监视与子仪器
                 // VSA解析时，使能11a/b/g/n/ac等基础业务的帧/MAC解析
    WT_DIQ_AX,   // 11ax数字IQ解析开关，依赖于WT_DIQ_ac
    WT_DIQ_BE,   // 11be数字IQ解析开关，依赖于WT_DIQ_ax

    //模式license
    WT_SIGNALING,
    WT_BEAMFORMING, // Explicit （显示）Beamforming，依赖于mimo
    WT_PER,

    WT_INTER,          //使能RF模式下的交互测试
    WT_SUB_INSTRUMENT, //子仪器功能
    WT_AIQ,            //模拟IQ
    WT_BT5_1,          // BT5.1,BT5.2
    WT_SLE,            // SLE
    WT_11BA,             // 802.11BA
    WT_11AZ,             // 802.11AZ
    WT_PAC,
    WT_HWO,             //BaseBand
    WT_WISUN,
    WT_LORA,
    WT_EVMC,
    WT_CHANNEL_MODE,
    WT_CHANNEL_MATRIX,
    WT_DIQ_MULTI_CELL,
    WT_BT_HDR,
    WT_WAVE_DECRYPT,
    WT_BROADCAST,  //广播
    WT_WIFI6E,  //WIFI6E
    WT_SUB_NET,  //子网口
    WT_LTE_IoT,
    WT_BLE_ADVERTISING,
    WT_DECT,
    WT_HIGH_POWER,
    WT_MAC_ADVANCED,
    WT_DPD,
    WT_11BN,
    WT_11BN_ADV,
    WT_MLO_RF,
    WT_CLOCKRATE,
    WT_BT6,
    WT_BT_HDT,
    WT_SLP,
    WT_UWB,
    WT_NR, //5G NR
    WT_DIQ_BN,
    WT_MAC_BN,
    WT_MAC_INTER_BN,
    WT_GNSS_DB,

    WT_MAX_PROT, //无效，仅作结尾标识用
};

//测试类型定义  -- 与Licence无关
enum WT_TEST_TYPE
{
    TEST_SISO,           //SISO
    TEST_80_80M,         //80+80M
    TEST_SELF_MIMO,      //单机MIMO
    TEST_MULTI_MIMO,     //多机MIMO
    TEST_CMIMO,          //CMIMO
    TEST_SWITCHED_MIMO,  //Switched MIMO
    //4xx新增
    DigitalIQ,           //数字IQ模式； 整机模式，通过网口传输基带数据，不支持监视与子仪器；在该模式下，需进一步判断相应的业务Licence
    Interactive,         //交互模式；   切换到该模式后，新增加交互测试的Technology，在此基础上需进一步判断业务Licence
    TEST_DEVM,           //DEVM模式

    //仅固件内部使用
    TEST_80_80M_AGC,     //80+80M 双端口模式或双射频参数模式AGC
};

enum Extend_EVM_MODE
{
    Extend_EVM_NORMAL,
    Extend_EVM_VECTORAVG,
    Extend_EVM_DUALVSA,
    Extend_EVM_NOISECOMPENSATION,
};

//RF ID定义
enum WT_PORT_E
{
    WT_RF_OFF,
    WT_RF_1 = 1,
    WT_RF_2,
    WT_RF_3,
    WT_RF_4,
    WT_RF_5,
    WT_RF_6,
    WT_RF_7,
    WT_RF_8,
    WT_RF_MAX,
};

//仪器类型，需Add 4xx，与Licence无关
enum MODEL_TYPE
{
    MODEL_TYPE_WT160,
    MODEL_TYPE_WT200,
    MODEL_TYPE_WT200B,
    MODEL_TYPE_WT208 = 3,
    MODEL_TYPE_WT208C = 4,

    MODEL_TYPE_WT300 = 14,
    MODEL_TYPE_WT328 = 15,
    MODEL_TYPE_WT318 = 16,
    MODEL_TYPE_WT328E = 17,
    MODEL_TYPE_WT328C = 18,

    MODEL_TYPE_WT448 = 30,
    MODEL_TYPE_WT428 = 31,
    MODEL_TYPE_WT328CE = 32,
    MODEL_TYPE_WT422 = 33,
    MODEL_TYPE_WT428C = 34,
    MODEL_TYPE_WT428H = 35,
    MODEL_TYPE_CT550 = 36,
    MODEL_TYPE_INVALID = 0xFFFF
};

#define LIC_BIT_HIGHT 12
#define LIC_BIT_MID 7
#define LIC_BIT_LOW 0

//生成工具使用的lic类型对照表,note:当业务枚举大于64时，可能会导致license值相同冲突，当时只用了6位代表prot，或者后面加的业务license都用Max-tech
enum WT_GENARATE_LIC_TYPE
{
    WTL_GPRF        = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_GPRF<<LIC_BIT_MID) + WT_GPRF,

    WTL_WIFI_SISO   = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_WIFI_SISO,
    WTL_11A         = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_11A,
    WTL_11B         = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_11B,
    WTL_11G         = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_11G,
    WTL_11N         = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_11N,
    WTL_11P         = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_11P,
    WTL_11AC        = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_11AC,
    WTL_11AX        = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_11AX,
    WTL_11BE        = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_11BE,
    WTL_11AH        = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_11AH,
    WTL_11AD        = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_11AD,
    WTL_160M        = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_160M,
    WTL_320M        = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_320M,
	WTL_MULIT_SEG   = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_MULIT_SEG,
    WTL_CMIMO       = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_CMIMO,
    WTL_MAC_11AC    = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_MAC_AC,
    WTL_MAC_11AX    = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_MAC_AX,
    WTL_MAC_11BE    = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_SISO<<LIC_BIT_MID) + WT_MAC_BE,

    WTL_WIFI_MIMO       = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_MIMO<<LIC_BIT_MID) + WT_WIFI_MIMO,
    WTL_MUMIMO          = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_MIMO<<LIC_BIT_MID) + WT_MUMIMO,
    WTL_WIFI_MAS_MIMO   = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_MIMO<<LIC_BIT_MID) + WT_WIFI_MAS_MIMO,
    WTL_MAS_MUMIMO      = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_MIMO<<LIC_BIT_MID) + WT_MAS_MUMIMO,
    WTL_SWITCHED_MIMO   = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_WIFI_MIMO<<LIC_BIT_MID) + WT_SWITCHED_MIMO,

    WTL_ZIGBEE      = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_ZIGBEE,
    WTL_BT          = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_BLUETOOTH<<LIC_BIT_MID) + WT_BT,
    WTL_ZWAVE       = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_ZWAVE,

    WTL_GPS         = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_GPS,                         //建议删除
    WTL_BEIDOU      = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_BEIDOU,                      //建议删除
    WTL_GALILEO     = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_GALILEO,                     //建议删除
    WTL_GLONASS     = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_GLONASS,

    WTL_CELL_2G     = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_CELLULAR<<LIC_BIT_MID) + WT_CELL_2G,
    WTL_CELL_3G     = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_CELLULAR<<LIC_BIT_MID) + WT_CELL_3G,
    WTL_CELL_LTE    = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_CELLULAR<<LIC_BIT_MID) + WT_CELL_LTE,
    WTL_CELL_5G     = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_CELLULAR<<LIC_BIT_MID) + WT_CELL_5G,

    WTL_DEVM        = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_DEVM<<LIC_BIT_MID) + WT_DEVM,

    WTL_MAC_INTER_AC    = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_INTER<<LIC_BIT_MID) + WT_MAC_INTER_AC,
    WTL_MAC_INTER_AX    = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_INTER<<LIC_BIT_MID) + WT_MAC_INTER_AX,               //建议删除
    WTL_MAC_INTER_BE    = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_INTER<<LIC_BIT_MID) + WT_MAC_INTER_BE,               //建议删除
    WTL_MAC_ENCRYPTION  = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_INTER<<LIC_BIT_MID) + WT_MAC_ENCRYPTION,	

    WTL_IBF             = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_OTHER<<LIC_BIT_MID) + WT_IBF,
    WTL_SEQUENCE        = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_OTHER<<LIC_BIT_MID) + WT_SEQUENCE,
    WTL_DIQ_AC          = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_OTHER<<LIC_BIT_MID) + WT_DIQ_AC,
    WTL_DIQ_AX          = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_OTHER<<LIC_BIT_MID) + WT_DIQ_AX,
    WTL_DIQ_BE          = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_OTHER<<LIC_BIT_MID) + WT_DIQ_BE,

    WTL_SIGNALING   = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_SIGNALING,
    WTL_BEAMFORMING = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_BEAMFORMING,
    WTL_PER         = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_PER,

    WTL_INTER           = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_INTER,
    WTL_SUB_INSTRUMENT  = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_SUB_INSTRUMENT,
    WTL_AIQ             = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_AIQ,
    WTL_BT5_1           = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_BT5_1,
    WTL_SLE             = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_SLE,
    WTL_11BA            = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_11BA,
    WTL_11AZ            = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_11AZ,
    WTL_PAC             = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_PAC,

    WTL_WISUN           = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_WISUN,
    WTL_LORA            = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_LORA,
    WTL_EVMC            = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_EVMC,
    WTL_CHANNEL_MODE    = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_CHANNEL_MODE,
    WTL_CHANNEL_MATRIX  = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_CHANNEL_MATRIX,
    WTL_DIQ_MULTI_CELL  = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_DIQ_MULTI_CELL,
    WTL_BT_HDR          = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_BT_HDR,
    WTL_WAVE_DECRYPT    = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_WAVE_DECRYPT,
    WTL_VSG_BRD         = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_BROADCAST,
    WTL_WIFI6E          = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_WIFI6E,
    WTL_SUB_NET         = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_SUB_NET,
    WTL_LTE_IoT         = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_LTE_IoT,
    WTL_BLE_ADVERTISING = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_BLE_ADVERTISING,
    WTL_DECT            = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_DECT,
    WTL_HIGH_POWER      = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_HIGH_POWER,
    WTL_MAC_ADVANCED    = (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_MAC_ADVANCED,
    WTL_DPD             =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_DPD,
    WTL_11BN            =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_11BN,
    WTL_11BN_ADV        =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_11BN_ADV,
    WTL_MLO_RF          =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_MLO_RF,
    WTL_CLOCKRATE       =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_CLOCKRATE,
    WTL_BT6             =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_BT6,
    WTL_BT_HDT          =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_BT_HDT,
    WTL_SLP             =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_SLP,
    WTL_UWB             =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_UWB,
    WTL_NR              =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_NR,
    WTL_DIQ_BN          =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_DIQ_BN,
    WTL_MAC_BN          =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_MAC_BN,
    WTL_MAC_INTER_BN    =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_MAC_INTER_BN, 
    WTL_GNSS_DB         =  (WT_PROT_TYPE<<LIC_BIT_HIGHT) + (LIC_MODE<<LIC_BIT_MID) + WT_GNSS_DB, 

    WTL_BAND_1G     = (WT_FREQ_BAND_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_BAND_1G,
    WTL_BAND_2G     = (WT_FREQ_BAND_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_BAND_2G,
    WTL_BAND_2_4G   = (WT_FREQ_BAND_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_BAND_2_4G,
    WTL_BAND_3G     = (WT_FREQ_BAND_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_BAND_3G,
    WTL_BAND_4G     = (WT_FREQ_BAND_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_BAND_4G,
    WTL_BAND_5G     = (WT_FREQ_BAND_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_BAND_5G,
    WTL_BAND_6G     = (WT_FREQ_BAND_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_BAND_6G,
    WTL_BAND_8G     = (WT_FREQ_BAND_TYPE<<LIC_BIT_HIGHT) + (LIC_MAX_TECH<<LIC_BIT_MID) + WT_BAND_8G,

    WTL_RF_PORT_1   = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_RF_PORT_NUM<<LIC_BIT_MID) + 1,
    WTL_RF_PORT_2   = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_RF_PORT_NUM<<LIC_BIT_MID) + 2,
    WTL_RF_PORT_4   = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_RF_PORT_NUM<<LIC_BIT_MID) + 4,
    WTL_RF_PORT_8   = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_RF_PORT_NUM<<LIC_BIT_MID) + 8,
    WTL_ETH_PORT_4  = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_ETH_PORT_NUM<<LIC_BIT_MID) + 4,
    WTL_ETH_PORT_8  = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_ETH_PORT_NUM<<LIC_BIT_MID) + 8,
    WTL_LINK_4      = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_LINK_NUM<<LIC_BIT_MID) + 4,
    WTL_LINK_8      = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_LINK_NUM<<LIC_BIT_MID) + 8,
    WTL_VSG_UNIT_1  = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_VSG_UNIT_NUM<<LIC_BIT_MID) + 1,
    WTL_VSG_UNIT_2  = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_VSG_UNIT_NUM<<LIC_BIT_MID) + 2,
    WTL_VSG_UNIT_4  = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_VSG_UNIT_NUM<<LIC_BIT_MID) + 4,
    WTL_VSA_UNIT_1  = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_VSA_UNIT_NUM<<LIC_BIT_MID) + 1,
    WTL_VSA_UNIT_2  = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_VSA_UNIT_NUM<<LIC_BIT_MID) + 2,
    WTL_VSA_UNIT_4  = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_VSA_UNIT_NUM<<LIC_BIT_MID) + 4,
    WTL_HP_RF_PORT_9 = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_SPEC_RF_PORT9<<LIC_BIT_MID) + 1,
    WTL_USE_MODULES_PARALLET = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_USE_MODULES_PARALLET<<LIC_BIT_MID) + 1,
    WTL_DIGITAL_ETH_NUM = (WT_HW_TYPE<<LIC_BIT_HIGHT) + (WT_DIGITAL_ETH_NUM<<LIC_BIT_MID) + 1,
};
#endif
