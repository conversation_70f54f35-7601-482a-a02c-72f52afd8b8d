/**
 * @file scpi_3gpp_alz_default_param.cpp
 * @brief scpi蜂窝默认vsa分析设置
 * @version 0.1
 * @date 2024-09-21
 * 
 * @copyright Copyright (c) 2024
 * 
 */

#include "scpi_3gpp_alz_default_param.h"
#include "scpi_3gpp_common.h"

using namespace cellular::method;

// C++11 兼容的 create_unique 实现
template<typename T, typename... Args>
std::unique_ptr<T> create_unique(Args&&... args) {
    return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
}

// 公共接口实现
void cellular::alz::SetDefaultParam(int standard, int linkDirect, AlzParam3GPP &param)
{
    std::unique_ptr<AlzParamBase> alzParam;
    switch (standard) {
    case ALG_3GPP_STD_GSM:
        alzParam = create_unique<GsmAlzParamFactory>()->CreateAlzParam();
        break;
    case ALG_3GPP_STD_WCDMA:
        alzParam = create_unique<WcdmaAlzParamFactory>()->CreateAlzParam();
        break;
    case ALG_3GPP_STD_4G:
        alzParam = create_unique<LteAlzParamFactory>()->CreateAlzParam();
        break;
    case ALG_3GPP_STD_5G:
        alzParam = create_unique<NrAlzParamFactory>()->CreateAlzParam();
        break;
    case ALG_3GPP_STD_NB_IOT:
        alzParam = create_unique<NbiotAlzParamFactory>()->CreateAlzParam();
        break;
    default:
        break;
    }

    if (alzParam) {
        alzParam->SetDefaultParam(param, linkDirect);
    }
}

void cellular::alz::GsmAlzParam::SetDefaultParam(AlzParam3GPP &param, int linkDirect)
{
    (void)linkDirect;

    auto &gsm = param.GSM;
    memset(&gsm, 0, sizeof(gsm));
    gsm.NumbOfSlot = 1;

    for (int i = 0; i < 11; i++)
    {
        gsm.SpectMod.OffsetState[i] = 1;
    }
    gsm.SpectMod.FreqOffset[0] = 0.1;
    gsm.SpectMod.FreqOffset[1] = 0.2;
    gsm.SpectMod.FreqOffset[2] = 0.25;
    gsm.SpectMod.FreqOffset[3] = 0.4;
    gsm.SpectMod.FreqOffset[4] = 0.6;
    gsm.SpectMod.FreqOffset[5] = 0.8;
    gsm.SpectMod.FreqOffset[6] = 1.0;
    gsm.SpectMod.FreqOffset[7] = 1.2;
    gsm.SpectMod.FreqOffset[8] = 1.4;
    gsm.SpectMod.FreqOffset[9] = 1.6;
    gsm.SpectMod.FreqOffset[10] = 1.8;
    gsm.SpectMod.FreqOffset[11] = 1.9;
    gsm.SpectMod.FreqOffset[12] = 1.9;
    gsm.SpectMod.FreqOffset[13] = 1.9;
    gsm.SpectMod.FreqOffset[14] = 1.9;
    gsm.SpectMod.FreqOffset[15] = 1.9;
    gsm.SpectMod.FreqOffset[16] = 1.9;
    gsm.SpectMod.FreqOffset[17] = 1.9;
    gsm.SpectMod.FreqOffset[18] = 1.9;
    gsm.SpectMod.FreqOffset[19] = 1.9;

    for (int i = 0; i < 4; i++)
    {
        gsm.SpectSwt.OffsetState[i] = 1;
    }
    gsm.SpectSwt.FreqOffset[0] = 0.4;
    gsm.SpectSwt.FreqOffset[1] = 0.6;
    gsm.SpectSwt.FreqOffset[2] = 1.2;
    gsm.SpectSwt.FreqOffset[3] = 1.8;
    gsm.SpectSwt.FreqOffset[4] = 1.9;
    gsm.SpectSwt.FreqOffset[5] = 1.9;
    gsm.SpectSwt.FreqOffset[6] = 1.9;
    gsm.SpectSwt.FreqOffset[7] = 1.9;
    gsm.SpectSwt.FreqOffset[8] = 1.9;
    gsm.SpectSwt.FreqOffset[9] = 1.9;
    gsm.SpectSwt.FreqOffset[10] = 1.9;
    gsm.SpectSwt.FreqOffset[11] = 1.9;
    gsm.SpectSwt.FreqOffset[12] = 1.9;
    gsm.SpectSwt.FreqOffset[13] = 1.9;
    gsm.SpectSwt.FreqOffset[14] = 1.9;
    gsm.SpectSwt.FreqOffset[15] = 1.9;
    gsm.SpectSwt.FreqOffset[16] = 1.9;
    gsm.SpectSwt.FreqOffset[17] = 1.9;
    gsm.SpectSwt.FreqOffset[18] = 1.9;
    gsm.SpectSwt.FreqOffset[19] = 1.9;

    // LimitInfo.ModLimit
    gsm.LimitInfo.ModLimit[0].EvmRms.LimitValue = 10;
    gsm.LimitInfo.ModLimit[0].EvmPeak.LimitValue = 35;
    gsm.LimitInfo.ModLimit[0].Evm95Percent.Limit = 20;
    gsm.LimitInfo.ModLimit[0].MErrRms.LimitValue = 10;
    gsm.LimitInfo.ModLimit[0].MErrPeak.LimitValue = 35;
    gsm.LimitInfo.ModLimit[0].MErr95Percent.Limit = 20;
    gsm.LimitInfo.ModLimit[0].PhErrRms.LimitValue = 5;
    gsm.LimitInfo.ModLimit[0].PhErrRms.Current = 1;
    gsm.LimitInfo.ModLimit[0].PhErrRms.Average = 1;
    gsm.LimitInfo.ModLimit[0].PhErrRms.Max = 1;
    gsm.LimitInfo.ModLimit[0].PhErrPeak.LimitValue = 20;
    gsm.LimitInfo.ModLimit[0].PhErrPeak.Current = 1;
    gsm.LimitInfo.ModLimit[0].PhErrPeak.Average = 1;
    gsm.LimitInfo.ModLimit[0].PhErrPeak.Max = 1;
    gsm.LimitInfo.ModLimit[0].PhErr95Percent.Limit = 10;
    gsm.LimitInfo.ModLimit[0].IQOffset.LimitValue = -30;
    gsm.LimitInfo.ModLimit[0].IQImbalance.LimitValue = -30;
    gsm.LimitInfo.ModLimit[0].FreError.LimitValue = 90;
    gsm.LimitInfo.ModLimit[0].FreError.Current = 1;
    gsm.LimitInfo.ModLimit[0].FreError.Average = 1;
    gsm.LimitInfo.ModLimit[0].FreError.Max = 1;
    gsm.LimitInfo.ModLimit[0].PhErrRms.LimitValue = 10;

    gsm.LimitInfo.ModLimit[1].EvmRms.LimitValue = 9;
    gsm.LimitInfo.ModLimit[1].EvmRms.Current = 1;
    gsm.LimitInfo.ModLimit[1].EvmRms.Average = 1;
    gsm.LimitInfo.ModLimit[1].EvmRms.Max = 1;
    gsm.LimitInfo.ModLimit[1].EvmPeak.LimitValue = 30;
    gsm.LimitInfo.ModLimit[1].EvmPeak.Current = 1;
    gsm.LimitInfo.ModLimit[1].EvmPeak.Average = 1;
    gsm.LimitInfo.ModLimit[1].EvmPeak.Max = 1;
    gsm.LimitInfo.ModLimit[1].Evm95Percent.Limit = 15;
    gsm.LimitInfo.ModLimit[1].Evm95Percent.State = 1;
    gsm.LimitInfo.ModLimit[1].MErrRms.LimitValue = 9;
    gsm.LimitInfo.ModLimit[1].MErrRms.Current = 1;
    gsm.LimitInfo.ModLimit[1].MErrRms.Average = 1;
    gsm.LimitInfo.ModLimit[1].MErrRms.Max = 1;
    gsm.LimitInfo.ModLimit[1].MErrPeak.LimitValue = 30;
    gsm.LimitInfo.ModLimit[1].MErr95Percent.Limit = 15;
    gsm.LimitInfo.ModLimit[1].PhErrRms.LimitValue = 5;
    gsm.LimitInfo.ModLimit[1].PhErrPeak.LimitValue = 20;
    gsm.LimitInfo.ModLimit[1].PhErr95Percent.Limit = 10;
    gsm.LimitInfo.ModLimit[1].IQOffset.LimitValue = -30;
    gsm.LimitInfo.ModLimit[1].IQOffset.Average = 1;
    gsm.LimitInfo.ModLimit[1].IQImbalance.LimitValue = -30;
    gsm.LimitInfo.ModLimit[1].FreError.LimitValue = 90;
    gsm.LimitInfo.ModLimit[1].FreError.Current = 1;
    gsm.LimitInfo.ModLimit[1].FreError.Average = 1;
    gsm.LimitInfo.ModLimit[1].FreError.Max = 1;
    gsm.LimitInfo.ModLimit[1].PhErrRms.LimitValue = 10;

    gsm.LimitInfo.ModLimit[2].EvmRms.LimitValue = 7;
    gsm.LimitInfo.ModLimit[2].EvmRms.Current = 1;
    gsm.LimitInfo.ModLimit[2].EvmRms.Average = 1;
    gsm.LimitInfo.ModLimit[2].EvmRms.Max = 1;
    gsm.LimitInfo.ModLimit[2].EvmPeak.LimitValue = 30;
    gsm.LimitInfo.ModLimit[2].EvmPeak.Current = 1;
    gsm.LimitInfo.ModLimit[2].EvmPeak.Average = 1;
    gsm.LimitInfo.ModLimit[2].EvmPeak.Max = 1;
    gsm.LimitInfo.ModLimit[2].Evm95Percent.Limit = 15;
    gsm.LimitInfo.ModLimit[2].Evm95Percent.State = 1;
    gsm.LimitInfo.ModLimit[2].MErrRms.LimitValue = 7;
    gsm.LimitInfo.ModLimit[2].MErrRms.Current = 1;
    gsm.LimitInfo.ModLimit[2].MErrRms.Average = 1;
    gsm.LimitInfo.ModLimit[2].MErrRms.Max = 1;
    gsm.LimitInfo.ModLimit[2].MErrPeak.LimitValue = 30;
    gsm.LimitInfo.ModLimit[2].MErr95Percent.Limit = 15;
    gsm.LimitInfo.ModLimit[2].PhErrRms.LimitValue = 5;
    gsm.LimitInfo.ModLimit[2].PhErrPeak.LimitValue = 20;
    gsm.LimitInfo.ModLimit[2].PhErr95Percent.Limit = 10;
    gsm.LimitInfo.ModLimit[2].IQOffset.LimitValue = -30;
    gsm.LimitInfo.ModLimit[2].IQOffset.Average = 1;
    gsm.LimitInfo.ModLimit[2].IQImbalance.LimitValue = -30;
    gsm.LimitInfo.ModLimit[2].FreError.LimitValue = 90;
    gsm.LimitInfo.ModLimit[2].FreError.Current = 1;
    gsm.LimitInfo.ModLimit[2].FreError.Average = 1;
    gsm.LimitInfo.ModLimit[2].FreError.Max = 1;
    gsm.LimitInfo.ModLimit[2].PhErrRms.LimitValue = 10;

    // LimitInfo.PVTLimit
    gsm.LimitInfo.PVTLimit.AvgLimit[0].State = 1;
    gsm.LimitInfo.PVTLimit.AvgLimit[0].FromPCL = 5;
    gsm.LimitInfo.PVTLimit.AvgLimit[0].ToPCL = 5;
    gsm.LimitInfo.PVTLimit.AvgLimit[0].Lower = -2;
    gsm.LimitInfo.PVTLimit.AvgLimit[0].Upper = 2;

    gsm.LimitInfo.PVTLimit.AvgLimit[1].State = 1;
    gsm.LimitInfo.PVTLimit.AvgLimit[1].FromPCL = 0;
    gsm.LimitInfo.PVTLimit.AvgLimit[1].ToPCL = 2;
    gsm.LimitInfo.PVTLimit.AvgLimit[1].Lower = -2;
    gsm.LimitInfo.PVTLimit.AvgLimit[1].Upper = 2;

    gsm.LimitInfo.PVTLimit.AvgLimit[2].State = 1;
    gsm.LimitInfo.PVTLimit.AvgLimit[2].FromPCL = 3;
    gsm.LimitInfo.PVTLimit.AvgLimit[2].ToPCL = 15;
    gsm.LimitInfo.PVTLimit.AvgLimit[2].Lower = -3;
    gsm.LimitInfo.PVTLimit.AvgLimit[2].Upper = 3;

    gsm.LimitInfo.PVTLimit.AvgLimit[3].State = 1;
    gsm.LimitInfo.PVTLimit.AvgLimit[3].FromPCL = 16;
    gsm.LimitInfo.PVTLimit.AvgLimit[3].ToPCL = 31;
    gsm.LimitInfo.PVTLimit.AvgLimit[3].Lower = -5;
    gsm.LimitInfo.PVTLimit.AvgLimit[3].Upper = 5;

    gsm.LimitInfo.PVTLimit.GuardPeriod.State = 1;
    gsm.LimitInfo.PVTLimit.GuardPeriod.Limit = 3;

    for (int i = 0; i < arraySize(gsm.LimitInfo.PVTLimit.UpperTemLimit); i++)
    {
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Start.Time = -38;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Start.LevelRel = -59;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Start.LevelAbs.State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Start.LevelAbs.Limit = -36;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Stop.Time = -28;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Stop.LevelRel = -3;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Stop.LevelAbs.State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[0].StaticLimt.Stop.LevelAbs.Limit = -48;
        
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Start.Time = -28;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Start.LevelRel = -30;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Start.LevelAbs.State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Start.LevelAbs.Limit = -17;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Stop.Time = -18;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Stop.LevelRel = -30;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Stop.LevelAbs.State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[1].StaticLimt.Stop.LevelAbs.Limit = -17;

        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[2].State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[2].StaticLimt.Start.Time = -18;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[2].StaticLimt.Start.LevelRel = -6;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[2].StaticLimt.Stop.Time = -10;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[2].StaticLimt.Stop.LevelRel = -6;

        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[3].State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[3].StaticLimt.Start.Time = -10;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[3].StaticLimt.Start.LevelRel = 4;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].RiseEdgeLimit[3].StaticLimt.Stop.LevelRel = 4;

        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[0].State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[0].StaticLimt.Start.LevelRel = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[0].StaticLimt.Stop.Time = 542.8;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].UsefulPartLimit[0].StaticLimt.Stop.LevelRel = 1;

        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[0].State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[0].StaticLimt.Start.Time = 542.8;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[0].StaticLimt.Start.LevelRel = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[0].StaticLimt.Stop.Time = 552.8;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[0].StaticLimt.Stop.LevelRel = 1;

        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[1].State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[1].StaticLimt.Start.Time = 552.8;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[1].StaticLimt.Start.LevelRel = -6;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[1].StaticLimt.Stop.Time = 560.8;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[1].StaticLimt.Stop.LevelRel = -6;

        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Start.Time = 560.8;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Start.LevelRel = -30;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Start.LevelAbs.State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Start.LevelAbs.Limit = -17;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Stop.Time = 570.8;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Stop.LevelRel = -30;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Stop.LevelAbs.State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[2].StaticLimt.Stop.LevelAbs.Limit = -17;

        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Start.Time = 570.8;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Start.LevelRel = -59;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Start.LevelAbs.State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Start.LevelAbs.Limit = -54;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Stop.Time = 580.8;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Stop.LevelRel = -59;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Stop.LevelAbs.State = 1;
        gsm.LimitInfo.PVTLimit.UpperTemLimit[i].FallEdgeLimit[3].StaticLimt.Stop.LevelAbs.Limit = -54;

        gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[0].State = 1;
        gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[0].StaticLimt.Start.LevelRel = -1;
        gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[0].StaticLimt.Stop.Time = 542.8;
        gsm.LimitInfo.PVTLimit.LowerTemlimit[i].UsefulPartLimit[0].StaticLimt.Stop.LevelRel = -1;
    }

    gsm.LimitInfo.PVTLimit.UpperTemLimit[2].RiseEdgeLimit[3].StaticLimt.Start.LevelRel = 6.5;
    gsm.LimitInfo.PVTLimit.UpperTemLimit[2].RiseEdgeLimit[3].StaticLimt.Stop.LevelRel = 6.5;
    gsm.LimitInfo.PVTLimit.UpperTemLimit[2].FallEdgeLimit[0].StaticLimt.Start.LevelRel = 6.5;
    gsm.LimitInfo.PVTLimit.UpperTemLimit[2].FallEdgeLimit[0].StaticLimt.Stop.LevelRel = 6.5;

    // LimitInfo.SpecModLimit
    gsm.LimitInfo.SpecModLimit[0].RefPwrLimit.LowPwr = 33;
    gsm.LimitInfo.SpecModLimit[0].RefPwrLimit.HighPwr = 39;
    for (int i = 0; i < 10; i++)
    {
        gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[i].State = 1;
    }
    for (int i = 0; i < arraySize(gsm.LimitInfo.SpecModLimit); i++)
    {
        for (int j = 4; j < arraySize(gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit); j++)
        {
            gsm.LimitInfo.SpecModLimit[i].FreOffsetLimit[j].LowPwrRel = -60;
            gsm.LimitInfo.SpecModLimit[i].FreOffsetLimit[j].HighPwrRel = -66;
            gsm.LimitInfo.SpecModLimit[i].FreOffsetLimit[j].AbsPwr = -51;
        }
    }
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[0].LowPwrRel = 0.5;
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[1].LowPwrRel = -30;
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[2].LowPwrRel = -33;
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[3].LowPwrRel = -54;
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[0].HighPwrRel = 0.5;
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[1].HighPwrRel = -30;
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[2].HighPwrRel = -33;
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[3].HighPwrRel = -54;
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[0].AbsPwr = -36;
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[1].AbsPwr = -36;
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[2].AbsPwr = -36;
    gsm.LimitInfo.SpecModLimit[0].FreOffsetLimit[3].AbsPwr = -36;

    gsm.LimitInfo.SpecModLimit[1].RefPwrLimit.LowPwr = 33;
    gsm.LimitInfo.SpecModLimit[1].RefPwrLimit.HighPwr = 34;
    gsm.LimitInfo.SpecModLimit[1].FreOffsetLimit[3].LowPwrRel = -54;
    gsm.LimitInfo.SpecModLimit[1].FreOffsetLimit[3].HighPwrRel = -54;
    gsm.LimitInfo.SpecModLimit[2].RefPwrLimit.LowPwr = 33;
    gsm.LimitInfo.SpecModLimit[2].RefPwrLimit.HighPwr = 34;
    gsm.LimitInfo.SpecModLimit[2].FreOffsetLimit[3].LowPwrRel = -54;
    gsm.LimitInfo.SpecModLimit[2].FreOffsetLimit[3].HighPwrRel = -54;

    // LimitInfo.SpecSwiLimit
    for (int i = 0; i < arraySize(gsm.LimitInfo.SpecSwiLimit); i++)
    {
        for (int j = 0; j < arraySize(gsm.LimitInfo.SpecSwiLimit[0].RefPower); j++)
        {
            gsm.LimitInfo.SpecSwiLimit[i].RefPower[j].State = 1;
        }

        gsm.LimitInfo.SpecSwiLimit[i].RefPower[0].Limit = 39;
        gsm.LimitInfo.SpecSwiLimit[i].RefPower[1].Limit = 37;
        gsm.LimitInfo.SpecSwiLimit[i].RefPower[2].Limit = 35;
        gsm.LimitInfo.SpecSwiLimit[i].RefPower[3].Limit = 33;
        gsm.LimitInfo.SpecSwiLimit[i].RefPower[4].Limit = 31;
        gsm.LimitInfo.SpecSwiLimit[i].RefPower[5].Limit = 29;
        gsm.LimitInfo.SpecSwiLimit[i].RefPower[6].Limit = 27;
        gsm.LimitInfo.SpecSwiLimit[i].RefPower[7].Limit = 25;
        gsm.LimitInfo.SpecSwiLimit[i].RefPower[8].Limit = 23;
        gsm.LimitInfo.SpecSwiLimit[i].RefPower[9].Limit = 21;

        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].State = 1;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[0] = -13;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[1] = -15;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[2] = -17;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[3] = -19;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[4] = -21;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[5] = -23;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[6] = -23;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[7] = -23;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[8] = -23;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[0].LimitValue[9] = -23;

        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].State = 1;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[0] = -21;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[1] = -21;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[2] = -21;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[3] = -21;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[4] = -23;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[5] = -25;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[6] = -26;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[7] = -26;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[8] = -26;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[1].LimitValue[9] = -26;

        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].State = 1;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[0] = -21;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[1] = -21;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[2] = -21;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[3] = -21;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[4] = -23;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[5] = -25;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[6] = -27;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[7] = -29;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[8] = -31;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[2].LimitValue[9] = -32;

        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].State = 1;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[0] = -24;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[1] = -24;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[2] = -24;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[3] = -24;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[4] = -26;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[5] = -28;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[6] = -30;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[7] = -32;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[8] = -34;
        gsm.LimitInfo.SpecSwiLimit[i].FreOffsetLimit[3].LimitValue[9] = -36;
    }
}

void cellular::alz::WcdmaAlzParam::SetDefaultParam(AlzParam3GPP &param, int linkDirect)
{
    auto &wcdma = param.WCDMA;
    memset(&wcdma, 0, sizeof(wcdma));
    wcdma.LinkDirect = linkDirect;

    if (linkDirect == ALG_3GPP_UL) {
        SetWcdmaULParams(wcdma.UL);
    } else {
        SetWcdmaDLParams(wcdma.DL);
    }

    SetWcdmaMeasureParams(wcdma.Measure);
}

void cellular::alz::LteAlzParam::SetDefaultParam(AlzParam3GPP &param, int linkDirect)
{
    auto &lte = param.LTE;
    auto &LteList = param.LTELIST;

    memset(&LteList, 0, sizeof(LteList));
    LteList.ChannelBW = 1400000;
    LteList.RBAutoMode = 1;
    LteList.RBNum = 100;
    LteList.Measure.ModStatNum = 20;
    LteList.Measure.SEMStatNum = 20;
    LteList.Measure.ACLRStatNum = 20;
    LteList.Measure.PowerStatNum = 20;

    memset(&lte, 0, sizeof(lte));

    lte.LinkDirect = linkDirect;
    if (linkDirect == ALG_3GPP_UL) {
        SetLteULParams(lte.Pusch);
    } else {
        SetLteDLParams(lte.Pdsch);
        lte.ChanType = ALG_4G_PDSCH;
    }

    lte.Cell[0].State = 1;
    for (int i = 0; i < ALG_4G_MAX_CELL_NUM; i++)
    {
        lte.Cell[i].CellIdx = i;
        lte.Cell[i].ChannelBW = 20 * MHz;
    }
    lte.NSValue = 1;
    lte.MeasInfo.MeasSubfrmCount = 1;
    lte.MeasInfo.MeasSlotType = 2;
    lte.MeasInfo.Modulate.TxMeasureEnable = 1;
    lte.MeasInfo.Modulate.EvmEnable = 1;
    lte.MeasInfo.Modulate.IBEEnable = 1;
    lte.MeasInfo.Modulate.ESFlatEnable = 1;
    lte.MeasInfo.Modulate.IQConstelEnable = 1;
    lte.MeasInfo.Modulate.ModStatNum = 1;
    lte.MeasInfo.Modulate.ModEnable = 1;
    lte.MeasInfo.Spectrum.ACLREnable = 1;
    lte.MeasInfo.Spectrum.SpectEnable = 1;
    lte.MeasInfo.Spectrum.OBWEnable = 1;
    lte.MeasInfo.Spectrum.SEMEnable = 1;
    lte.MeasInfo.Spectrum.SEMStatNum = 1;
    lte.MeasInfo.Spectrum.ACLRStatNum = 1;
    lte.MeasInfo.Spectrum.UTRA1Enable = 1;
    lte.MeasInfo.Spectrum.UTRA2Enable = 1;
    lte.MeasInfo.Spectrum.EUTRA1Enable = 1;
    lte.MeasInfo.Power.PowerStatNum = 1;
    SetLteLimitParams(lte.LimitInfo);
}

void cellular::alz::NrAlzParam::SetDefaultParam(AlzParam3GPP &param, int linkDirect)
{
    const bool isSpectrumRbwInvalid = (param.SpectrumRBW < 1000) ||
                                        (param.SpectrumRBW > 500000) ||
                                        (param.SpectrumRBW % 10 != 0);
    param.SpectrumRBW = isSpectrumRbwInvalid ? 120 * KHz : param.SpectrumRBW;
    
    auto &nr = param.NR;
    auto &nr_list = param.NR5GLIST;
    memset(&nr_list, 0, sizeof(nr_list));
    memset(&nr, 0, sizeof(nr));
    
    nr_list.Version = 17;
    nr_list.ChannelBW = 20 * MHz;
    nr_list.NSValue = 1;
    nr_list.DmrsTypeAPos = 2;
    nr_list.UseSCSpacing = 30 * KHz;
    nr_list.SCSpacing = 30 * KHz;
    nr_list.BwpRBNum = 51;
    nr_list.ConfigType[0] = 1;
    nr_list.ConfigType[1] = 1;
    nr_list.MaxLength[0] = 1;
    nr_list.MaxLength[1] = 1;
    nr_list.AdditionalPos[0] = 2;
    nr_list.AdditionalPos[1] = 2;
    nr_list.SymNum = 14;
    nr_list.RBAutoMode = 1;
    nr_list.RBNum = 100;
    nr_list.DmrsSymbLen = 1;
    nr_list.Measure.ModStatNum = 20;
    nr_list.Measure.SEMStatNum = 20;
    nr_list.Measure.ACLRStatNum = 20;
    nr_list.Measure.TxPwrStatNum = 20;
    
    nr.LinkDirect = linkDirect;
    nr.Version = 17;

    nr.ViewSet.EvmEnable = 1;
    nr.ViewSet.IBEEnable = 1;
    nr.ViewSet.ESFlatEnable = 1;
    nr.ViewSet.IQConstelEnable = 1;
    nr.ViewSet.SpectEnable = 1;
    nr.ViewSet.ACLREnable = 1;
    nr.ViewSet.TxMeasureEnable = 1;
    nr.ViewSet.ModEnable = 1;
    nr.ViewSet.OBWEnable = 1;
    nr.ViewSet.SEMEnable = 1;

    if (linkDirect == ALG_3GPP_UL) {
        SetNrULParams(nr.UL);
    } else {
        SetNrDLParams(nr.DL);
    }
    
    nr.Measure.MeasSFNum = 1;
    nr.Measure.MeasAllEnable = 1;
    nr.Measure.ModStatNum = 1;
    nr.Measure.SEMStatNum = 1;
    nr.Measure.ACLRStatNum = 1;
    nr.Measure.UTRAEnable[0] = 1;
    nr.Measure.UTRAEnable[1] = 1;
    nr.Measure.UTRAEnable[2] = 1;
    nr.Measure.PwrDynStatNum = 1;
    nr.Measure.TxPwrStatNum = 1;
    
    SetNrLimitParams(nr.LimitInfo);
}

void cellular::alz::NbiotAlzParam::SetDefaultParam(AlzParam3GPP &param, int linkDirect)
{
    const bool isSpectrumRbwInvalid = (param.SpectrumRBW < 1000) ||
                                        (param.SpectrumRBW > 500000) ||
                                        (param.SpectrumRBW % 10 != 0);
    param.SpectrumRBW = isSpectrumRbwInvalid ? 30 * KHz : param.SpectrumRBW;

    auto &nbiot = param.NBIOT;
    memset(&nbiot, 0, sizeof(nbiot));
    nbiot.LinkDirect = linkDirect;

    if (linkDirect == ALG_3GPP_UL) {
        SetNbiotULParams(nbiot.UL);
    } else {
        SetNbiotDLParams(nbiot.DL);
    }
    
    SetNbiotLimitParams(nbiot.LimitInfo);

}

void cellular::alz::WcdmaAlzParam::SetWcdmaULParams(Alg_3GPP_AlzULInWCDMA &wcdma_ul)
{
    wcdma_ul.DPDCHAvailable = 1;
    wcdma_ul.MeasureLen = 1;
    wcdma_ul.SyncSlotId = -1;
    wcdma_ul.CDPSpreadFactor = 4;
}

void cellular::alz::WcdmaAlzParam::SetWcdmaDLParams(Alg_3GPP_AlzDLInWCDMA &wcdma_dl)
{
    wcdma_dl.DPCHNum = 1;
    wcdma_dl.DPCH[0].State = 1;
    wcdma_dl.DPCH[0].ChanCode = 3;
    wcdma_dl.DPCH[0].DCH.State = 1;
    wcdma_dl.DPCH[0].DCH.DTCH[0].State = 1;
    wcdma_dl.DPCH[0].DCH.DTCH[0].TTI = 10;
    for(int i = 0; i < arraySize(wcdma_dl.DPCH); i++) 
    {
        wcdma_dl.DPCH[i].SlotFormat = 2;
        wcdma_dl.DPCH[i].SymbRate = 15000;
        wcdma_dl.DPCH[i].DCH.Interleaver2Stat = 1;
        wcdma_dl.DPCH[i].DCH.DCCH.TbCount = 1;
        wcdma_dl.DPCH[i].DCH.DCCH.TbSize = 100;
        wcdma_dl.DPCH[i].DCH.DCCH.Crc = 16;
        wcdma_dl.DPCH[i].DCH.DCCH.RmAttribute = 1;
        wcdma_dl.DPCH[i].DCH.DCCH.EProtection = 3;
        wcdma_dl.DPCH[i].DCH.DCCH.InterleaverStat = 1;
        for(int j = 0; j < arraySize(wcdma_dl.DPCH[i].DCH.DTCH); j++)
        {
            wcdma_dl.DPCH[i].DCH.DTCH[j].TbCount = 1;
            wcdma_dl.DPCH[i].DCH.DTCH[j].TbSize = 100;
            wcdma_dl.DPCH[i].DCH.DTCH[j].Crc = 16;
            wcdma_dl.DPCH[i].DCH.DTCH[j].RmAttribute = 1;
            wcdma_dl.DPCH[i].DCH.DTCH[j].EProtection = 3;
            wcdma_dl.DPCH[i].DCH.DTCH[j].InterleaverStat = 1;
        }
    }
}

void cellular::alz::WcdmaAlzParam::SetWcdmaMeasureParams(Alg_3GPP_AlzMeasureWCDMA &wcdma_measure)
{
    wcdma_measure.PeakMagnErrLimitMode = 1;
    wcdma_measure.PeakMagnErrLimit = 50.0;
    wcdma_measure.RmsMagnErrLimitMode = 1;
    wcdma_measure.RmsMagnErrLimit = 17.5;
    wcdma_measure.PeakEvmLimitMode = 1;
    wcdma_measure.PeakEvmLimit = 50.0;
    wcdma_measure.RmsEvmLimitMode = 1;
    wcdma_measure.RmsEvmLimit = 50.0;
    wcdma_measure.PeakPhaseErrLimitMode = 1;
    wcdma_measure.PeakPhaseErrLimit = 45.0;
    wcdma_measure.RmsPhaseErrLimitMode = 1;
    wcdma_measure.RmsPhaseErrLimit = 10.0;
    wcdma_measure.CFErrLimitMode = 1;
    wcdma_measure.CFErrLimit = 200;
    wcdma_measure.PhaseDisLimitMode = 1;
    wcdma_measure.UpperLimit = 66.0;
    wcdma_measure.DynamicLimit = 36.0;
    wcdma_measure.AclrLimit1Mode = 1;
    wcdma_measure.UtraLimit1 = -32.2;
    wcdma_measure.AclrLimit2Mode = 1;
    wcdma_measure.UtraLimit2 = -42.2;
    wcdma_measure.SEMLimitADMode = 1;
    wcdma_measure.LimitAD[0] = -47.5;
    wcdma_measure.LimitAD[1] = -47.5;
    wcdma_measure.LimitAD[2] = -37.5;
    wcdma_measure.LimitAD[3] = -33.5;
    wcdma_measure.SEMLimitEFMode = 1;
    wcdma_measure.SEMAbsLimitG[0] = -48.5;
    wcdma_measure.SEMAbsLimitH[0] = -13;
    wcdma_measure.SEMAbsLimitH[1] = -15;
    wcdma_measure.LimitEF[0] = -48.5;
    wcdma_measure.LimitEF[1] = -33.5;
    wcdma_measure.AclrAbsLimitMode = 1;
    wcdma_measure.AbsLimit = -50.0;
}

void cellular::alz::LteAlzParam::SetLteULParams(Alg_3GPP_AlzPusch4g *lte_pusch)
{
    for (int i = 0; i < ALG_4G_MAX_CELL_NUM; i++)
    {
        lte_pusch[i].CellIdx = i;
        lte_pusch[i].State = 1;
        lte_pusch[i].RBDetMode = 1;
        lte_pusch[i].RBNum = 100;
        lte_pusch[i].LayerNum = 1;
        lte_pusch[i].AntennaNum = 1;
        lte_pusch[i].Codeword = 1;
        for (int j = 0; j < arraySize(lte_pusch[i].PayloadSize); ++j)
        {
            lte_pusch[i].PayloadSize[j] = 1500;
        }
    }
}

void cellular::alz::LteAlzParam::SetLteDLParams(Alg_3GPP_AlzPdsch4g &lte_pdsch)
{
    lte_pdsch.ResAllocateType = 2;
    lte_pdsch.RBNum = 100;
    lte_pdsch.LayerNum = 1;
    lte_pdsch.AntennaNum = 1;
    lte_pdsch.Codeword = 1;
    for (int i = 0; i < arraySize(lte_pdsch.Modulate); ++i)
    {
        lte_pdsch.Modulate[i] = 2;
    }
    lte_pdsch.ChanCodingState = 1;
    lte_pdsch.Scramble = 1;
    lte_pdsch.McsTable = 1;
    lte_pdsch.TxMode = 1;
    lte_pdsch.UECategory = 1;
    for (int i = 0; i < arraySize(lte_pdsch.PayloadSize); ++i)
    {
        lte_pdsch.PayloadSize[i] = 1500;
    }
    
    for (int i = 0; i < arraySize(lte_pdsch.NIR); ++i)
    {
        lte_pdsch.NIR[i] = 3667200;
    }
    
    for (int i = 0; i < arraySize(lte_pdsch.SoftChanBit); ++i)
    {
        lte_pdsch.SoftChanBit[i] = 58675200;
    }
}

void cellular::alz::LteAlzParam::SetLteLimitParams(Alg_3GPP_LimitIn4g &lte_limit)
{
    // ModLimit
    {
        lte_limit.ModLimit[0].EvmRms.State = 1;
        lte_limit.ModLimit[0].EvmRms.Limit = 17.5;
        lte_limit.ModLimit[0].EvmPeak.Limit = 35.0;
        lte_limit.ModLimit[0].MErrRms.Limit = 17.5;
        lte_limit.ModLimit[0].MErrPeak.Limit = 35.0;
        lte_limit.ModLimit[0].PhErrRms.Limit = 17.5;
        lte_limit.ModLimit[0].PhErrPeak.Limit = 35.0;
        lte_limit.ModLimit[0].FreqErr.State = 1;
        lte_limit.ModLimit[0].FreqErr.Limit = 0.1;
        lte_limit.ModLimit[0].IQOffset.State = 1;
        lte_limit.ModLimit[0].IQOffset.PwrLimit[0] = -24.2;
        lte_limit.ModLimit[0].IQOffset.PwrLimit[1] = -19.2;
        lte_limit.ModLimit[0].IQOffset.PwrLimit[2] = -9.2;
        lte_limit.ModLimit[0].IBE.State = 1;
        lte_limit.ModLimit[0].IBE.GenMin = -29.2;
        lte_limit.ModLimit[0].IBE.GenEVM = 17.5;
        lte_limit.ModLimit[0].IBE.GenPwr = -57.0;
        lte_limit.ModLimit[0].IBE.IQImage[0] = -24.2;
        lte_limit.ModLimit[0].IBE.IQOffsetPwr[0] = -24.2;
        lte_limit.ModLimit[0].IBE.IQOffsetPwr[1] = -19.2;
        lte_limit.ModLimit[0].IBE.IQOffsetPwr[2] = -9.2;
        lte_limit.ModLimit[0].SpectFlat.State = 1;
        lte_limit.ModLimit[0].SpectFlat.Range1 = 5.4;
        lte_limit.ModLimit[0].SpectFlat.Range2 = 9.4;
        lte_limit.ModLimit[0].SpectFlat.Max1Min2 = 6.4;
        lte_limit.ModLimit[0].SpectFlat.Max2Min1 = 8.4;
        lte_limit.ModLimit[0].SpectFlat.EdgeFreq = 3.0;

        lte_limit.ModLimit[1].EvmRms.State = 1;
        lte_limit.ModLimit[1].EvmRms.Limit = 12.5;
        lte_limit.ModLimit[1].EvmPeak.Limit = 25.0;
        lte_limit.ModLimit[1].MErrRms.Limit = 12.5;
        lte_limit.ModLimit[1].MErrPeak.Limit = 25.0;
        lte_limit.ModLimit[1].PhErrRms.Limit = 12.5;
        lte_limit.ModLimit[1].PhErrPeak.Limit = 25.0;
        lte_limit.ModLimit[1].FreqErr.State = 1;
        lte_limit.ModLimit[1].FreqErr.Limit = 0.1;
        lte_limit.ModLimit[1].IQOffset.State = 1;
        lte_limit.ModLimit[1].IQOffset.PwrLimit[0] = -24.2;
        lte_limit.ModLimit[1].IQOffset.PwrLimit[1] = -19.2;
        lte_limit.ModLimit[1].IQOffset.PwrLimit[2] = -9.2;
        lte_limit.ModLimit[1].IBE.State = 1;
        lte_limit.ModLimit[1].IBE.GenMin = -29.2;
        lte_limit.ModLimit[1].IBE.GenEVM = 12.5;
        lte_limit.ModLimit[1].IBE.GenPwr = -57.0;
        lte_limit.ModLimit[1].IBE.IQImage[0] = -24.2;
        lte_limit.ModLimit[1].IBE.IQOffsetPwr[0] = -24.2;
        lte_limit.ModLimit[1].IBE.IQOffsetPwr[1] = -19.2;
        lte_limit.ModLimit[1].IBE.IQOffsetPwr[2] = -9.2;
        lte_limit.ModLimit[1].SpectFlat.State = 1;
        lte_limit.ModLimit[1].SpectFlat.Range1 = 5.4;
        lte_limit.ModLimit[1].SpectFlat.Range2 = 9.4;
        lte_limit.ModLimit[1].SpectFlat.Max1Min2 = 6.4;
        lte_limit.ModLimit[1].SpectFlat.Max2Min1 = 8.4;
        lte_limit.ModLimit[1].SpectFlat.EdgeFreq = 3.0;

        lte_limit.ModLimit[2].EvmRms.State = 1;
        lte_limit.ModLimit[2].EvmRms.Limit = 8.0;
        lte_limit.ModLimit[2].EvmPeak.Limit = 15.0;
        lte_limit.ModLimit[2].MErrRms.Limit = 8.0;
        lte_limit.ModLimit[2].MErrPeak.Limit = 15.0;
        lte_limit.ModLimit[2].PhErrRms.Limit = 8.0;
        lte_limit.ModLimit[2].PhErrPeak.Limit = 15.0;
        lte_limit.ModLimit[2].FreqErr.State = 1;
        lte_limit.ModLimit[2].FreqErr.Limit = 0.1;
        lte_limit.ModLimit[2].IQOffset.State = 1;
        lte_limit.ModLimit[2].IQOffset.PwrLimit[0] = -24.2;
        lte_limit.ModLimit[2].IQOffset.PwrLimit[1] = -19.2;
        lte_limit.ModLimit[2].IQOffset.PwrLimit[2] = -9.2;
        lte_limit.ModLimit[2].IBE.State = 1;
        lte_limit.ModLimit[2].IBE.GenMin = -29.2;
        lte_limit.ModLimit[2].IBE.GenEVM = 8.0;
        lte_limit.ModLimit[2].IBE.GenPwr = -57.0;
        lte_limit.ModLimit[2].IBE.IQImage[0] = -24.2;
        lte_limit.ModLimit[2].IBE.IQOffsetPwr[0] = -24.2;
        lte_limit.ModLimit[2].IBE.IQOffsetPwr[1] = -19.2;
        lte_limit.ModLimit[2].IBE.IQOffsetPwr[2] = -9.2;
        lte_limit.ModLimit[2].SpectFlat.State = 1;
        lte_limit.ModLimit[2].SpectFlat.Range1 = 5.4;
        lte_limit.ModLimit[2].SpectFlat.Range2 = 9.4;
        lte_limit.ModLimit[2].SpectFlat.Max1Min2 = 6.4;
        lte_limit.ModLimit[2].SpectFlat.Max2Min1 = 8.4;
        lte_limit.ModLimit[2].SpectFlat.EdgeFreq = 3.0;

        lte_limit.ModLimit[3].EvmRms.State = 1;
        lte_limit.ModLimit[3].EvmRms.Limit = 3.5;
        lte_limit.ModLimit[3].EvmPeak.Limit = 7.0;
        lte_limit.ModLimit[3].MErrRms.Limit = 3.5;
        lte_limit.ModLimit[3].MErrPeak.Limit = 7.0;
        lte_limit.ModLimit[3].PhErrRms.Limit = 3.5;
        lte_limit.ModLimit[3].PhErrPeak.Limit = 7.0;
        lte_limit.ModLimit[3].FreqErr.State = 1;
        lte_limit.ModLimit[3].FreqErr.Limit = 0.1;
        lte_limit.ModLimit[3].IQOffset.State = 1;
        lte_limit.ModLimit[3].IQOffset.PwrLimit[0] = -24.2;
        lte_limit.ModLimit[3].IQOffset.PwrLimit[1] = -19.2;
        lte_limit.ModLimit[3].IQOffset.PwrLimit[2] = -9.2;
        lte_limit.ModLimit[3].IBE.State = 1;
        lte_limit.ModLimit[3].IBE.GenMin = -29.2;
        lte_limit.ModLimit[3].IBE.GenEVM = 3.5;
        lte_limit.ModLimit[3].IBE.GenPwr = -57.0;
        lte_limit.ModLimit[3].IBE.IQImage[0] = -24.2;
        lte_limit.ModLimit[3].IBE.IQOffsetPwr[0] = -24.2;
        lte_limit.ModLimit[3].IBE.IQOffsetPwr[1] = -19.2;
        lte_limit.ModLimit[3].IBE.IQOffsetPwr[2] = -9.2;
        lte_limit.ModLimit[3].SpectFlat.State = 1;
        lte_limit.ModLimit[3].SpectFlat.Range1 = 5.4;
        lte_limit.ModLimit[3].SpectFlat.Range2 = 9.4;
        lte_limit.ModLimit[3].SpectFlat.Max1Min2 = 6.4;
        lte_limit.ModLimit[3].SpectFlat.Max2Min1 = 8.4;
        lte_limit.ModLimit[3].SpectFlat.EdgeFreq = 3.0;
    }

    // SpectLimit
    {
        // OBWLimit
        {
            lte_limit.SpectLimit[0].OBWLimit.Limit = 1.4;
            lte_limit.SpectLimit[1].OBWLimit.Limit = 3.0;
            lte_limit.SpectLimit[2].OBWLimit.Limit = 5.0;
            lte_limit.SpectLimit[3].OBWLimit.Limit = 10.0;
            lte_limit.SpectLimit[4].OBWLimit.Limit = 15.0;
            lte_limit.SpectLimit[5].OBWLimit.Limit = 20.0;
        }

        // SEMLimit
        {
            // 1.4MHz
            {
                lte_limit.SpectLimit[0].SEMLimit[0][0].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[0][1].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[0][2].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[0][0].StartFreq = 0.015;
                lte_limit.SpectLimit[0].SEMLimit[0][1].StartFreq = 1.5;
                lte_limit.SpectLimit[0].SEMLimit[0][2].StartFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[0][0].StopFreq = 0.985;
                lte_limit.SpectLimit[0].SEMLimit[0][1].StopFreq = 2.0;
                lte_limit.SpectLimit[0].SEMLimit[0][2].StopFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[0][0].LimitPower = -8.5;
                lte_limit.SpectLimit[0].SEMLimit[0][1].LimitPower = -8.5;
                lte_limit.SpectLimit[0].SEMLimit[0][2].LimitPower = -23.5;
                lte_limit.SpectLimit[0].SEMLimit[0][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[0].SEMLimit[0][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[0].SEMLimit[0][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[0].SEMLimit[0]); i++)
                {
                    lte_limit.SpectLimit[0].SEMLimit[0][i].StartFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[0][i].StopFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[0][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[0].SEMLimit[0][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[0].SEMLimit[1][0].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[1][1].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[1][2].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[1][0].StartFreq = 0.015;
                lte_limit.SpectLimit[0].SEMLimit[1][1].StartFreq = 1.5;
                lte_limit.SpectLimit[0].SEMLimit[1][2].StartFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[1][0].StopFreq = 0.985;
                lte_limit.SpectLimit[0].SEMLimit[1][1].StopFreq = 2.0;
                lte_limit.SpectLimit[0].SEMLimit[1][2].StopFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[1][0].LimitPower = -8.5;
                lte_limit.SpectLimit[0].SEMLimit[1][1].LimitPower = -11.5;
                lte_limit.SpectLimit[0].SEMLimit[1][2].LimitPower = -23.5;
                lte_limit.SpectLimit[0].SEMLimit[1][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[0].SEMLimit[1][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[0].SEMLimit[1][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[0].SEMLimit[1]); i++)
                {
                    lte_limit.SpectLimit[0].SEMLimit[1][i].StartFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[1][i].StopFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[1][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[0].SEMLimit[1][i].RBW = 1 * MHz;
                }
                
                lte_limit.SpectLimit[0].SEMLimit[2][0].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[2][1].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[2][2].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[2][0].StartFreq = 0.015;
                lte_limit.SpectLimit[0].SEMLimit[2][1].StartFreq = 1.5;
                lte_limit.SpectLimit[0].SEMLimit[2][2].StartFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[2][0].StopFreq = 0.985;
                lte_limit.SpectLimit[0].SEMLimit[2][1].StopFreq = 2.0;
                lte_limit.SpectLimit[0].SEMLimit[2][2].StopFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[2][0].LimitPower = -8.5;
                lte_limit.SpectLimit[0].SEMLimit[2][1].LimitPower = -11.5;
                lte_limit.SpectLimit[0].SEMLimit[2][2].LimitPower = -23.5;
                lte_limit.SpectLimit[0].SEMLimit[2][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[0].SEMLimit[2][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[0].SEMLimit[2][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[0].SEMLimit[2]); i++)
                {
                    lte_limit.SpectLimit[0].SEMLimit[2][i].StartFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[2][i].StopFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[2][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[0].SEMLimit[2][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[0].SEMLimit[3][0].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[3][1].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[3][2].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[3][3].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[3][0].StartFreq = 0.015;
                lte_limit.SpectLimit[0].SEMLimit[3][1].StartFreq = 0.15;
                lte_limit.SpectLimit[0].SEMLimit[3][2].StartFreq = 1.5;
                lte_limit.SpectLimit[0].SEMLimit[3][3].StartFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[3][0].StopFreq = 0.085;
                lte_limit.SpectLimit[0].SEMLimit[3][1].StopFreq = 0.95;
                lte_limit.SpectLimit[0].SEMLimit[3][2].StopFreq = 2.0;
                lte_limit.SpectLimit[0].SEMLimit[3][3].StopFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[3][0].LimitPower = -11.5;
                lte_limit.SpectLimit[0].SEMLimit[3][1].LimitPower = -11.5;
                lte_limit.SpectLimit[0].SEMLimit[3][2].LimitPower = -11.5;
                lte_limit.SpectLimit[0].SEMLimit[3][3].LimitPower = -23.5;
                lte_limit.SpectLimit[0].SEMLimit[3][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[0].SEMLimit[3][1].RBW = 100 * KHz;
                lte_limit.SpectLimit[0].SEMLimit[3][2].RBW = 1 * MHz;
                lte_limit.SpectLimit[0].SEMLimit[3][3].RBW = 1 * MHz;
                for (int i = 4; i < arraySize(lte_limit.SpectLimit[0].SEMLimit[3]); i++)
                {
                    lte_limit.SpectLimit[0].SEMLimit[3][i].StartFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[3][i].StopFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[3][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[0].SEMLimit[3][i].RBW = 1 * MHz;
                }
                
                lte_limit.SpectLimit[0].SEMLimit[4][0].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[4][1].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[4][2].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[4][0].StartFreq = 0.015;
                lte_limit.SpectLimit[0].SEMLimit[4][1].StartFreq = 1.5;
                lte_limit.SpectLimit[0].SEMLimit[4][2].StartFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[4][0].StopFreq = 0.985;
                lte_limit.SpectLimit[0].SEMLimit[4][1].StopFreq = 2.0;
                lte_limit.SpectLimit[0].SEMLimit[4][2].StopFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[4][0].LimitPower = -11.5;
                lte_limit.SpectLimit[0].SEMLimit[4][1].LimitPower = -11.5;
                lte_limit.SpectLimit[0].SEMLimit[4][2].LimitPower = -23.5;
                lte_limit.SpectLimit[0].SEMLimit[4][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[0].SEMLimit[4][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[0].SEMLimit[4][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[0].SEMLimit[4]); i++)
                {
                    lte_limit.SpectLimit[0].SEMLimit[4][i].StartFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[4][i].StopFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[4][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[0].SEMLimit[4][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[0].SEMLimit[5][0].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[5][1].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[5][2].State = 1;
                lte_limit.SpectLimit[0].SEMLimit[5][0].StartFreq = 0.015;
                lte_limit.SpectLimit[0].SEMLimit[5][1].StartFreq = 1.5;
                lte_limit.SpectLimit[0].SEMLimit[5][2].StartFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[5][0].StopFreq = 0.985;
                lte_limit.SpectLimit[0].SEMLimit[5][1].StopFreq = 2.0;
                lte_limit.SpectLimit[0].SEMLimit[5][2].StopFreq = 3.0;
                lte_limit.SpectLimit[0].SEMLimit[5][0].LimitPower = -13.5;
                lte_limit.SpectLimit[0].SEMLimit[5][1].LimitPower = -11.5;
                lte_limit.SpectLimit[0].SEMLimit[5][2].LimitPower = -23.5;
                lte_limit.SpectLimit[0].SEMLimit[5][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[0].SEMLimit[5][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[0].SEMLimit[5][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[0].SEMLimit[5]); i++)
                {
                    lte_limit.SpectLimit[0].SEMLimit[5][i].StartFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[5][i].StopFreq = 5.0;
                    lte_limit.SpectLimit[0].SEMLimit[5][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[0].SEMLimit[5][i].RBW = 1 * MHz;
                }
            }

            // 3MHz
            {
                lte_limit.SpectLimit[1].SEMLimit[0][0].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[0][1].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[0][2].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[0][0].StartFreq = 0.015;
                lte_limit.SpectLimit[1].SEMLimit[0][1].StartFreq = 1.5;
                lte_limit.SpectLimit[1].SEMLimit[0][2].StartFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[0][0].StopFreq = 0.985;
                lte_limit.SpectLimit[1].SEMLimit[0][1].StopFreq = 4.5;
                lte_limit.SpectLimit[1].SEMLimit[0][2].StopFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[0][0].LimitPower = -11.5;
                lte_limit.SpectLimit[1].SEMLimit[0][1].LimitPower = -8.5;
                lte_limit.SpectLimit[1].SEMLimit[0][2].LimitPower = -23.5;
                lte_limit.SpectLimit[1].SEMLimit[0][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[1].SEMLimit[0][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[1].SEMLimit[0][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[1].SEMLimit[0]); i++)
                {
                    lte_limit.SpectLimit[1].SEMLimit[0][i].StartFreq = 6.0;
                    lte_limit.SpectLimit[1].SEMLimit[0][i].StopFreq = 6.0;
                    lte_limit.SpectLimit[1].SEMLimit[0][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[1].SEMLimit[0][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[1].SEMLimit[1][0].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[1][1].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[1][2].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[1][0].StartFreq = 0.015;
                lte_limit.SpectLimit[1].SEMLimit[1][1].StartFreq = 1.5;
                lte_limit.SpectLimit[1].SEMLimit[1][2].StartFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[1][0].StopFreq = 0.985;
                lte_limit.SpectLimit[1].SEMLimit[1][1].StopFreq = 4.5;
                lte_limit.SpectLimit[1].SEMLimit[1][2].StopFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[1][0].LimitPower = -11.5;
                lte_limit.SpectLimit[1].SEMLimit[1][1].LimitPower = -11.5;
                lte_limit.SpectLimit[1].SEMLimit[1][2].LimitPower = -23.5;
                lte_limit.SpectLimit[1].SEMLimit[1][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[1].SEMLimit[1][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[1].SEMLimit[1][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[1].SEMLimit[1]); i++)
                {
                    lte_limit.SpectLimit[1].SEMLimit[1][i].StartFreq = 6.0;
                    lte_limit.SpectLimit[1].SEMLimit[1][i].StopFreq = 6.0;
                    lte_limit.SpectLimit[1].SEMLimit[1][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[1].SEMLimit[1][i].RBW = 1 * MHz;
                }
                
                lte_limit.SpectLimit[1].SEMLimit[2][0].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[2][1].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[2][2].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[2][0].StartFreq = 0.015;
                lte_limit.SpectLimit[1].SEMLimit[2][1].StartFreq = 1.5;
                lte_limit.SpectLimit[1].SEMLimit[2][2].StartFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[2][0].StopFreq = 0.985;
                lte_limit.SpectLimit[1].SEMLimit[2][1].StopFreq = 4.5;
                lte_limit.SpectLimit[1].SEMLimit[2][2].StopFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[2][0].LimitPower = -11.5;
                lte_limit.SpectLimit[1].SEMLimit[2][1].LimitPower = -11.5;
                lte_limit.SpectLimit[1].SEMLimit[2][2].LimitPower = -23.5;
                lte_limit.SpectLimit[1].SEMLimit[2][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[1].SEMLimit[2][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[1].SEMLimit[2][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[1].SEMLimit[2]); i++)
                {
                    lte_limit.SpectLimit[1].SEMLimit[2][i].StartFreq = 5.0;
                    lte_limit.SpectLimit[1].SEMLimit[2][i].StopFreq = 5.0;
                    lte_limit.SpectLimit[1].SEMLimit[2][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[1].SEMLimit[2][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[1].SEMLimit[3][0].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[3][1].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[3][2].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[3][3].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[3][0].StartFreq = 0.015;
                lte_limit.SpectLimit[1].SEMLimit[3][1].StartFreq = 0.15;
                lte_limit.SpectLimit[1].SEMLimit[3][2].StartFreq = 1.5;
                lte_limit.SpectLimit[1].SEMLimit[3][3].StartFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[3][0].StopFreq = 0.085;
                lte_limit.SpectLimit[1].SEMLimit[3][1].StopFreq = 0.95;
                lte_limit.SpectLimit[1].SEMLimit[3][2].StopFreq = 4.5;
                lte_limit.SpectLimit[1].SEMLimit[3][3].StopFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[3][0].LimitPower = -11.5;
                lte_limit.SpectLimit[1].SEMLimit[3][1].LimitPower = -11.5;
                lte_limit.SpectLimit[1].SEMLimit[3][2].LimitPower = -11.5;
                lte_limit.SpectLimit[1].SEMLimit[3][3].LimitPower = -23.5;
                lte_limit.SpectLimit[1].SEMLimit[3][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[1].SEMLimit[3][1].RBW = 100 * KHz;
                lte_limit.SpectLimit[1].SEMLimit[3][2].RBW = 1 * MHz;
                lte_limit.SpectLimit[1].SEMLimit[3][3].RBW = 1 * MHz;
                for (int i = 4; i < arraySize(lte_limit.SpectLimit[1].SEMLimit[3]); i++)
                {
                    lte_limit.SpectLimit[1].SEMLimit[3][i].StartFreq = 6.0;
                    lte_limit.SpectLimit[1].SEMLimit[3][i].StopFreq = 6.0;
                    lte_limit.SpectLimit[1].SEMLimit[3][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[1].SEMLimit[3][i].RBW = 1 * MHz;
                }
                
                lte_limit.SpectLimit[1].SEMLimit[4][0].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[4][1].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[4][2].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[4][0].StartFreq = 0.015;
                lte_limit.SpectLimit[1].SEMLimit[4][1].StartFreq = 1.5;
                lte_limit.SpectLimit[1].SEMLimit[4][2].StartFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[4][0].StopFreq = 0.985;
                lte_limit.SpectLimit[1].SEMLimit[4][1].StopFreq = 4.5;
                lte_limit.SpectLimit[1].SEMLimit[4][2].StopFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[4][0].LimitPower = -11.5;
                lte_limit.SpectLimit[1].SEMLimit[4][1].LimitPower = -11.5;
                lte_limit.SpectLimit[1].SEMLimit[4][2].LimitPower = -23.5;
                lte_limit.SpectLimit[1].SEMLimit[4][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[1].SEMLimit[4][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[1].SEMLimit[4][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[1].SEMLimit[4]); i++)
                {
                    lte_limit.SpectLimit[1].SEMLimit[4][i].StartFreq = 6.0;
                    lte_limit.SpectLimit[1].SEMLimit[4][i].StopFreq = 6.0;
                    lte_limit.SpectLimit[1].SEMLimit[4][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[1].SEMLimit[4][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[1].SEMLimit[5][0].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[5][1].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[5][2].State = 1;
                lte_limit.SpectLimit[1].SEMLimit[5][0].StartFreq = 0.015;
                lte_limit.SpectLimit[1].SEMLimit[5][1].StartFreq = 1.5;
                lte_limit.SpectLimit[1].SEMLimit[5][2].StartFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[5][0].StopFreq = 0.985;
                lte_limit.SpectLimit[1].SEMLimit[5][1].StopFreq = 4.5;
                lte_limit.SpectLimit[1].SEMLimit[5][2].StopFreq = 5.5;
                lte_limit.SpectLimit[1].SEMLimit[5][0].LimitPower = -13.5;
                lte_limit.SpectLimit[1].SEMLimit[5][1].LimitPower = -11.5;
                lte_limit.SpectLimit[1].SEMLimit[5][2].LimitPower = -23.5;
                lte_limit.SpectLimit[1].SEMLimit[5][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[1].SEMLimit[5][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[1].SEMLimit[5][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[1].SEMLimit[5]); i++)
                {
                    lte_limit.SpectLimit[1].SEMLimit[5][i].StartFreq = 6.0;
                    lte_limit.SpectLimit[1].SEMLimit[5][i].StopFreq = 6.0;
                    lte_limit.SpectLimit[1].SEMLimit[5][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[1].SEMLimit[5][i].RBW = 1 * MHz;
                }
            }

            // 5MHz
            {
                lte_limit.SpectLimit[2].SEMLimit[0][0].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[0][1].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[0][2].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[0][3].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[0][0].StartFreq = 0.015;
                lte_limit.SpectLimit[2].SEMLimit[0][1].StartFreq = 1.5;
                lte_limit.SpectLimit[2].SEMLimit[0][2].StartFreq = 5.5;
                lte_limit.SpectLimit[2].SEMLimit[0][3].StartFreq = 6.5;
                lte_limit.SpectLimit[2].SEMLimit[0][0].StopFreq = 0.985;
                lte_limit.SpectLimit[2].SEMLimit[0][1].StopFreq = 4.5;
                lte_limit.SpectLimit[2].SEMLimit[0][2].StopFreq = 5.5;
                lte_limit.SpectLimit[2].SEMLimit[0][3].StopFreq = 9.5;
                lte_limit.SpectLimit[2].SEMLimit[0][0].LimitPower = -13.5;
                lte_limit.SpectLimit[2].SEMLimit[0][1].LimitPower = -8.5;
                lte_limit.SpectLimit[2].SEMLimit[0][2].LimitPower = -11.5;
                lte_limit.SpectLimit[2].SEMLimit[0][3].LimitPower = -23.5;
                lte_limit.SpectLimit[2].SEMLimit[0][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[2].SEMLimit[0][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[2].SEMLimit[0][2].RBW = 1 * MHz;
                lte_limit.SpectLimit[2].SEMLimit[0][3].RBW = 1 * MHz;
                for (int i = 4; i < arraySize(lte_limit.SpectLimit[2].SEMLimit[0]); i++)
                {
                    lte_limit.SpectLimit[2].SEMLimit[0][i].StartFreq = 10.0;
                    lte_limit.SpectLimit[2].SEMLimit[0][i].StopFreq = 10.0;
                    lte_limit.SpectLimit[2].SEMLimit[0][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[2].SEMLimit[0][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[2].SEMLimit[1][0].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[1][1].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[1][2].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[1][0].StartFreq = 0.015;
                lte_limit.SpectLimit[2].SEMLimit[1][1].StartFreq = 1.5;
                lte_limit.SpectLimit[2].SEMLimit[1][2].StartFreq = 6.5;
                lte_limit.SpectLimit[2].SEMLimit[1][0].StopFreq = 0.985;
                lte_limit.SpectLimit[2].SEMLimit[1][1].StopFreq = 5.5;
                lte_limit.SpectLimit[2].SEMLimit[1][2].StopFreq = 9.5;
                lte_limit.SpectLimit[2].SEMLimit[1][0].LimitPower = -13.5;
                lte_limit.SpectLimit[2].SEMLimit[1][1].LimitPower = -11.5;
                lte_limit.SpectLimit[2].SEMLimit[1][2].LimitPower = -23.5;
                lte_limit.SpectLimit[2].SEMLimit[1][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[2].SEMLimit[1][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[2].SEMLimit[1][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[2].SEMLimit[1]); i++)
                {
                    lte_limit.SpectLimit[2].SEMLimit[1][i].StartFreq = 10.0;
                    lte_limit.SpectLimit[2].SEMLimit[1][i].StopFreq = 10.0;
                    lte_limit.SpectLimit[2].SEMLimit[1][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[2].SEMLimit[1][i].RBW = 1 * MHz;
                }
                
                lte_limit.SpectLimit[2].SEMLimit[2][0].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[2][1].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[2][2].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[2][0].StartFreq = 0.015;
                lte_limit.SpectLimit[2].SEMLimit[2][1].StartFreq = 1.5;
                lte_limit.SpectLimit[2].SEMLimit[2][2].StartFreq = 5.5;
                lte_limit.SpectLimit[2].SEMLimit[2][0].StopFreq = 0.985;
                lte_limit.SpectLimit[2].SEMLimit[2][1].StopFreq = 4.5;
                lte_limit.SpectLimit[2].SEMLimit[2][2].StopFreq = 9.5;
                lte_limit.SpectLimit[2].SEMLimit[2][0].LimitPower = -13.5;
                lte_limit.SpectLimit[2].SEMLimit[2][1].LimitPower = -11.5;
                lte_limit.SpectLimit[2].SEMLimit[2][2].LimitPower = -23.5;
                lte_limit.SpectLimit[2].SEMLimit[2][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[2].SEMLimit[2][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[2].SEMLimit[2][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[2].SEMLimit[2]); i++)
                {
                    lte_limit.SpectLimit[2].SEMLimit[2][i].StartFreq = 10.0;
                    lte_limit.SpectLimit[2].SEMLimit[2][i].StopFreq = 10.0;
                    lte_limit.SpectLimit[2].SEMLimit[2][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[2].SEMLimit[2][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[2].SEMLimit[3][0].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[3][1].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[3][2].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[3][3].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[3][0].StartFreq = 0.015;
                lte_limit.SpectLimit[2].SEMLimit[3][1].StartFreq = 0.15;
                lte_limit.SpectLimit[2].SEMLimit[3][2].StartFreq = 1.5;
                lte_limit.SpectLimit[2].SEMLimit[3][3].StartFreq = 6.5;
                lte_limit.SpectLimit[2].SEMLimit[3][0].StopFreq = 0.085;
                lte_limit.SpectLimit[2].SEMLimit[3][1].StopFreq = 0.95;
                lte_limit.SpectLimit[2].SEMLimit[3][2].StopFreq = 5.5;
                lte_limit.SpectLimit[2].SEMLimit[3][3].StopFreq = 9.5;
                lte_limit.SpectLimit[2].SEMLimit[3][0].LimitPower = -13.5;
                lte_limit.SpectLimit[2].SEMLimit[3][1].LimitPower = -11.5;
                lte_limit.SpectLimit[2].SEMLimit[3][2].LimitPower = -11.5;
                lte_limit.SpectLimit[2].SEMLimit[3][3].LimitPower = -23.5;
                lte_limit.SpectLimit[2].SEMLimit[3][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[2].SEMLimit[3][1].RBW = 100 * KHz;
                lte_limit.SpectLimit[2].SEMLimit[3][2].RBW = 1 * MHz;
                lte_limit.SpectLimit[2].SEMLimit[3][3].RBW = 1 * MHz;
                for (int i = 4; i < arraySize(lte_limit.SpectLimit[2].SEMLimit[3]); i++)
                {
                    lte_limit.SpectLimit[2].SEMLimit[3][i].StartFreq = 10.0;
                    lte_limit.SpectLimit[2].SEMLimit[3][i].StopFreq = 10.0;
                    lte_limit.SpectLimit[2].SEMLimit[3][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[2].SEMLimit[3][i].RBW = 1 * MHz;
                }
                
                lte_limit.SpectLimit[2].SEMLimit[4][0].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[4][1].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[4][0].StartFreq = 0.025;
                lte_limit.SpectLimit[2].SEMLimit[4][1].StartFreq = 1.5;
                lte_limit.SpectLimit[2].SEMLimit[4][0].StopFreq = 0.975;
                lte_limit.SpectLimit[2].SEMLimit[4][1].StopFreq = 9.5;
                lte_limit.SpectLimit[2].SEMLimit[4][0].LimitPower = -11.5;
                lte_limit.SpectLimit[2].SEMLimit[4][1].LimitPower = -11.5;
                lte_limit.SpectLimit[2].SEMLimit[4][0].RBW = 50 * KHz;
                lte_limit.SpectLimit[2].SEMLimit[4][1].RBW = 1 * MHz;
                for (int i = 2; i < arraySize(lte_limit.SpectLimit[2].SEMLimit[4]); i++)
                {
                    lte_limit.SpectLimit[2].SEMLimit[4][i].StartFreq = 9.5;
                    lte_limit.SpectLimit[2].SEMLimit[4][i].StopFreq = 9.5;
                    lte_limit.SpectLimit[2].SEMLimit[4][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[2].SEMLimit[4][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[2].SEMLimit[5][0].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[5][1].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[5][2].State = 1;
                lte_limit.SpectLimit[2].SEMLimit[5][0].StartFreq = 0.015;
                lte_limit.SpectLimit[2].SEMLimit[5][1].StartFreq = 0.15;
                lte_limit.SpectLimit[2].SEMLimit[5][2].StartFreq = 6.5;
                lte_limit.SpectLimit[2].SEMLimit[5][0].StopFreq = 0.085;
                lte_limit.SpectLimit[2].SEMLimit[5][1].StopFreq = 5.95;
                lte_limit.SpectLimit[2].SEMLimit[5][2].StopFreq = 9.5;
                lte_limit.SpectLimit[2].SEMLimit[5][0].LimitPower = -13.5;
                lte_limit.SpectLimit[2].SEMLimit[5][1].LimitPower = -11.5;
                lte_limit.SpectLimit[2].SEMLimit[5][2].LimitPower = -23.5;
                lte_limit.SpectLimit[2].SEMLimit[5][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[2].SEMLimit[5][1].RBW = 100 * KHz;
                lte_limit.SpectLimit[2].SEMLimit[5][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[2].SEMLimit[5]); i++)
                {
                    lte_limit.SpectLimit[2].SEMLimit[5][i].StartFreq = 9.5;
                    lte_limit.SpectLimit[2].SEMLimit[5][i].StopFreq = 9.5;
                    lte_limit.SpectLimit[2].SEMLimit[5][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[2].SEMLimit[5][i].RBW = 1 * MHz;
                }
            }

            // 10MHz
            {
                lte_limit.SpectLimit[3].SEMLimit[0][0].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[0][1].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[0][2].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[0][3].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[0][0].StartFreq = 0.015;
                lte_limit.SpectLimit[3].SEMLimit[0][1].StartFreq = 1.5;
                lte_limit.SpectLimit[3].SEMLimit[0][2].StartFreq = 5.5;
                lte_limit.SpectLimit[3].SEMLimit[0][3].StartFreq = 10.5;
                lte_limit.SpectLimit[3].SEMLimit[0][0].StopFreq = 0.985;
                lte_limit.SpectLimit[3].SEMLimit[0][1].StopFreq = 4.5;
                lte_limit.SpectLimit[3].SEMLimit[0][2].StopFreq = 9.5;
                lte_limit.SpectLimit[3].SEMLimit[0][3].StopFreq = 14.5;
                lte_limit.SpectLimit[3].SEMLimit[0][0].LimitPower = -16.5;
                lte_limit.SpectLimit[3].SEMLimit[0][1].LimitPower = -8.5;
                lte_limit.SpectLimit[3].SEMLimit[0][2].LimitPower = -11.5;
                lte_limit.SpectLimit[3].SEMLimit[0][3].LimitPower = -23.5;
                lte_limit.SpectLimit[3].SEMLimit[0][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[3].SEMLimit[0][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[3].SEMLimit[0][2].RBW = 1 * MHz;
                lte_limit.SpectLimit[3].SEMLimit[0][3].RBW = 1 * MHz;
                for (int i = 4; i < arraySize(lte_limit.SpectLimit[3].SEMLimit[0]); i++)
                {
                    lte_limit.SpectLimit[3].SEMLimit[0][i].StartFreq = 15.0;
                    lte_limit.SpectLimit[3].SEMLimit[0][i].StopFreq = 15.0;
                    lte_limit.SpectLimit[3].SEMLimit[0][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[3].SEMLimit[0][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[3].SEMLimit[1][0].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[1][1].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[1][2].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[1][0].StartFreq = 0.015;
                lte_limit.SpectLimit[3].SEMLimit[1][1].StartFreq = 1.5;
                lte_limit.SpectLimit[3].SEMLimit[1][2].StartFreq = 10.5;
                lte_limit.SpectLimit[3].SEMLimit[1][0].StopFreq = 0.985;
                lte_limit.SpectLimit[3].SEMLimit[1][1].StopFreq = 9.5;
                lte_limit.SpectLimit[3].SEMLimit[1][2].StopFreq = 14.5;
                lte_limit.SpectLimit[3].SEMLimit[1][0].LimitPower = -16.5;
                lte_limit.SpectLimit[3].SEMLimit[1][1].LimitPower = -11.5;
                lte_limit.SpectLimit[3].SEMLimit[1][2].LimitPower = -23.5;
                lte_limit.SpectLimit[3].SEMLimit[1][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[3].SEMLimit[1][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[3].SEMLimit[1][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[3].SEMLimit[1]); i++)
                {
                    lte_limit.SpectLimit[3].SEMLimit[1][i].StartFreq = 15.0;
                    lte_limit.SpectLimit[3].SEMLimit[1][i].StopFreq = 15.0;
                    lte_limit.SpectLimit[3].SEMLimit[1][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[3].SEMLimit[1][i].RBW = 1 * MHz;
                }
                
                lte_limit.SpectLimit[3].SEMLimit[2][0].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[2][1].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[2][2].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[2][0].StartFreq = 0.015;
                lte_limit.SpectLimit[3].SEMLimit[2][1].StartFreq = 1.5;
                lte_limit.SpectLimit[3].SEMLimit[2][2].StartFreq = 5.5;
                lte_limit.SpectLimit[3].SEMLimit[2][0].StopFreq = 0.985;
                lte_limit.SpectLimit[3].SEMLimit[2][1].StopFreq = 4.5;
                lte_limit.SpectLimit[3].SEMLimit[2][2].StopFreq = 14.5;
                lte_limit.SpectLimit[3].SEMLimit[2][0].LimitPower = -16.5;
                lte_limit.SpectLimit[3].SEMLimit[2][1].LimitPower = -11.5;
                lte_limit.SpectLimit[3].SEMLimit[2][2].LimitPower = -23.5;
                lte_limit.SpectLimit[3].SEMLimit[2][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[3].SEMLimit[2][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[3].SEMLimit[2][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[3].SEMLimit[2]); i++)
                {
                    lte_limit.SpectLimit[3].SEMLimit[2][i].StartFreq = 15.0;
                    lte_limit.SpectLimit[3].SEMLimit[2][i].StopFreq = 15.0;
                    lte_limit.SpectLimit[3].SEMLimit[2][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[3].SEMLimit[2][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[3].SEMLimit[3][0].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[3][1].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[3][2].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[3][3].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[3][0].StartFreq = 0.015;
                lte_limit.SpectLimit[3].SEMLimit[3][1].StartFreq = 0.15;
                lte_limit.SpectLimit[3].SEMLimit[3][2].StartFreq = 1.5;
                lte_limit.SpectLimit[3].SEMLimit[3][3].StartFreq = 10.5;
                lte_limit.SpectLimit[3].SEMLimit[3][0].StopFreq = 0.085;
                lte_limit.SpectLimit[3].SEMLimit[3][1].StopFreq = 0.95;
                lte_limit.SpectLimit[3].SEMLimit[3][2].StopFreq = 9.5;
                lte_limit.SpectLimit[3].SEMLimit[3][3].StopFreq = 14.5;
                lte_limit.SpectLimit[3].SEMLimit[3][0].LimitPower = -16.5;
                lte_limit.SpectLimit[3].SEMLimit[3][1].LimitPower = -11.5;
                lte_limit.SpectLimit[3].SEMLimit[3][2].LimitPower = -11.5;
                lte_limit.SpectLimit[3].SEMLimit[3][3].LimitPower = -23.5;
                lte_limit.SpectLimit[3].SEMLimit[3][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[3].SEMLimit[3][1].RBW = 100 * KHz;
                lte_limit.SpectLimit[3].SEMLimit[3][2].RBW = 1 * MHz;
                lte_limit.SpectLimit[3].SEMLimit[3][3].RBW = 1 * MHz;
                for (int i = 4; i < arraySize(lte_limit.SpectLimit[3].SEMLimit[3]); i++)
                {
                    lte_limit.SpectLimit[3].SEMLimit[3][i].StartFreq = 15.0;
                    lte_limit.SpectLimit[3].SEMLimit[3][i].StopFreq = 15.0;
                    lte_limit.SpectLimit[3].SEMLimit[3][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[3].SEMLimit[3][i].RBW = 1 * MHz;
                }
                
                lte_limit.SpectLimit[3].SEMLimit[4][0].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[4][1].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[4][2].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[4][0].StartFreq = 0.05;
                lte_limit.SpectLimit[3].SEMLimit[4][1].StartFreq = 1.5;
                lte_limit.SpectLimit[3].SEMLimit[4][2].StartFreq = 10.5;
                lte_limit.SpectLimit[3].SEMLimit[4][0].StopFreq = 0.95;
                lte_limit.SpectLimit[3].SEMLimit[4][1].StopFreq = 9.5;
                lte_limit.SpectLimit[3].SEMLimit[4][2].StopFreq = 14.5;
                lte_limit.SpectLimit[3].SEMLimit[4][0].LimitPower = -11.5;
                lte_limit.SpectLimit[3].SEMLimit[4][1].LimitPower = -11.5;
                lte_limit.SpectLimit[3].SEMLimit[4][2].LimitPower = -23.5;
                lte_limit.SpectLimit[3].SEMLimit[4][0].RBW = 100 * KHz;
                lte_limit.SpectLimit[3].SEMLimit[4][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[3].SEMLimit[4][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[3].SEMLimit[4]); i++)
                {
                    lte_limit.SpectLimit[3].SEMLimit[4][i].StartFreq = 14.5;
                    lte_limit.SpectLimit[3].SEMLimit[4][i].StopFreq = 14.5;
                    lte_limit.SpectLimit[3].SEMLimit[4][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[3].SEMLimit[4][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[3].SEMLimit[5][0].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[5][1].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[5][2].State = 1;
                lte_limit.SpectLimit[3].SEMLimit[5][0].StartFreq = 0.015;
                lte_limit.SpectLimit[3].SEMLimit[5][1].StartFreq = 0.15;
                lte_limit.SpectLimit[3].SEMLimit[5][2].StartFreq = 10.5;
                lte_limit.SpectLimit[3].SEMLimit[5][0].StopFreq = 0.085;
                lte_limit.SpectLimit[3].SEMLimit[5][1].StopFreq = 9.95;
                lte_limit.SpectLimit[3].SEMLimit[5][2].StopFreq = 14.5;
                lte_limit.SpectLimit[3].SEMLimit[5][0].LimitPower = -16.5;
                lte_limit.SpectLimit[3].SEMLimit[5][1].LimitPower = -11.5;
                lte_limit.SpectLimit[3].SEMLimit[5][2].LimitPower = -23.5;
                lte_limit.SpectLimit[3].SEMLimit[5][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[3].SEMLimit[5][1].RBW = 100 * KHz;
                lte_limit.SpectLimit[3].SEMLimit[5][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[3].SEMLimit[5]); i++)
                {
                    lte_limit.SpectLimit[3].SEMLimit[5][i].StartFreq = 14.5;
                    lte_limit.SpectLimit[3].SEMLimit[5][i].StopFreq = 14.5;
                    lte_limit.SpectLimit[3].SEMLimit[5][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[3].SEMLimit[5][i].RBW = 1 * MHz;
                }
            }

            // 15MHz
            {
                lte_limit.SpectLimit[4].SEMLimit[0][0].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[0][1].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[0][2].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[0][3].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[0][0].StartFreq = 0.015;
                lte_limit.SpectLimit[4].SEMLimit[0][1].StartFreq = 1.5;
                lte_limit.SpectLimit[4].SEMLimit[0][2].StartFreq = 5.5;
                lte_limit.SpectLimit[4].SEMLimit[0][3].StartFreq = 15.5;
                lte_limit.SpectLimit[4].SEMLimit[0][0].StopFreq = 0.985;
                lte_limit.SpectLimit[4].SEMLimit[0][1].StopFreq = 4.5;
                lte_limit.SpectLimit[4].SEMLimit[0][2].StopFreq = 14.5;
                lte_limit.SpectLimit[4].SEMLimit[0][3].StopFreq = 19.5;
                lte_limit.SpectLimit[4].SEMLimit[0][0].LimitPower = -18.5;
                lte_limit.SpectLimit[4].SEMLimit[0][1].LimitPower = -8.5;
                lte_limit.SpectLimit[4].SEMLimit[0][2].LimitPower = -11.5;
                lte_limit.SpectLimit[4].SEMLimit[0][3].LimitPower = -23.5;
                lte_limit.SpectLimit[4].SEMLimit[0][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[4].SEMLimit[0][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[4].SEMLimit[0][2].RBW = 1 * MHz;
                lte_limit.SpectLimit[4].SEMLimit[0][3].RBW = 1 * MHz;
                for (int i = 4; i < arraySize(lte_limit.SpectLimit[4].SEMLimit[0]); i++)
                {
                    lte_limit.SpectLimit[4].SEMLimit[0][i].StartFreq = 20.0;
                    lte_limit.SpectLimit[4].SEMLimit[0][i].StopFreq = 20.0;
                    lte_limit.SpectLimit[4].SEMLimit[0][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[4].SEMLimit[0][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[4].SEMLimit[1][0].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[1][1].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[1][2].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[1][0].StartFreq = 0.015;
                lte_limit.SpectLimit[4].SEMLimit[1][1].StartFreq = 1.5;
                lte_limit.SpectLimit[4].SEMLimit[1][2].StartFreq = 15.5;
                lte_limit.SpectLimit[4].SEMLimit[1][0].StopFreq = 0.985;
                lte_limit.SpectLimit[4].SEMLimit[1][1].StopFreq = 14.5;
                lte_limit.SpectLimit[4].SEMLimit[1][2].StopFreq = 19.5;
                lte_limit.SpectLimit[4].SEMLimit[1][0].LimitPower = -18.5;
                lte_limit.SpectLimit[4].SEMLimit[1][1].LimitPower = -11.5;
                lte_limit.SpectLimit[4].SEMLimit[1][2].LimitPower = -23.5;
                lte_limit.SpectLimit[4].SEMLimit[1][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[4].SEMLimit[1][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[4].SEMLimit[1][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[4].SEMLimit[1]); i++)
                {
                    lte_limit.SpectLimit[4].SEMLimit[1][i].StartFreq = 20.0;
                    lte_limit.SpectLimit[4].SEMLimit[1][i].StopFreq = 20.0;
                    lte_limit.SpectLimit[4].SEMLimit[1][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[4].SEMLimit[1][i].RBW = 1 * MHz;
                }
                
                lte_limit.SpectLimit[4].SEMLimit[2][0].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[2][1].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[2][2].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[2][0].StartFreq = 0.015;
                lte_limit.SpectLimit[4].SEMLimit[2][1].StartFreq = 1.5;
                lte_limit.SpectLimit[4].SEMLimit[2][2].StartFreq = 5.5;
                lte_limit.SpectLimit[4].SEMLimit[2][0].StopFreq = 0.985;
                lte_limit.SpectLimit[4].SEMLimit[2][1].StopFreq = 4.5;
                lte_limit.SpectLimit[4].SEMLimit[2][2].StopFreq = 19.5;
                lte_limit.SpectLimit[4].SEMLimit[2][0].LimitPower = -18.5;
                lte_limit.SpectLimit[4].SEMLimit[2][1].LimitPower = -11.5;
                lte_limit.SpectLimit[4].SEMLimit[2][2].LimitPower = -23.5;
                lte_limit.SpectLimit[4].SEMLimit[2][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[4].SEMLimit[2][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[4].SEMLimit[2][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[4].SEMLimit[2]); i++)
                {
                    lte_limit.SpectLimit[4].SEMLimit[2][i].StartFreq = 20.0;
                    lte_limit.SpectLimit[4].SEMLimit[2][i].StopFreq = 20.0;
                    lte_limit.SpectLimit[4].SEMLimit[2][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[4].SEMLimit[2][i].RBW = 1 * MHz;
                }

                for (int i = 0; i < arraySize(lte_limit.SpectLimit[4].SEMLimit[3]); i++)
                {
                    lte_limit.SpectLimit[4].SEMLimit[3][i].StartFreq = 20.0;
                    lte_limit.SpectLimit[4].SEMLimit[3][i].StopFreq = 20.0;
                    lte_limit.SpectLimit[4].SEMLimit[3][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[4].SEMLimit[3][i].RBW = 1 * MHz;
                }
                lte_limit.SpectLimit[4].SEMLimit[3][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[4].SEMLimit[3][1].RBW = 100 * KHz;
                
                lte_limit.SpectLimit[4].SEMLimit[4][0].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[4][1].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[4][2].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[4][0].StartFreq = 0.075;
                lte_limit.SpectLimit[4].SEMLimit[4][1].StartFreq = 1.5;
                lte_limit.SpectLimit[4].SEMLimit[4][2].StartFreq = 10.5;
                lte_limit.SpectLimit[4].SEMLimit[4][0].StopFreq = 0.925;
                lte_limit.SpectLimit[4].SEMLimit[4][1].StopFreq = 9.5;
                lte_limit.SpectLimit[4].SEMLimit[4][2].StopFreq = 19.5;
                lte_limit.SpectLimit[4].SEMLimit[4][0].LimitPower = -11.5;
                lte_limit.SpectLimit[4].SEMLimit[4][1].LimitPower = -11.5;
                lte_limit.SpectLimit[4].SEMLimit[4][2].LimitPower = -23.5;
                lte_limit.SpectLimit[4].SEMLimit[4][0].RBW = 150 * KHz;
                lte_limit.SpectLimit[4].SEMLimit[4][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[4].SEMLimit[4][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[4].SEMLimit[4]); i++)
                {
                    lte_limit.SpectLimit[4].SEMLimit[4][i].StartFreq = 19.5;
                    lte_limit.SpectLimit[4].SEMLimit[4][i].StopFreq = 19.5;
                    lte_limit.SpectLimit[4].SEMLimit[4][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[4].SEMLimit[4][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[4].SEMLimit[5][0].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[5][1].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[5][2].State = 1;
                lte_limit.SpectLimit[4].SEMLimit[5][0].StartFreq = 0.015;
                lte_limit.SpectLimit[4].SEMLimit[5][1].StartFreq = 0.15;
                lte_limit.SpectLimit[4].SEMLimit[5][2].StartFreq = 15.5;
                lte_limit.SpectLimit[4].SEMLimit[5][0].StopFreq = 0.085;
                lte_limit.SpectLimit[4].SEMLimit[5][1].StopFreq = 14.95;
                lte_limit.SpectLimit[4].SEMLimit[5][2].StopFreq = 19.5;
                lte_limit.SpectLimit[4].SEMLimit[5][0].LimitPower = -18.5;
                lte_limit.SpectLimit[4].SEMLimit[5][1].LimitPower = -11.5;
                lte_limit.SpectLimit[4].SEMLimit[5][2].LimitPower = -23.5;
                lte_limit.SpectLimit[4].SEMLimit[5][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[4].SEMLimit[5][1].RBW = 100 * KHz;
                lte_limit.SpectLimit[4].SEMLimit[5][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[4].SEMLimit[5]); i++)
                {
                    lte_limit.SpectLimit[4].SEMLimit[5][i].StartFreq = 19.5;
                    lte_limit.SpectLimit[4].SEMLimit[5][i].StopFreq = 19.5;
                    lte_limit.SpectLimit[4].SEMLimit[5][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[4].SEMLimit[5][i].RBW = 1 * MHz;
                }
            }

            // 20MHz
            {
                lte_limit.SpectLimit[5].SEMLimit[0][0].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[0][1].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[0][2].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[0][3].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[0][0].StartFreq = 0.015;
                lte_limit.SpectLimit[5].SEMLimit[0][1].StartFreq = 1.5;
                lte_limit.SpectLimit[5].SEMLimit[0][2].StartFreq = 5.5;
                lte_limit.SpectLimit[5].SEMLimit[0][3].StartFreq = 20.5;
                lte_limit.SpectLimit[5].SEMLimit[0][0].StopFreq = 0.985;
                lte_limit.SpectLimit[5].SEMLimit[0][1].StopFreq = 4.5;
                lte_limit.SpectLimit[5].SEMLimit[0][2].StopFreq = 19.5;
                lte_limit.SpectLimit[5].SEMLimit[0][3].StopFreq = 24.5;
                lte_limit.SpectLimit[5].SEMLimit[0][0].LimitPower = -19.5;
                lte_limit.SpectLimit[5].SEMLimit[0][1].LimitPower = -8.5;
                lte_limit.SpectLimit[5].SEMLimit[0][2].LimitPower = -11.5;
                lte_limit.SpectLimit[5].SEMLimit[0][3].LimitPower = -23.5;
                lte_limit.SpectLimit[5].SEMLimit[0][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[5].SEMLimit[0][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[5].SEMLimit[0][2].RBW = 1 * MHz;
                lte_limit.SpectLimit[5].SEMLimit[0][3].RBW = 1 * MHz;
                for (int i = 4; i < arraySize(lte_limit.SpectLimit[5].SEMLimit[0]); i++)
                {
                    lte_limit.SpectLimit[5].SEMLimit[0][i].StartFreq = 20.0;
                    lte_limit.SpectLimit[5].SEMLimit[0][i].StopFreq = 25.0;
                    lte_limit.SpectLimit[5].SEMLimit[0][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[5].SEMLimit[0][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[5].SEMLimit[1][0].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[1][1].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[1][2].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[1][0].StartFreq = 0.015;
                lte_limit.SpectLimit[5].SEMLimit[1][1].StartFreq = 1.5;
                lte_limit.SpectLimit[5].SEMLimit[1][2].StartFreq = 20.5;
                lte_limit.SpectLimit[5].SEMLimit[1][0].StopFreq = 0.985;
                lte_limit.SpectLimit[5].SEMLimit[1][1].StopFreq = 19.5;
                lte_limit.SpectLimit[5].SEMLimit[1][2].StopFreq = 24.5;
                lte_limit.SpectLimit[5].SEMLimit[1][0].LimitPower = -19.5;
                lte_limit.SpectLimit[5].SEMLimit[1][1].LimitPower = -11.5;
                lte_limit.SpectLimit[5].SEMLimit[1][2].LimitPower = -23.5;
                lte_limit.SpectLimit[5].SEMLimit[1][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[5].SEMLimit[1][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[5].SEMLimit[1][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[5].SEMLimit[1]); i++)
                {
                    lte_limit.SpectLimit[5].SEMLimit[1][i].StartFreq = 20.0;
                    lte_limit.SpectLimit[5].SEMLimit[1][i].StopFreq = 25.0;
                    lte_limit.SpectLimit[5].SEMLimit[1][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[5].SEMLimit[1][i].RBW = 1 * MHz;
                }
                
                lte_limit.SpectLimit[5].SEMLimit[2][0].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[2][1].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[2][2].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[2][0].StartFreq = 0.015;
                lte_limit.SpectLimit[5].SEMLimit[2][1].StartFreq = 1.5;
                lte_limit.SpectLimit[5].SEMLimit[2][2].StartFreq = 5.5;
                lte_limit.SpectLimit[5].SEMLimit[2][0].StopFreq = 0.985;
                lte_limit.SpectLimit[5].SEMLimit[2][1].StopFreq = 4.5;
                lte_limit.SpectLimit[5].SEMLimit[2][2].StopFreq = 24.5;
                lte_limit.SpectLimit[5].SEMLimit[2][0].LimitPower = -19.5;
                lte_limit.SpectLimit[5].SEMLimit[2][1].LimitPower = -11.5;
                lte_limit.SpectLimit[5].SEMLimit[2][2].LimitPower = -23.5;
                lte_limit.SpectLimit[5].SEMLimit[2][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[5].SEMLimit[2][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[5].SEMLimit[2][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[5].SEMLimit[2]); i++)
                {
                    lte_limit.SpectLimit[5].SEMLimit[2][i].StartFreq = 20.0;
                    lte_limit.SpectLimit[5].SEMLimit[2][i].StopFreq = 25.0;
                    lte_limit.SpectLimit[5].SEMLimit[2][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[5].SEMLimit[2][i].RBW = 1 * MHz;
                }

                for (int i = 0; i < arraySize(lte_limit.SpectLimit[5].SEMLimit[3]); i++)
                {
                    lte_limit.SpectLimit[5].SEMLimit[3][i].StartFreq = 20.0;
                    lte_limit.SpectLimit[5].SEMLimit[3][i].StopFreq = 25.0;
                    lte_limit.SpectLimit[5].SEMLimit[3][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[5].SEMLimit[3][i].RBW = 1 * MHz;
                }
                lte_limit.SpectLimit[5].SEMLimit[3][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[5].SEMLimit[3][1].RBW = 100 * KHz;
                lte_limit.SpectLimit[5].SEMLimit[3][0].StartFreq = 20.5;
                lte_limit.SpectLimit[5].SEMLimit[3][1].StartFreq = 20.5;
                lte_limit.SpectLimit[5].SEMLimit[3][2].StartFreq = 20.5;
                lte_limit.SpectLimit[5].SEMLimit[3][3].StartFreq = 20.5;
                
                lte_limit.SpectLimit[5].SEMLimit[4][0].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[4][1].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[4][2].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[4][0].StartFreq = 0.1;
                lte_limit.SpectLimit[5].SEMLimit[4][1].StartFreq = 1.5;
                lte_limit.SpectLimit[5].SEMLimit[4][2].StartFreq = 10.5;
                lte_limit.SpectLimit[5].SEMLimit[4][0].StopFreq = 0.9;
                lte_limit.SpectLimit[5].SEMLimit[4][1].StopFreq = 9.5;
                lte_limit.SpectLimit[5].SEMLimit[4][2].StopFreq = 24.5;
                lte_limit.SpectLimit[5].SEMLimit[4][0].LimitPower = -11.5;
                lte_limit.SpectLimit[5].SEMLimit[4][1].LimitPower = -11.5;
                lte_limit.SpectLimit[5].SEMLimit[4][2].LimitPower = -23.5;
                lte_limit.SpectLimit[5].SEMLimit[4][0].RBW = 200 * KHz;
                lte_limit.SpectLimit[5].SEMLimit[4][1].RBW = 1 * MHz;
                lte_limit.SpectLimit[5].SEMLimit[4][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[5].SEMLimit[4]); i++)
                {
                    lte_limit.SpectLimit[5].SEMLimit[4][i].StartFreq = 24.5;
                    lte_limit.SpectLimit[5].SEMLimit[4][i].StopFreq = 24.5;
                    lte_limit.SpectLimit[5].SEMLimit[4][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[5].SEMLimit[4][i].RBW = 1 * MHz;
                }

                lte_limit.SpectLimit[5].SEMLimit[5][0].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[5][1].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[5][2].State = 1;
                lte_limit.SpectLimit[5].SEMLimit[5][0].StartFreq = 0.015;
                lte_limit.SpectLimit[5].SEMLimit[5][1].StartFreq = 0.15;
                lte_limit.SpectLimit[5].SEMLimit[5][2].StartFreq = 20.5;
                lte_limit.SpectLimit[5].SEMLimit[5][0].StopFreq = 0.085;
                lte_limit.SpectLimit[5].SEMLimit[5][1].StopFreq = 19.95;
                lte_limit.SpectLimit[5].SEMLimit[5][2].StopFreq = 24.5;
                lte_limit.SpectLimit[5].SEMLimit[5][0].LimitPower = -19.5;
                lte_limit.SpectLimit[5].SEMLimit[5][1].LimitPower = -11.5;
                lte_limit.SpectLimit[5].SEMLimit[5][2].LimitPower = -23.5;
                lte_limit.SpectLimit[5].SEMLimit[5][0].RBW = 30 * KHz;
                lte_limit.SpectLimit[5].SEMLimit[5][1].RBW = 100 * KHz;
                lte_limit.SpectLimit[5].SEMLimit[5][2].RBW = 1 * MHz;
                for (int i = 3; i < arraySize(lte_limit.SpectLimit[5].SEMLimit[5]); i++)
                {
                    lte_limit.SpectLimit[5].SEMLimit[5][i].StartFreq = 24.5;
                    lte_limit.SpectLimit[5].SEMLimit[5][i].StopFreq = 24.5;
                    lte_limit.SpectLimit[5].SEMLimit[5][i].LimitPower = -25.0;
                    lte_limit.SpectLimit[5].SEMLimit[5][i].RBW = 1 * MHz;
                }
            }
        }
    }
    
    lte_limit.SEMAddTestTol[0] = 0.3;
    lte_limit.SEMAddTestTol[1] = 0.5;
}

void cellular::alz::LteAlzParam::SetLteListParams(Alg_4G_ListInType &LteList)
{
    memset(&LteList, 0, sizeof(LteList));
    LteList.ChannelBW = 1400000;
    LteList.RBAutoMode = 1;
    LteList.RBNum = 100;
    LteList.Measure.ModStatNum = 20;
    LteList.Measure.SEMStatNum = 20;
    LteList.Measure.ACLRStatNum = 20;
    LteList.Measure.PowerStatNum = 20;
}

void cellular::alz::NrAlzParam::SetNrULParams(Alg_3GPP_AlzULIn5g &nr_ul)
{
    nr_ul.Frequency = 1950;
    nr_ul.NSValue = 1;
    nr_ul.SlotPeriod = 5000;
    nr_ul.TDDPat[0].ULSlotNumber = 8;
    nr_ul.TDDPat[0].ULSymbNumber = 14;

    nr_ul.TDDPat[1].ULSlotNumber = 8;
    nr_ul.TDDPat[1].ULSymbNumber = 14;

    nr_ul.TDDPat[2].ULSlotNumber = 8;
    nr_ul.TDDPat[2].ULSymbNumber = 14;
    
    nr_ul.CellNum = 1;
    for (int i = 0; i < arraySize(nr_ul.Cell); i++)
    {
        nr_ul.Cell[i].ChannelBW = 20 * MHz;
        nr_ul.Cell[i].DmrsTypeAPos = 2;
        nr_ul.Cell[i].UseSCSpacing = 30 * KHz;
        nr_ul.Cell[i].Bwp.SCSpacing = 30 * KHz;
        nr_ul.Cell[i].Bwp.RBNum = 51;
        for (int j = 0; j < arraySize(nr_ul.Cell[i].Bwp.ConfigType); j++)
        {
            nr_ul.Cell[i].Bwp.ConfigType[j] = 1;
        }
        for (int j = 0; j < arraySize(nr_ul.Cell[i].Bwp.MaxLength); j++)
        {
            nr_ul.Cell[i].Bwp.MaxLength[j] = 1;
        }
        for (int j = 0; j < arraySize(nr_ul.Cell[i].Bwp.AdditionalPos); j++)
        {
            nr_ul.Cell[i].Bwp.AdditionalPos[j] = 2;
        }
    }
    for (int i = 0; i < arraySize(nr_ul.Pusch); i++)
    {
        nr_ul.Pusch[i].SymNum = 14;
        nr_ul.Pusch[i].RBDetMode = 1;
        nr_ul.Pusch[i].RBNum = 51; // 与param.NR.UL.Cell[i].Bwp.RBNum初始化一致;
        nr_ul.Pusch[i].DmrsSymbLen = 1;
    }
}

void cellular::alz::NrAlzParam::SetNrDLParams(Alg_3GPP_AlzDLIn5g &nr_dl)
{
    nr_dl.SlotPeriod = 10000;
    nr_dl.TDDPat[0].DLSlotNumber = 8;
    nr_dl.TDDPat[0].DLSymbNumber = 14;
    nr_dl.TDDPat[1].DLSlotNumber = 8;
    nr_dl.TDDPat[1].DLSymbNumber = 14;
    nr_dl.TDDPat[2].DLSlotNumber = 8;
    nr_dl.TDDPat[2].DLSymbNumber = 14;
    nr_dl.CellNum = 1;
    nr_dl.ChanType = 0x50;
    nr_dl.Frequency = 1950; // 单位: MHz
    for (int i = 0; i < arraySize(nr_dl.Cell); i++)
    {
        nr_dl.Cell[i].ChannelBW = 100 * MHz;
        nr_dl.Cell[i].DmrsTypeAPos = 2;
        nr_dl.Cell[i].UseSCSpacing = 30 * KHz;
        nr_dl.Cell[i].Bwp.SCSpacing = 30 * KHz;
        nr_dl.Cell[i].Bwp.RBNum = 273;
        nr_dl.Cell[i].Bwp.Pdsch.ResourceAllocation = 1;
        nr_dl.Cell[i].Bwp.Pdsch.ConfigType = 1;
        nr_dl.Cell[i].Bwp.Pdsch.MaxLength = 2;
        nr_dl.Cell[i].Bwp.Pdsch.RBGSizeType = 1;

        nr_dl.Cell[i].Bwp.Coreset.State = 1;
        nr_dl.Cell[i].Bwp.Coreset.SymbNum = 1;
        nr_dl.Cell[i].Bwp.Coreset.RBNum = 6;
        memset(nr_dl.Cell[i].Bwp.Coreset.BitMap, 1, sizeof(nr_dl.Cell[i].Bwp.Coreset.BitMap));
    }

    for (int i = 0; i < arraySize(nr_dl.Channel); i++)
    {
        nr_dl.Channel[i].Pbch.State = 1;
        nr_dl.Channel[i].Pbch.SCSpacing = 30 * KHz;
        nr_dl.Channel[i].Pbch.RBOffset = (273 - 20) / 2;
        nr_dl.Channel[i].Pbch.SCOffset = 6;
        nr_dl.Channel[i].Pbch.PbchCase = 1;
        nr_dl.Channel[i].Pbch.Length = 4;
        nr_dl.Channel[i].Pbch.Position[0] = 0;
        nr_dl.Channel[i].Pbch.Position[1] = 0;
        nr_dl.Channel[i].Pbch.Position[2] = 1;
        nr_dl.Channel[i].Pbch.Position[3] = 1;
        nr_dl.Channel[i].Pbch.BurstSetPeriod = 10;

        nr_dl.Channel[i].Pdsch.SymbNum = 13;
        nr_dl.Channel[i].Pdsch.SymbOffset = 1;
        nr_dl.Channel[i].Pdsch.RBNum = 273; // 与nr_dl.NR.UL.Cell[i].Bwp.RBNum初始化一致;
        nr_dl.Channel[i].Pdsch.LayerNum = 1;
        nr_dl.Channel[i].Pdsch.AntennaNum = 1;
        nr_dl.Channel[i].Pdsch.CDMGrpWOData = 1;
        nr_dl.Channel[i].Pdsch.DmrsSymbLen = 1;
        for (int j = 0; j < arraySize(nr_dl.Channel[i].Pdsch.DmrsAntPort); j++)
        {
            nr_dl.Channel[i].Pdsch.DmrsAntPort[j] = 1000;
        }
        nr_dl.Channel[i].Pdsch.Codewords = 1;
        nr_dl.Channel[i].Pdsch.Modulate[0] = 2;
        nr_dl.Channel[i].Pdsch.Modulate[1] = 2;
        nr_dl.Channel[i].Pdsch.ChanCodingState = 1;
        nr_dl.Channel[i].Pdsch.Scrambling = 1;
        nr_dl.Channel[i].Pdsch.UeID = 14;
        memset(nr_dl.Channel[i].Pdsch.Bitmap, 1, sizeof(nr_dl.Channel[i].Pdsch.Bitmap));
    }
}

void cellular::alz::NrAlzParam::SetNrLimitParams(Alg_3GPP_LimitIn5g &nr_limit)
{
    // ModLimit
    nr_limit.ModLimit[0].EvmRms.State = 1;
    nr_limit.ModLimit[0].EvmRms.Limit = 30;
    nr_limit.ModLimit[0].EvmPeak.Limit = 60;
    nr_limit.ModLimit[0].MErrRms.Limit = 30;
    nr_limit.ModLimit[0].MErrPeak.Limit = 60;
    nr_limit.ModLimit[0].PhErrRms.Limit = 30;
    nr_limit.ModLimit[0].PhErrPeak.Limit = 60;
    nr_limit.ModLimit[0].FreqErr.State = 1;
    nr_limit.ModLimit[0].FreqErr.Limit = 0.1;
    nr_limit.ModLimit[0].IQOffset.State = 1;
    nr_limit.ModLimit[0].IQOffset.PwrLimit[0] = -27.2;
    nr_limit.ModLimit[0].IQOffset.PwrLimit[1] = -24.2;
    nr_limit.ModLimit[0].IQOffset.PwrLimit[2] = -19.2;
    nr_limit.ModLimit[0].IQOffset.PwrLimit[3] = -9.2;
    nr_limit.ModLimit[0].IBE.State = 1;
    nr_limit.ModLimit[0].IBE.GenMin = -29.2;
    nr_limit.ModLimit[0].IBE.GenEVM = 30;
    nr_limit.ModLimit[0].IBE.GenPwr = -57;
    nr_limit.ModLimit[0].IBE.IQImage[0] = -27.2;
    nr_limit.ModLimit[0].IBE.IQImage[1] = -24.2;
    nr_limit.ModLimit[0].IBE.IQOffsetPwr[0] = -27.2;
    nr_limit.ModLimit[0].IBE.IQOffsetPwr[1] = -24.2;
    nr_limit.ModLimit[0].IBE.IQOffsetPwr[2] = -19.2;
    nr_limit.ModLimit[0].IBE.IQOffsetPwr[3] = -9.2;
    nr_limit.ModLimit[0].SpectFlat.State = 1;
    nr_limit.ModLimit[0].SpectFlat.Range1 = 5.4;
    nr_limit.ModLimit[0].SpectFlat.Range2 = 9.4;
    nr_limit.ModLimit[0].SpectFlat.Max1Min2 = 6.4;
    nr_limit.ModLimit[0].SpectFlat.Max2Min1 = 8.4;
    nr_limit.ModLimit[0].SpectFlat.EdgeFreq = 3;

    nr_limit.ModLimit[1].EvmRms.State = 1;
    nr_limit.ModLimit[1].EvmRms.Limit = 30;
    nr_limit.ModLimit[1].EvmPeak.Limit = 60;
    nr_limit.ModLimit[1].MErrRms.Limit = 30;
    nr_limit.ModLimit[1].MErrPeak.Limit = 60;
    nr_limit.ModLimit[1].PhErrRms.Limit = 30;
    nr_limit.ModLimit[1].PhErrPeak.Limit = 60;
    nr_limit.ModLimit[1].FreqErr.State = 1;
    nr_limit.ModLimit[1].FreqErr.Limit = 0.1;
    nr_limit.ModLimit[1].IQOffset.State = 1;
    nr_limit.ModLimit[1].IQOffset.PwrLimit[0] = -27.2;
    nr_limit.ModLimit[1].IQOffset.PwrLimit[1] = -24.2;
    nr_limit.ModLimit[1].IQOffset.PwrLimit[2] = -19.2;
    nr_limit.ModLimit[1].IQOffset.PwrLimit[3] = -9.2;
    nr_limit.ModLimit[1].IBE.State = 1;
    nr_limit.ModLimit[1].IBE.GenMin = -29.2;
    nr_limit.ModLimit[1].IBE.GenEVM = 30;
    nr_limit.ModLimit[1].IBE.GenPwr = -57;
    nr_limit.ModLimit[1].IBE.IQImage[0] = -27.2;
    nr_limit.ModLimit[1].IBE.IQImage[1] = -24.2;
    nr_limit.ModLimit[1].IBE.IQOffsetPwr[0] = -27.2;
    nr_limit.ModLimit[1].IBE.IQOffsetPwr[1] = -24.2;
    nr_limit.ModLimit[1].IBE.IQOffsetPwr[2] = -19.2;
    nr_limit.ModLimit[1].IBE.IQOffsetPwr[3] = -9.2;
    nr_limit.ModLimit[1].SpectFlat.State = 1;
    nr_limit.ModLimit[1].SpectFlat.Range1 = 7.4;
    nr_limit.ModLimit[1].SpectFlat.Range2 = 15.4;
    nr_limit.ModLimit[1].SpectFlat.Max1Min2 = 6.4;
    nr_limit.ModLimit[1].SpectFlat.Max2Min1 = 8.4;
    nr_limit.ModLimit[1].SpectFlat.EdgeFreq = 3;

    nr_limit.ModLimit[2].EvmRms.State = 1;
    nr_limit.ModLimit[2].EvmRms.Limit = 17.5;
    nr_limit.ModLimit[2].EvmPeak.Limit = 35;
    nr_limit.ModLimit[2].MErrRms.Limit = 17.5;
    nr_limit.ModLimit[2].MErrPeak.Limit = 35;
    nr_limit.ModLimit[2].PhErrRms.Limit = 17.5;
    nr_limit.ModLimit[2].PhErrPeak.Limit = 35;
    nr_limit.ModLimit[2].FreqErr.State = 1;
    nr_limit.ModLimit[2].FreqErr.Limit = 0.1;
    nr_limit.ModLimit[2].IQOffset.State = 1;
    nr_limit.ModLimit[2].IQOffset.PwrLimit[0] = -27.2;
    nr_limit.ModLimit[2].IQOffset.PwrLimit[1] = -24.2;
    nr_limit.ModLimit[2].IQOffset.PwrLimit[2] = -19.2;
    nr_limit.ModLimit[2].IQOffset.PwrLimit[3] = -9.2;
    nr_limit.ModLimit[2].IBE.State = 1;
    nr_limit.ModLimit[2].IBE.GenMin = -29.2;
    nr_limit.ModLimit[2].IBE.GenEVM = 17.5;
    nr_limit.ModLimit[2].IBE.GenPwr = -57;
    nr_limit.ModLimit[2].IBE.IQImage[0] = -27.2;
    nr_limit.ModLimit[2].IBE.IQImage[1] = -24.2;
    nr_limit.ModLimit[2].IBE.IQOffsetPwr[0] = -27.2;
    nr_limit.ModLimit[2].IBE.IQOffsetPwr[1] = -24.2;
    nr_limit.ModLimit[2].IBE.IQOffsetPwr[2] = -19.2;
    nr_limit.ModLimit[2].IBE.IQOffsetPwr[3] = -9.2;
    nr_limit.ModLimit[2].SpectFlat.State = 1;
    nr_limit.ModLimit[2].SpectFlat.Range1 = 5.4;
    nr_limit.ModLimit[2].SpectFlat.Range2 = 9.4;
    nr_limit.ModLimit[2].SpectFlat.Max1Min2 = 6.4;
    nr_limit.ModLimit[2].SpectFlat.Max2Min1 = 8.4;
    nr_limit.ModLimit[2].SpectFlat.EdgeFreq = 3;

    nr_limit.ModLimit[3].EvmRms.State = 1;
    nr_limit.ModLimit[3].EvmRms.Limit = 12.5;
    nr_limit.ModLimit[3].EvmPeak.Limit = 25;
    nr_limit.ModLimit[3].MErrRms.Limit = 12.5;
    nr_limit.ModLimit[3].MErrPeak.Limit = 25;
    nr_limit.ModLimit[3].PhErrRms.Limit = 12.5;
    nr_limit.ModLimit[3].PhErrPeak.Limit = 25;
    nr_limit.ModLimit[3].FreqErr.State = 1;
    nr_limit.ModLimit[3].FreqErr.Limit = 0.1;
    nr_limit.ModLimit[3].IQOffset.State = 1;
    nr_limit.ModLimit[3].IQOffset.PwrLimit[0] = -27.2;
    nr_limit.ModLimit[3].IQOffset.PwrLimit[1] = -24.2;
    nr_limit.ModLimit[3].IQOffset.PwrLimit[2] = -19.2;
    nr_limit.ModLimit[3].IQOffset.PwrLimit[3] = -9.2;
    nr_limit.ModLimit[3].IBE.State = 1;
    nr_limit.ModLimit[3].IBE.GenMin = -29.2;
    nr_limit.ModLimit[3].IBE.GenEVM = 12.5;
    nr_limit.ModLimit[3].IBE.GenPwr = -57;
    nr_limit.ModLimit[3].IBE.IQImage[0] = -27.2;
    nr_limit.ModLimit[3].IBE.IQImage[1] = -24.2;
    nr_limit.ModLimit[3].IBE.IQOffsetPwr[0] = -27.2;
    nr_limit.ModLimit[3].IBE.IQOffsetPwr[1] = -24.2;
    nr_limit.ModLimit[3].IBE.IQOffsetPwr[2] = -19.2;
    nr_limit.ModLimit[3].IBE.IQOffsetPwr[3] = -9.2;
    nr_limit.ModLimit[3].SpectFlat.State = 1;
    nr_limit.ModLimit[3].SpectFlat.Range1 = 5.4;
    nr_limit.ModLimit[3].SpectFlat.Range2 = 9.4;
    nr_limit.ModLimit[3].SpectFlat.Max1Min2 = 6.4;
    nr_limit.ModLimit[3].SpectFlat.Max2Min1 = 8.4;
    nr_limit.ModLimit[3].SpectFlat.EdgeFreq = 3;

    nr_limit.ModLimit[4].EvmRms.State = 1;
    nr_limit.ModLimit[4].EvmRms.Limit = 8;
    nr_limit.ModLimit[4].EvmPeak.Limit = 16;
    nr_limit.ModLimit[4].MErrRms.Limit = 8;
    nr_limit.ModLimit[4].MErrPeak.Limit = 16;
    nr_limit.ModLimit[4].PhErrRms.Limit = 8;
    nr_limit.ModLimit[4].PhErrPeak.Limit = 16;
    nr_limit.ModLimit[4].FreqErr.State = 1;
    nr_limit.ModLimit[4].FreqErr.Limit = 0.1;
    nr_limit.ModLimit[4].IQOffset.State = 1;
    nr_limit.ModLimit[4].IQOffset.PwrLimit[0] = -27.2;
    nr_limit.ModLimit[4].IQOffset.PwrLimit[1] = -24.2;
    nr_limit.ModLimit[4].IQOffset.PwrLimit[2] = -19.2;
    nr_limit.ModLimit[4].IQOffset.PwrLimit[3] = -9.2;
    nr_limit.ModLimit[4].IBE.State = 1;
    nr_limit.ModLimit[4].IBE.GenMin = -29.2;
    nr_limit.ModLimit[4].IBE.GenEVM = 8;
    nr_limit.ModLimit[4].IBE.GenPwr = -57;
    nr_limit.ModLimit[4].IBE.IQImage[0] = -27.2;
    nr_limit.ModLimit[4].IBE.IQImage[1] = -24.2;
    nr_limit.ModLimit[4].IBE.IQOffsetPwr[0] = -27.2;
    nr_limit.ModLimit[4].IBE.IQOffsetPwr[1] = -24.2;
    nr_limit.ModLimit[4].IBE.IQOffsetPwr[2] = -19.2;
    nr_limit.ModLimit[4].IBE.IQOffsetPwr[3] = -9.2;
    nr_limit.ModLimit[4].SpectFlat.State = 1;
    nr_limit.ModLimit[4].SpectFlat.Range1 = 5.4;
    nr_limit.ModLimit[4].SpectFlat.Range2 = 9.4;
    nr_limit.ModLimit[4].SpectFlat.Max1Min2 = 6.4;
    nr_limit.ModLimit[4].SpectFlat.Max2Min1 = 8.4;
    nr_limit.ModLimit[4].SpectFlat.EdgeFreq = 3;

    nr_limit.ModLimit[5].EvmRms.State = 1;
    nr_limit.ModLimit[5].EvmRms.Limit = 3.5;
    nr_limit.ModLimit[5].EvmPeak.Limit = 7;
    nr_limit.ModLimit[5].MErrRms.Limit = 3.5;
    nr_limit.ModLimit[5].MErrPeak.Limit = 7;
    nr_limit.ModLimit[5].PhErrRms.Limit = 3.5;
    nr_limit.ModLimit[5].PhErrPeak.Limit = 7;
    nr_limit.ModLimit[5].FreqErr.State = 1;
    nr_limit.ModLimit[5].FreqErr.Limit = 0.1;
    nr_limit.ModLimit[5].IQOffset.State = 1;
    nr_limit.ModLimit[5].IQOffset.PwrLimit[0] = -27.2;
    nr_limit.ModLimit[5].IQOffset.PwrLimit[1] = -24.2;
    nr_limit.ModLimit[5].IQOffset.PwrLimit[2] = -19.2;
    nr_limit.ModLimit[5].IQOffset.PwrLimit[3] = -9.2;
    nr_limit.ModLimit[5].IBE.State = 1;
    nr_limit.ModLimit[5].IBE.GenMin = -29.2;
    nr_limit.ModLimit[5].IBE.GenEVM = 3.5;
    nr_limit.ModLimit[5].IBE.GenPwr = -57;
    nr_limit.ModLimit[5].IBE.IQImage[0] = -27.2;
    nr_limit.ModLimit[5].IBE.IQImage[1] = -24.2;
    nr_limit.ModLimit[5].IBE.IQOffsetPwr[0] = -27.2;
    nr_limit.ModLimit[5].IBE.IQOffsetPwr[1] = -24.2;
    nr_limit.ModLimit[5].IBE.IQOffsetPwr[2] = -19.2;
    nr_limit.ModLimit[5].IBE.IQOffsetPwr[3] = -9.2;
    nr_limit.ModLimit[5].SpectFlat.State = 1;
    nr_limit.ModLimit[5].SpectFlat.Range1 = 5.4;
    nr_limit.ModLimit[5].SpectFlat.Range2 = 9.4;
    nr_limit.ModLimit[5].SpectFlat.Max1Min2 = 6.4;
    nr_limit.ModLimit[5].SpectFlat.Max2Min1 = 8.4;
    nr_limit.ModLimit[5].SpectFlat.EdgeFreq = 3;

    // SpectLimit
    nr_limit.SpectLimit[0].OBWLimit.Limit = 5;
    nr_limit.SpectLimit[1].OBWLimit.Limit = 10;
    nr_limit.SpectLimit[2].OBWLimit.Limit = 15;
    nr_limit.SpectLimit[3].OBWLimit.Limit = 20;
    nr_limit.SpectLimit[4].OBWLimit.Limit = 25;
    nr_limit.SpectLimit[5].OBWLimit.Limit = 30;
    nr_limit.SpectLimit[6].OBWLimit.Limit = 40;
    nr_limit.SpectLimit[7].OBWLimit.Limit = 50;
    nr_limit.SpectLimit[8].OBWLimit.Limit = 60;
    nr_limit.SpectLimit[9].OBWLimit.Limit = 70;
    nr_limit.SpectLimit[10].OBWLimit.Limit = 80;
    nr_limit.SpectLimit[11].OBWLimit.Limit = 90;
    nr_limit.SpectLimit[12].OBWLimit.Limit = 100;

    for (int i = 0; i < arraySize(nr_limit.SpectLimit); i++)
    {
        nr_limit.SpectLimit[i].OBWLimit.State = 1;
        nr_limit.SpectLimit[i].UtraLimit[0].RelState = 1;
        nr_limit.SpectLimit[i].UtraLimit[0].RelLimit = 33;
        nr_limit.SpectLimit[i].UtraLimit[0].AbsState = 1;
        nr_limit.SpectLimit[i].UtraLimit[0].AbsPwr = -50;
        nr_limit.SpectLimit[i].UtraLimit[1].RelLimit = 36;
        nr_limit.SpectLimit[i].UtraLimit[1].AbsPwr = -50;
        nr_limit.SpectLimit[i].UtraLimit[1].RelState = 1;
        nr_limit.SpectLimit[i].UtraLimit[1].AbsState = 1;
        nr_limit.SpectLimit[i].NRLimit.RelState = 1;
        nr_limit.SpectLimit[i].NRLimit.RelLimit = 30;
        nr_limit.SpectLimit[i].NRLimit.AbsState = 1;
        nr_limit.SpectLimit[i].NRLimit.AbsPwr = -50;
    }
    
    nr_limit.SEMAddTestTol[0] = 1.5;
    nr_limit.SEMAddTestTol[1] = 1.8;
    nr_limit.SEMAddTestTol[2] = 1.8;
    
    nr_limit.ACLRAddTestTol[0] = 0.8;
    nr_limit.ACLRAddTestTol[1] = 1;

    nr_limit.PowerLimit.State = 1;
    nr_limit.PowerLimit.OffPower = -50;
    nr_limit.PowerLimit.TestTol[0] = 1.5;
    nr_limit.PowerLimit.TestTol[1] = 1.8;
    nr_limit.PowerLimit.TestTol[2] = 1.7;
    nr_limit.PowerLimit.TestTol[3] = 1.8;
}

void cellular::alz::NbiotAlzParam::SetNbiotULParams(Alg_3GPP_AlzULInNBIOT &nbiot_ul)
{
    nbiot_ul.ChannelBW = 200 * KHz;
    nbiot_ul.ChanType = ALG_NBIOT_NPUSCH;
    nbiot_ul.Npusch.Format = 1;
    nbiot_ul.Npusch.SCSpacing = 15 * KHz;
    nbiot_ul.Npusch.Repetitions = 1;
    nbiot_ul.Npusch.RUs = 1;
    nbiot_ul.Npusch.SubcarrierNum = 1;
    nbiot_ul.Npusch.Modulate = 1;
    nbiot_ul.Npusch.Scrambling = 1;
}

void cellular::alz::NbiotAlzParam::SetNbiotDLParams(Alg_3GPP_AlzDLInNBIOT &nbiot_dl)
{
    nbiot_dl.ChannelBW = 200 * KHz;
    nbiot_dl.LTEAntennaNum = 1;
    nbiot_dl.NBAntennaNum = 1;
    nbiot_dl.ChanType = ALG_NBIOT_NPDSCH;
    nbiot_dl.Npdsch.NSF = 1;
    nbiot_dl.Npdsch.Repetitions = 1;
    nbiot_dl.Npdsch.StartSubfrm = 6;
    nbiot_dl.Npdsch.ChanCodingState = 1;
}

void cellular::alz::NbiotAlzParam::SetNbiotLimitParams(Alg_3GPP_LimitInNBIOT &nbiot_limit)
{
    nbiot_limit.EvmRms.State = 1;
    nbiot_limit.EvmRms.Limit = 17.5;
    nbiot_limit.EvmPeak.Limit = 35;
    nbiot_limit.MErrRms.Limit = 17.5;
    nbiot_limit.MErrPeak.Limit = 35;
    nbiot_limit.PhErrRms.Limit = 17.5;
    nbiot_limit.PhErrPeak.Limit = 35;
    nbiot_limit.FreqErrLow.State = 1;
    nbiot_limit.FreqErrLow.Limit = 0.2;
    nbiot_limit.FreqErrHigh.State = 1;
    nbiot_limit.FreqErrHigh.Limit = 0.2;
    nbiot_limit.IQOffset.State = 1;
    nbiot_limit.IQOffset.PwrLimit[0] = -24.2;
    nbiot_limit.IQOffset.PwrLimit[1] = -19.2;
    nbiot_limit.IQOffset.PwrLimit[2] = -9.2;
    nbiot_limit.IBE.State = 1;
    nbiot_limit.IBE.GenMin = -30;
    nbiot_limit.IBE.GenPwr = -57;
    nbiot_limit.IBE.IQImage[0] = -24.2;
    nbiot_limit.IBE.IQOffsetPwr[0] = -24.2;
    nbiot_limit.IBE.IQOffsetPwr[1] = -19.2;
    nbiot_limit.IBE.IQOffsetPwr[2] = -9.2;
    nbiot_limit.OBWLimit.State = 1;
    nbiot_limit.OBWLimit.Limit = 200; // 单位: KHz
    for (int i = 0; i < 4; i++)
    {
        nbiot_limit.SEMLimit[i].State = 1;
    }
    for (int i = 0; i < arraySize(nbiot_limit.SEMLimit); i++)
    {
        nbiot_limit.SEMLimit[i].StartFreq = 0.015;
        nbiot_limit.SEMLimit[i].StopFreq = 0.085;
        nbiot_limit.SEMLimit[i].StartPower = 22.9;
        nbiot_limit.SEMLimit[i].StopPower = 1.1;
    }
    nbiot_limit.GSM.RelState = 1;
    nbiot_limit.GSM.RelLimit = 19.2;
    nbiot_limit.GSM.AbsState = 1;
    nbiot_limit.GSM.AbsPwr = -50;

    nbiot_limit.UTRA.RelState = 1;
    nbiot_limit.UTRA.RelLimit = 36.2;
    nbiot_limit.UTRA.AbsState = 1;
    nbiot_limit.UTRA.AbsPwr = -50;
}
